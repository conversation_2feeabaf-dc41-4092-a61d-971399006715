package com.stt.android.domain.achievements

import androidx.annotation.StringRes
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.workouts.domain.R
import com.suunto.algorithms.data.Length
import com.suunto.algorithms.data.Length.Companion.kilometers

data class RecordItem(
    val personalRecordType: PersonalRecordType,
    val workoutHeader: WorkoutHeader?
)

data class RecordItemDomain(
    val personalRecordType: PersonalRecordType,
    val value: String?,
    val timestamp: String?,
    val workoutHeader: WorkoutHeader?
)

enum class LongDistanceRunningRange(val distanceRange: ClosedRange<Length>) {
    RECORD_5_KM(5.kilometers.rangeTo(6.kilometers)),
    RECORD_10_KM(10.kilometers.rangeTo(11.kilometers)),
    RECORD_HALF_MARATHON_KM(20.kilometers.rangeTo(22.kilometers)),
    RECORD_FULL_MARATHON_KM(41.kilometers.rangeTo(43.kilometers)),
}

enum class PersonalRecordType(@StringRes val titleId: Int, val recordRemoteType: String) {
    RUNNING_5_KM(R.string.fastest_5_KM, "FiveKilometers"),
    RUNNING_10_KM(R.string.fastest_10_KM, "TenKilometers"),
    MARATHON_HALF_MARATHON_KM(R.string.fastest_half_marathon_KM, "HalfMarathon"),
    MARATHON_FULL_MARATHON_KM(R.string.fastest_full_marathon_KM, "FullMarathon"),
    LONGEST_DISTANCE(R.string.longest_distance, "LongestDistance"),
    FASTEST_PACE(R.string.fastest_pace, "FastestPace"),
    HIGHEST_CLIMB(R.string.highest_climb, "HighestClimb"),
    HIGHEST_ALTITUDE(R.string.highest_altitude, "HighestAltitude"),
    HIGHEST_SPEED(R.string.highest_speed, "HighestSpeed"),
}
