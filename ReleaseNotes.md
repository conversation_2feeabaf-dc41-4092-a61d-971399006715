#### 6.2.0 (2025-08-22)
 - Fixes
  - 191758 No Add to route pop-up shows after long pressing route in create route view[dev] by author:JiangXianming2
  - 191801 192218 192224 191451 AI Coach survey summary fixes by author:<PERSON>
  - Initial popular route sync by author:dt2dev
  - 192322 192288 191758 some drag issues are related to the marker by author:JessieeLi
  - Route list not show when location permission not allowed by author:moon
  - 192220 192030 191040 191047 some bugs of badges [develop] by author:liangkun3892
  - Fix crash in recovery state calculation [Develop] by author:wang<PERSON><PERSON><PERSON>-suunto
  - 191835 191998 192293 192267 Fix TP bugs by author:wang<PERSON><PERSON>un-suunto
  - 192262 The tab name is Activities instead of Activity in training by author:Jiang<PERSON>ianming2
  - 190637 AI planner - Rounded bar chart by author:<PERSON>Gt capabilities by author:Jiang<PERSON>ianming2

 - Features
  - 192361 Heart belt setting UI 1/x by author:Yazhou66
  - 192212 add sleep details screen chart changed analytics by author:shuaiwenchen
  - 192408 Dailyhealth events by author:<PERSON><PERSON><PERSON>ming2
  - 187306 Support offline maps on mobile, 15/x by author:<PERSON><PERSON> (<PERSON>)
  - Change Posts to Media by author:wangxiaochun-suunto
  - 192159 add leave home screen analytics by author:shuaiwenchen
  - 192392 Sync pop routes to remote by author:dt2dev
  - 192150 Follow request + Privacy setting optimization, 2/x by author:wangxiaochun-suunto
  - 191851-LeaveWidgetsDetailPage-analytics, 3/x by author:shuaiwenchen
  - 187306 Support offline maps on mobile, 14/x by author:Xizhi Zhu (Steven)
  - 192332 Training and statistics click events by author:JiangXianming2
  - 192150  Follow request + Privacy setting optimization, 1/x by author:wangxiaochun-suunto
  - 190487 191119 Planned workout details design update by author:Sami Rajala
  - 192216 Training and statistics browsing duration events by author:JiangXianming2
  - 192126 Implement heart belt connection UI 1/x by author:Yazhou66
  - 191502 Support paging loading of workouts, 3/x  by author:wangxiaochun-suunto
  - 192112 add widget detail page button click analytics by author:shuaiwenchen
  - 192068 Implement find headphone protocol for SU10 by author:Yazhou66
  - 187306 Support offline maps on mobile, 13/x by author:Xizhi Zhu (Steven)
  - 192271 Sync popular routes to watch by author:dt2dev
  - 187306 Support offline maps on mobile, 12/x by author:Xizhi Zhu (Steven)
  - 192135 Training and statistics exposure events by author:JiangXianming2
  - 191851 leave widgets detail page analytics, 2/x by author:shuaiwenchen
  - 187306 Support offline maps on mobile, 11/x by author:Xizhi Zhu (Steven)
  - 192029 Implement metronome spatial audio immersive mode protocol for SU10 by author:Yazhou66
  - 191502 Support paging loading of workouts, 2/x by author:wangxiaochun-suunto
  - 191851 leave widgets detail page analytics by author:shuaiwenchen
  - 187306 Support offline maps on mobile, 10/x by author:Xizhi Zhu (Steven)

 - Technical
  - Support hebrew by author:JiangXianming2
  - Add pre-push for windows by author:JiangXianming2
  - Update Kotlin to 2.2.10 and KSP to 2.2.10-2.0.2 by author:Xizhi Zhu (Steven)
  - Update git hooks to run Lint against debug build by author:Xizhi Zhu (Steven)
  - Update mapbox and switch to 16kbit aligned deps by author:Sami Rajala
  - Added script to install git hooks by author:Yazhou66

 - Translations
  - Week 34, 2025, add Hebrew strings by author:JiangXianming2
