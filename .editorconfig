# EditorConfig is awesome: http://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
# Prevent Git (and Github) "No new line at end of file" warning
[*]
insert_final_newline = true

# 4 space indentation for Java and Kotlin source files
[*.{java,kt}]
indent_style = space
indent_size = 4
ktlint_code_style = android_studio

[*.{kt,kts}]
compose_allowed_composition_locals = LocalIsEdit,LocalPlayer,LocalIsMaterial3,LocalHyperLinkHandler,LocalExtraPalette,LocalExtraShapes,LocalSpacing,LocalExtraTextStyles,LocalElevation,LocalIconSizes,LocalShowBottomSheet,LocalTrainingHubDimens,LocalTrainingHubColors,LocalHomeWidgetScale
ktlint_compose_modifier-composed-check = disabled # Should investigate if we can replace calls to composed()
ktlint_compose_parameter-naming = disabled # Requires renaming a lot of params to enable
ktlint_compose_lambda-param-in-effect = disabled # Requires refactoring/suppressing a few features

# https://pinterest.github.io/ktlint/latest/rules/standard/
ktlint_standard_function-signature = disabled
ktlint_standard_trailing-comma-on-call-site = disabled
ktlint_standard_trailing-comma-on-declaration-site = disabled
ktlint_standard_import-ordering = disabled
ktlint_standard_no-empty-first-line-in-class-body = disabled
ktlint_standard_max-line-length = disabled
ktlint_standard_function-expression-body = disabled
ktlint_standard_property-naming = disabled
ktlint_standard_class-signature = disabled
ktlint_standard_function-naming = disabled
ktlint_standard_wrapping = disabled
ktlint_standard_annotation = disabled
ktlint_standard_no-multi-spaces = disabled
ktlint_standard_value-parameter-comment = disabled
ktlint_standard_backing-property-naming = disabled
ktlint_standard_multiline-if-else = disabled
ktlint_standard_no-trailing-spaces = disabled
ktlint_standard_comment-wrapping = disabled
ktlint_standard_no-unit-return = disabled # Gets confused by our own enum class Unit

# Disable all compose rules from divemodecustomization folder
[divecustomization/**.{kt,kts}]
ktlint_compose_lambda-param-event-trailing = disabled
ktlint_compose_mutable-state-autoboxing = disabled
