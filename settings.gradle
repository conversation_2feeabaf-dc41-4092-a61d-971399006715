pluginManagement {
    includeBuild("build-logic")
    repositories {
        mavenLocal()
        gradlePluginPortal()
        mavenCentral()
        google()
        maven { url = 'https://oss.sonatype.org/content/repositories/snapshots' }
    }
}
include ":utils"
include ":MdsLibrary"
include ":infoModel"
include ':SCSampleApp'
include ":SuuntoConnectivity"
include ":graphlib"
include ':appbase'
include ":STTAndroidCore"
include ':STTAndroidWear'
include ":location"
include ":bluetooth"
include ":remote"
include ":persistence"
include ":datasource"
include ":domain"
include ":timeline"
include ":remotebase"
include ":domainbase"
include ":datasourcebase"
include ":testutils"
include ":croplib"

include ":app"

include "maps"
project(':maps').projectDir = file('maps/base')
include "mapsProviderGoogle"
project(':mapsProviderGoogle').projectDir = file('maps/mapsProviderGoogle')
include "mapsProviderMapbox"
project(':mapsProviderMapbox').projectDir = file('maps/mapsProviderMapbox')
include ':mapsProviderAmap'
project(':mapsProviderAmap').projectDir = file('maps/mapsProviderAmap')

// :MapsSampleApp have been removed to speed up Gradle configure phase
// if you're working on these, you should manually add them and remember not to commit this file afterwards
// include "MapsSampleApp"
// project(':MapsSampleApp').projectDir = file('maps/MapsSampleApp')

include "analytics"
project(':analytics').projectDir = file('analytics/base')
include "analyticsremote"
project(':analyticsremote').projectDir = file('analytics/remote')
include "analyticschina"
project(':analyticschina').projectDir = file('analytics/china')

include "workoutsremote"
project(':workoutsremote').projectDir = file('workouts/remote')
include "workoutsdomain"
project(':workoutsdomain').projectDir = file('workouts/domain')
include "workoutsdatasource"
project(':workoutsdatasource').projectDir = file('workouts/datasource')

include "userremote"
project(':userremote').projectDir = file('user/remote')
include "userdomain"
project(':userdomain').projectDir = file('user/domain')
include "userdatasource"
project(':userdatasource').projectDir = file('user/datasource')

include 'sessionremote'
project(':sessionremote').projectDir = file('session/remote')
include 'sessiondomain'
project(':sessiondomain').projectDir = file('session/domain')
include 'sessiondatasource'
project(':sessiondatasource').projectDir = file('session/datasource')
include 'session'
project(':session').projectDir = file('session/session')

include 'devicebase'
project(':devicebase').projectDir = file('device/base')
include 'deviceonboarding'
project(':deviceonboarding').projectDir = file('device/onboarding')

include ':explore'
include 'exploredomain'
project(':exploredomain').projectDir = file('explore/domain')
include 'exploredatasource'
project(':exploredatasource').projectDir = file('explore/datasource')
include 'exploreremote'
project(':exploreremote').projectDir = file('explore/remote')

include ':workoutdetails'
include ':workoutplanner'

include ':divecustomization'
include 'divecustomizationdomain'
project(':divecustomizationdomain').projectDir = file('divecustomization/domain')

include ':diary'
include ':diarydomain'
project(':diarydomain').projectDir = file('diary/domain')
include ':elevationdata'
//include ':benchmark'
include ':diveplanner'
include 'diveplannerdomain'
project(':diveplannerdomain').projectDir = file('diveplanner/domain')

include ':composeui'
project(':composeui').projectDir = file('composeui')

include ':questionnaire'
project(':questionnaire').projectDir = file('questionnaire')

include ':suuntoplusui'
include ':suuntoplusstore'

include ':HeadsetBesOtaLibrary'
project(':HeadsetBesOtaLibrary').projectDir = file('headset/BesOtaLibrary')
include ':HeadsetJLOtaSdkLibrary'
project(':HeadsetJLOtaSdkLibrary').projectDir = file('headset/JLOtaLibrary/Sdk')
include ':HeadsetJLOtaCoreLibrary'
project(':HeadsetJLOtaCoreLibrary').projectDir = file('headset/JLOtaLibrary/Core')
include ':HeadsetSoaLibrary'
project(':HeadsetSoaLibrary').projectDir = file('headset/SoaLibrary')
include ':headset'
project(':headset').projectDir = file('headset/headset')

include ':watchdebug'
include ':divetrack'
include ':musicmanager'

include 'menstrualcycleonboarding'
project(':menstrualcycleonboarding').projectDir = file('menstrualcycle/onboarding')
include 'menstrualcycledomain'
project(':menstrualcycledomain').projectDir = file('menstrualcycle/domain')
include 'menstrualcycledatasource'
project(':menstrualcycledatasource').projectDir = file('menstrualcycle/datasource')
include 'menstrualcycleremote'
project(':menstrualcycleremote').projectDir = file('menstrualcycle/remote')

include ':sportmode'
include ':watchofflinemusic'
include ':eventtracking'

include 'sharingplatform'
project(':sharingplatform').projectDir = file('sharing/platform')
include ':SharingRedNoteLibrary'
project(':SharingRedNoteLibrary').projectDir = file('sharing/rednotelibrary')

include 'chartapi'
project(':chartapi').projectDir = file('chart/api')
include 'chartimpl'
project(':chartimpl').projectDir = file('chart/impl')

include 'tutorialapi'
project(':tutorialapi').projectDir = file('tutorial/api')
include 'tutorialimpl'
project(':tutorialimpl').projectDir = file('tutorial/impl')
include 'smartdevice'
project(':smartdevice').projectDir = file('headset/smartdevice')
include 'heartbelt'
project(":heartbelt").projectDir = file("headset/heartbelt")
