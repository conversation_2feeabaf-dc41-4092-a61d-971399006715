package com.stt.android.coroutines

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.currentTime
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.Test

class FlowThrottleExtensionsKtTest {

    @Test
    fun `throttleLatest should emit the first item immediately`() = runTest {
        val startTime = currentTime
        val result = flowOf(1)
            .throttleLatest(100)
            .first()

        assertThat(result).isEqualTo(1)
        assertThat(currentTime).isEqualTo(startTime)
    }

    @Test
    fun `throttleLatest should emit the next item after timeout immediately`() = runTest {
        val startTime = currentTime
        val result = flow {
            emit(1)
            delay(150)
            emit(2)
        }
            .throttleLatest(100)
            .toList()

        assertThat(result).isEqualTo(listOf(1, 2))
        assertThat(currentTime).isEqualTo(startTime + 150)
    }

    @Test
    fun `throttleLatest should emit only the most recent item after timeout`() = runTest {
        val startTime = currentTime
        val result = flow {
            emit(1)
            delay(10)
            emit(2)
            delay(10)
            emit(3)
            delay(60)
            emit(4)
            delay(50)
            emit(5)
            delay(100)
        }
            .throttleLatest(100)
            .map { it to currentTime - startTime }
            .toList()

        assertThat(result.size).isEqualTo(3)

        val (value1, delay1) = result[0]
        assertThat(value1).isEqualTo(1)
        assertThat(delay1).isEqualTo(0)

        val (value2, delay2) = result[1]
        assertThat(value2).isEqualTo(4)
        assertThat(delay2).isEqualTo(100)

        val (value3, delay3) = result[2]
        assertThat(value3).isEqualTo(5)
        assertThat(delay3).isEqualTo(200)
    }

    @Test
    fun `throttleLatest should not emit the last held back item when the flow completes`() = runTest {
        val startTime = currentTime
        val result = flow {
            emit(1)
            delay(50)
            emit(2)
        }
            .throttleLatest(100)
            .toList()

        assertThat(result).isEqualTo(listOf(1))
        assertThat(currentTime).isEqualTo(startTime + 50)
    }

    @Test
    fun `throttleLatest should emit the same value from upstream again`() = runTest {
        val result = flow {
            emit(Unit)
            delay(50)
            emit(Unit)
            delay(100)
        }
            .throttleLatest(100)
            .toList()

        assertThat(result).isEqualTo(listOf(Unit, Unit))
    }
}
