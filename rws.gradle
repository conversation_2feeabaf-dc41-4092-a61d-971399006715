import com.stt.gradle.translation.ApplyTranslationsTask
import com.stt.gradle.translation.CollectStringsForTranslationsTask

def remoteRwsPath = '/Android'
def localRwsPath = "${getLayout().getBuildDirectory().get()}/rws"
def translatedDir = 'Translated'
def referenceDir = "To-be-translated"

tasks.register('clearLocalRwsDirectories', Delete) {
    if (file(localRwsPath).exists()) {
        delete file(localRwsPath).listFiles()
    }
}

tasks.register('pullTranslationsFromRws') {
    dependsOn clearLocalRwsDirectories
    doFirst {
        mkdir(localRwsPath)
        ssh.run {
            session(remotes.rws) {
                get from: "$remoteRwsPath/$translatedDir", into: localRwsPath
            }
        }
    }
}

tasks.register('applyRwsTranslations', ApplyTranslationsTask) {
    dependsOn pullTranslationsFromRws
    performGitAdd.set(true)
    translatedFiles.set(fileTree("$localRwsPath/$translatedDir"))
    reference.set(fileTree(".").matching {
        include("**/values/strings.xml")
        include("**/values/voice_feedback.xml")
        exclude("maps/MapsSampleApp/**")
        exclude("SCSampleApp/**")
        exclude("app/build/**")
    })
}

tasks.register('commitTranslationsFromRws', Exec) {
    dependsOn applyRwsTranslations
    description "Commit version change"
    commandLine 'git', 'commit', '-m', "Update translations (${new Date().format('yyyy-MM-dd')})"
}

tasks.register('collectReferenceStringsForRws', CollectStringsForTranslationsTask) {
    dependsOn dependsOn: clearLocalRwsDirectories
    input.set(fileTree(".").matching {
        include("**/values/strings.xml")
        include("**/values/voice_feedback.xml")
        exclude("maps/MapsSampleApp/**")
        exclude("SCSampleApp/**")
        exclude("app/build/**")
    })
    doFirst {
        mkdir("$localRwsPath/$referenceDir")
    }
    if (!project.hasProperty("skipWritingOutput")) {
        output.set(file("$localRwsPath/$referenceDir/strings.xml"))
    }
}

project.ext {
    mergeTranslationsTasks = []
}

// Merge translation tasks for each language
// The merged translations are not needed in the weekly localization process, but may be required
// in ad hoc tasks related to rws translation memory synchronization, etc.
rootProject.ext.supportedLocales.each { lang ->
    def taskName = "collectReferenceStringsForRws$lang"
    mergeTranslationsTasks.add(taskName)
    tasks.register(taskName, CollectStringsForTranslationsTask) {
        input.set(fileTree(".").matching {
            include("**/values-$lang/strings.xml")
            include("**/values-$lang/voice_feedback.xml")
            exclude("maps/MapsSampleApp/**")
            exclude("SCSampleApp/**")
            exclude("app/build/**")
        })
        output.set(file("$localRwsPath/$referenceDir/android-merged-$lang-strings.xml"))
    }
}

tasks.register('mergeCurrentTranslationsToOneXmlPerLanguage') {
    dependsOn dependsOn: collectReferenceStringsForRws
    dependsOn dependsOn: mergeTranslationsTasks

    doLast {
        ant.move(file: "$localRwsPath/$referenceDir/strings.xml",
            tofile: "$localRwsPath/$referenceDir/android-merged-strings.xml")
    }
}

remotes {
    rws {
        host = '**************'
        port = 22
        user = 'suunto'
        identity = file("id_rsa_rws")
    }
}

ssh.settings {
    knownHosts = file('.ssh_rws_known_hosts')
}

tasks.register('pushReferenceStringsToRws') {
    dependsOn collectReferenceStringsForRws
    doLast {
        ssh.run {
            session(remotes.rws) {
                try {
                    remove("$remoteRwsPath/$referenceDir")
                } catch (Exception ignored) {
                }

                put from: "$localRwsPath/$referenceDir", into: remoteRwsPath
                println("Succesfully uploaded content from $localRwsPath/$referenceDir")
            }
        }
    }
}
