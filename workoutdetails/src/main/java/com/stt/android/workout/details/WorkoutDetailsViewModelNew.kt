package com.stt.android.workout.details

import androidx.core.app.NotificationManagerCompat
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.aerobiczone.AerobicZonesInfoSheet
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.colorfultrack.HeartRateWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PaceWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PowerWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.WorkoutColorfulTrackLoader
import com.stt.android.common.ui.ErrorEvent
import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.failure
import com.stt.android.common.viewstate.loaded
import com.stt.android.common.viewstate.loading
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.core.domain.GraphType
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.fit.DownloadFitFileUseCase
import com.stt.android.domain.fit.DownloadJsonFileUseCase
import com.stt.android.domain.report.ReportWorkoutUseCase
import com.stt.android.domain.routes.ExportGpxRouteUseCase
import com.stt.android.domain.routes.ExportGpxTrackUseCase
import com.stt.android.domain.routes.ExportKmlRouteUseCase
import com.stt.android.domain.sml.FetchSmlUseCase
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.weather.WeatherConditions
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workouts.AdditionalData
import com.stt.android.domain.workouts.DomainWorkout
import com.stt.android.domain.workouts.FetchWorkoutByKeyUseCase
import com.stt.android.domain.workouts.GetWorkoutByIdUseCase
import com.stt.android.domain.workouts.WorkoutDataSource
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.competition.CompetitionWorkoutSummaryData
import com.stt.android.domain.workouts.extensions.DiveExtension
import com.stt.android.exceptions.remote.HttpException
import com.stt.android.extensions.fitFilename
import com.stt.android.extensions.jsonFilename
import com.stt.android.extensions.useSkiMapStyle
import com.stt.android.infomodel.SummaryGraph
import com.stt.android.maps.MAP_TYPE_SKI
import com.stt.android.maps.MapSnapshotSpec
import com.stt.android.maps.MapType
import com.stt.android.maps.MapTypeHelper
import com.stt.android.maps.isSkiMap
import com.stt.android.models.MapSelectionModel
import com.stt.android.ui.controllers.WorkoutDataLoaderController
import com.stt.android.ui.controllers.loadWorkout
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.utils.STTConstants.ExtraKeys.FROM_NOTIFICATION
import com.stt.android.utils.STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE
import com.stt.android.workout.details.achievements.AchievementsDataLoader
import com.stt.android.workout.details.ads.hrbelt.HrBeltAdDataLoader
import com.stt.android.workout.details.analytics.WorkoutDetailsAnalytics
import com.stt.android.workout.details.comments.CommentsLoader
import com.stt.android.workout.details.competition.CompetitionWorkoutDataLoader
import com.stt.android.workout.details.divelocation.DiveLocationDataLoader
import com.stt.android.workout.details.diveprofile.DiveProfileDataLoader
import com.stt.android.workout.details.divetrack.DiveTrackDataLoader
import com.stt.android.workout.details.extensions.DiveExtensionDataLoader
import com.stt.android.workout.details.heartrate.HeartRateDataLoader
import com.stt.android.workout.details.intensity.ZoneAnalysisDataLoader
import com.stt.android.workout.details.laps.LapsDataLoader
import com.stt.android.workout.details.multisport.MultisportPartActivityLoader
import com.stt.android.workout.details.reactions.ReactionsLoader
import com.stt.android.workout.details.shareactivity.ShareActivityLoader
import com.stt.android.workout.details.sml.SmlDataLoader
import com.stt.android.workout.details.summary.RecentWorkoutSummaryDataLoader
import com.stt.android.workout.details.trend.RecentTrendDataLoader
import com.stt.android.workout.details.watch.WorkoutExtensionsDataLoader
import com.stt.android.workout.details.weather.WeatherConditionsLoader
import com.stt.android.workout.details.workoutdata.WorkoutDataLoader
import com.stt.android.workout.details.workoutheader.WorkoutHeaderLoader
import com.stt.android.workout.details.workoutvalues.WorkoutValuesLoader
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Dispatchers.IO
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.conflate
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.drop
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.EnumSet
import javax.inject.Inject

@OptIn(FlowPreview::class)
@HiltViewModel
class WorkoutDetailsViewModelNew @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    val navigationEventDispatcher: NavigationEventDispatcher,
    private val getWorkoutByIdUseCase: GetWorkoutByIdUseCase,
    private val workoutHeaderController: WorkoutHeaderController,
    private val reportWorkoutUseCase: ReportWorkoutUseCase,
    private val workoutDetailsAnalytics: WorkoutDetailsAnalytics,
    private val downloadFitFileUseCase: DownloadFitFileUseCase,
    private val downloadJsonFileUseCase: DownloadJsonFileUseCase,
    private val exportGpxRouteUseCase: ExportGpxRouteUseCase,
    private val exportGpxTrackUseCase: ExportGpxTrackUseCase,
    private val exportKmlRouteUseCase: ExportKmlRouteUseCase,
    private val notificationManager: NotificationManagerCompat,
    private val workoutValuesLoader: WorkoutValuesLoader,
    private val toolbarDataLoader: ToolbarDataLoader,
    private val coverImageDataLoader: CoverImageDataLoader,
    private val reactionsLoader: ReactionsLoader,
    private val commentsLoader: CommentsLoader,
    private val shareActivityLoader: ShareActivityLoader,
    @DefaultHeartRateLoader private val heartRateDataLoader: HeartRateDataLoader,
    private val workoutDataLoader: WorkoutDataLoader,
    private val smlDataLoader: SmlDataLoader,
    private val diveExtensionDataLoader: DiveExtensionDataLoader,
    private val diveProfileDataLoader: DiveProfileDataLoader,
    private val recentTrendDataLoader: RecentTrendDataLoader,
    private val workoutCompetitionDataLoader: CompetitionWorkoutDataLoader,
    private val recentWorkoutSummaryDataLoader: RecentWorkoutSummaryDataLoader,
    private val lapsDataLoader: LapsDataLoader,
    private val hrBeltAdDataLoader: HrBeltAdDataLoader,
    private val achievementsDataLoader: AchievementsDataLoader,
    private val diveLocationDataLoader: DiveLocationDataLoader,
    private val weatherConditionsLoader: WeatherConditionsLoader,
    private val multisportPartActivityLoader: MultisportPartActivityLoader,
    private val workoutHeaderLoader: WorkoutHeaderLoader,
    private val workoutExtensionsDataLoader: WorkoutExtensionsDataLoader,
    private val currentUserController: CurrentUserController,
    private val mapSelectionModel: MapSelectionModel,
    private val workoutDataSource: WorkoutDataSource,
    private val fetchWorkoutByKeyUseCase: FetchWorkoutByKeyUseCase,
    private val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val zoneAnalysisDataLoader: ZoneAnalysisDataLoader,
    private val diveTrackDataLoader: DiveTrackDataLoader,
    private val paceWorkoutColorfulTrackLoader: PaceWorkoutColorfulTrackLoader,
    private val heartRateWorkoutColorfulTrackLoader: HeartRateWorkoutColorfulTrackLoader,
    private val powerWorkoutColorfulTrackLoader: PowerWorkoutColorfulTrackLoader,
    private val workoutDataLoaderController: WorkoutDataLoaderController,
    private val fetchSmlUseCase: FetchSmlUseCase,
) : ViewModel() {
    private val _viewState = MutableLiveData<ViewState<WorkoutDetailsViewState?>>()
    val viewState: LiveData<ViewState<WorkoutDetailsViewState?>> = _viewState

    var currentPhotoPagerPosition: Int =
        savedStateHandle[SAVED_DATA_CURRENT_PHOTO_POSITION] ?: 0
        set(value) {
            field = value
            savedStateHandle[SAVED_DATA_CURRENT_PHOTO_POSITION] = value
        }

    private var currentTrendPage: Int =
        savedStateHandle[SAVED_DATA_CURRENT_TREND_PAGE] ?: 0
        set(value) {
            field = value
            savedStateHandle[SAVED_DATA_CURRENT_TREND_PAGE] = value
        }

    private var currentSummaryPage: Int =
        savedStateHandle[SAVED_DATA_CURRENT_SUMMARY_PAGE] ?: 0
        set(value) {
            field = value
            savedStateHandle[SAVED_DATA_CURRENT_SUMMARY_PAGE] = value
        }

    val onShowDiveEvents: LiveData<DiveProfileData?>
        get() = diveProfileDataLoader.navigationEvent

    val onAddCommentClick: LiveData<Unit>
        get() = commentsLoader.onAddCommentClick

    val onMenuClickEvent = toolbarDataLoader.onMenuClickEvent

    private val _reportSuccess = SingleLiveEvent<Boolean>()
    val reportSuccess: LiveData<Boolean>
        get() = _reportSuccess

    private val _targetDataIsMissing = SingleLiveEvent<Boolean>()
    val targetDataIsMissing: LiveData<Boolean>
        get() = _targetDataIsMissing

    private val _workoutDeleted = SingleLiveEvent<Boolean>()
    val workoutDeleted: LiveData<Boolean>
        get() = _workoutDeleted

    val onShareLink: LiveData<WorkoutHeader> = coverImageDataLoader.onShareLink

    private val _shareLink = SingleLiveEvent<String>()
    val shareLink: LiveData<String> = _shareLink

    val openSummariesTagClicked: LiveData<String>
        get() = _openSummariesTagClicked
    private val _openSummariesTagClicked = SingleLiveEvent<String>()

    val openAerobicZoneInfoClicked: LiveData<AerobicZonesInfoSheet>
        get() = _openAerobicZoneInfoClicked
    private val _openAerobicZoneInfoClicked = SingleLiveEvent<AerobicZonesInfoSheet>()

    private var longScreenshotLayout = false

    /**
     * Event String is analytics source
     */
    val openPremiumPromotionClicked: LiveData<String>
        get() = _openPremiumPromotionClicked
    private val _openPremiumPromotionClicked = SingleLiveEvent<String>()

    val onPremiumTagClicked: LiveData<String>
        get() = _onPremiumTagClicked
    private val _onPremiumTagClicked = SingleLiveEvent<String>()

    private val _shareLinkError = SingleLiveEvent<Unit>()
    val shareLinkError: LiveData<Unit> = _shareLinkError

    var shouldForceSkiMap = false

    /**
     * set longScreenshotLayout
     */
    fun setLongScreenshotLayout(longScreenshotLayout: Boolean) {
        this.longScreenshotLayout = longScreenshotLayout
        coverImageDataLoader.setLongScreenshotLayout(longScreenshotLayout)
    }

    fun showMultisportPartActivity(multisportPartActivity: MultisportPartActivity?) {
        multisportPartActivityLoader.setMultisportPartActivity(multisportPartActivity)
        workoutDetailsAnalytics.multisportPartChanged()
    }

    fun loadDataIfNeeded() {
        if (viewState.value == null || viewState.value?.isLoaded() == false) {
            loadData()
        }
    }

    fun loadData() = viewModelScope.launch {
        _viewState.value = loading()
        val analyticsSource: String? = savedStateHandle[NAVIGATED_FROM_SOURCE]

        val originalWorkout = getWorkout() ?: return@launch
        val seenStatusUpdated = markAsSeen(originalWorkout.header)
        val workout = if (seenStatusUpdated) {
            // Update workout before it's passed to loaders
            originalWorkout.copy(header = originalWorkout.header.copy(seen = true))
        } else {
            originalWorkout
        }

        workoutHeaderLoader.setWorkoutHeader(workout.header)
        setInitialMapState(workout.header.activityType)
        Timber.w("Loading workout details key=${workout.header.key} activity-type:${workout.header.activityType} start-timestamp:${workout.header.startTime}")

        collectWorkoutUpdates(workout)

        if (savedStateHandle.get<Boolean>(EXTRA_SHOW_MAP_GRAPH_ANALYSIS) == true) {
            trackWorkoutAnalysisScreen(analyticsSource)
        } else {
            trackWorkoutDetailsScreen(analyticsSource)
        }

        val showComments = savedStateHandle.get<Boolean>(EXTRA_SHOW_COMMENTS) == true
        showCommentsDialog(showComments, workout)

        cancelNotification(workout.header.key)

        @Suppress("UNCHECKED_CAST")
        combine(
            listOf(
                workoutHeaderLoader.workoutHeaderFlow,
                toolbarDataLoader.loadToolbarData(workout.header),
                coverImageDataLoader.loadCoverImagesData(workout),
                workoutValuesLoader.loadWorkoutValuesData(workout.header),
                reactionsLoader.loadReactionsData(workout.header),
                commentsLoader.loadComments(workout.header),
                shareActivityLoader.loadShareActivityData(workout.header),
                heartRateDataLoader.loadHeartRateData(workout.header, viewModelScope),
                workoutDataLoader.loadWorkoutData(workout.header),
                smlDataLoader.loadSml(workout.header),
                zoneAnalysisDataLoader.loadZoneAnalysisData(workout.header),
                diveExtensionDataLoader.loadDiveExtension(workout.header),
                diveProfileDataLoader.loadDiveProfile(workout.header),
                recentTrendDataLoader.loadRecentTrendData(workout.header, currentTrendPage),
                recentWorkoutSummaryDataLoader.loadSummary(workout.header, currentSummaryPage),
                workoutCompetitionDataLoader.loadCompetitionWorkout(workout.header),
                lapsDataLoader.loadLapsData(workout.header),
                hrBeltAdDataLoader.loadHrBeltAdData(workout.header),
                achievementsDataLoader.loadAchievements(workout.header),
                diveLocationDataLoader.loadDiveLocationData(workout.header),
                weatherConditionsLoader.loadWeatherConditions(workout.header),
                multisportPartActivityLoader.multisportPartActivityFlow,
                workoutExtensionsDataLoader.loadWorkoutExtensions(workout.header),
                isSubscribedToPremiumUseCase(),
                diveTrackDataLoader.loadDiveTrackData(workout.header)
            )
        ) { loadedData ->
            val isSubscribedToPremium = loadedData[23] as Boolean
            WorkoutDetailsViewState(
                workoutHeader = loadedData[0] as ViewState<WorkoutHeader?>,
                toolbar = loadedData[1] as ViewState<ToolbarData?>,
                coverImageData = loadedData[2] as ViewState<CoverImageData?>,
                workoutValues = loadedData[3] as ViewState<WorkoutValues?>,
                reactionsData = loadedData[4] as ViewState<ReactionsData?>,
                commentsData = loadedData[5] as ViewState<CommentsData>,
                shareActivityData = loadedData[6] as ViewState<ShareActivityData>,
                heartRateData = loadedData[7] as ViewState<HeartRateData>,
                workoutData = loadedData[8] as ViewState<WorkoutData?>,
                smlData = loadedData[9] as ViewState<Sml?>,
                zoneAnalysisData = loadedData[10] as ViewState<ZoneAnalysisData?>,
                diveExtensionData = loadedData[11] as ViewState<DiveExtension?>,
                diveProfileData = loadedData[12] as ViewState<DiveProfileData?>,
                recentTrendData = loadedData[13] as ViewState<RecentTrendData?>,
                recentWorkoutSummaryData = loadedData[14] as ViewState<RecentWorkoutSummaryData?>,
                competitionWorkoutSummaryData = loadedData[15] as ViewState<CompetitionWorkoutSummaryData?>,
                lapsData = loadedData[16] as ViewState<LapsData?>,
                hrBeltAdData = loadedData[17] as ViewState<HrBeltAdData?>,
                achievementsData = loadedData[18] as ViewState<AchievementsData?>,
                diveLocationData = loadedData[19] as ViewState<DiveLocationData?>,
                weatherConditions = loadedData[20] as ViewState<WeatherConditions?>,
                multisportPartActivity = loadedData[21] as ViewState<MultisportPartActivity?>,
                workoutExtensionsData = loadedData[22] as ViewState<WorkoutExtensionsData?>,
                isSubscribedToPremium = isSubscribedToPremium,
                onTrendPageSelected = ::onTrendPageSelected,
                onSummaryPageSelected = ::onSummaryPageSelected,
                onTagClicked = { tagName, isEditable ->
                    onTagClicked(tagName, isEditable, isSubscribedToPremium)
                },
                currentUsername = currentUserController.currentUser.username,
                onOpenPremiumPromotionClicked = ::onOpenPremiumPromotionClicked,
                diveTrackData = loadedData[24] as ViewState<DiveTrackData?>,
                onAerobicZoneInfoClicked = ::onAerobicZoneInfoClicked,
                hideBarInfo = savedStateHandle.get<Boolean>(EXTRA_HIDE_BAR_INFO) ?: false,
            )
        }.mapLatest { workoutDetailsViewState ->
            if (longScreenshotLayout) {
                loadColorfulTrackData(workoutDetailsViewState)
            } else {
                workoutDetailsViewState
            }
        }
            .conflate()
            .flowOn(Dispatchers.Default)
            .onEach { viewState ->
                Timber.d("Updating view state")
                _viewState.value = loaded(viewState)
            }
            .collect()
    }

    private suspend fun loadColorfulTrackData(
        workoutDetailsViewState: WorkoutDetailsViewState
    ): WorkoutDetailsViewState {
        val haveRouteCoverImage =
            workoutDetailsViewState.coverImageData?.data?.coverImages?.any {
                it is CoverImage.RouteCoverImage
            }
        val graphType =
            workoutDetailsViewState.zoneAnalysisData?.data?.mainGraphData?.lineChartData?.graphType
                ?: GraphType.NONE
        val colorfulTrackLoader = getColorfulTrackLoader(graphType)
        val workoutData = workoutDetailsViewState.workoutData?.data
        val workoutHeader = workoutDetailsViewState.workoutHeader?.data
        return if (haveRouteCoverImage == false ||
            colorfulTrackLoader == null ||
            workoutData == null ||
            workoutHeader == null ||
            workoutData.routePoints.isNullOrEmpty()
        ) {
            workoutDetailsViewState
        } else {
            val colorfulTrackMapData = colorfulTrackLoader.loadColorfulTrack(
                workoutData.routePoints,
                workoutHeader,
                workoutDetailsViewState.smlData?.data,
                workoutData.heartRateEvents,
                true
            )
            val coverImageData = workoutDetailsViewState.coverImageData?.data
            val newCoverImages = coverImageData?.coverImages?.map { routeCoverImage ->
                if (routeCoverImage is CoverImage.RouteCoverImage) {
                    routeCoverImage.copy(
                        colorfulPolylines = MapSnapshotSpec.ColorfulPolylines(
                            colorfulTrackMapData = colorfulTrackMapData,
                            graphType = graphType,
                            workoutId = routeCoverImage.workoutHeaderId,
                            width = 0,
                            height = 0,
                        )
                    )
                } else {
                    routeCoverImage
                }
            } ?: emptyList()
            workoutDetailsViewState.copy(
                coverImageData = loaded(
                    coverImageData?.copy(
                        coverImages = newCoverImages
                    )
                )
            )
        }
    }

    private fun getColorfulTrackLoader(graphType: GraphType?): WorkoutColorfulTrackLoader? {
        return when (graphType) {
            GraphType.Summary(SummaryGraph.HEARTRATE), GraphType.Summary(SummaryGraph.RECOVERYHRINTHREEMINS) -> heartRateWorkoutColorfulTrackLoader
            GraphType.Summary(SummaryGraph.PACE) -> paceWorkoutColorfulTrackLoader
            GraphType.Summary(SummaryGraph.POWER) -> powerWorkoutColorfulTrackLoader
            else -> null
        }
    }

    private suspend fun markAsSeen(workoutHeader: WorkoutHeader) = withContext(IO) {
        val workoutKey = workoutHeader.key
        if (!workoutKey.isNullOrEmpty() && !workoutHeader.seen) {
            workoutHeaderController.markAsSeen(workoutKey)
            true
        } else {
            false
        }
    }

    private fun collectWorkoutUpdates(workout: DomainWorkout) {
        viewModelScope.launch {
            workoutDataSource.fetchWorkoutHeader(workout.header.id)
                .drop(1) // Skip the first as it matches the current data
                .distinctUntilChanged()
                .catch {
                    Timber.w(it, "Collecting workout updates failed.")
                }.collect { workoutHeader ->
                    if (workoutHeader != null) {
                        workoutHeaderLoader.setWorkoutHeader(workoutHeader)
                        setInitialMapState(workoutHeader.activityType)
                        getWorkout(workoutHeader.id)?.run { coverImageDataLoader.update(this) }
                        commentsLoader.update(workoutHeader)
                        shareActivityLoader.update(workoutHeader)
                        diveLocationDataLoader.update(workoutHeader)
                        toolbarDataLoader.update(workoutHeader)
                        workoutValuesLoader.update(workoutHeader)
                        heartRateDataLoader.update(workoutHeader, viewModelScope)
                        recentWorkoutSummaryDataLoader.update(workoutHeader, currentSummaryPage)
                    } else {
                        _workoutDeleted.postValue(true)
                    }
                }
        }
    }

    private fun setInitialMapState(activityType: ActivityType) {
        val selectedMapType = mapSelectionModel.selectedMapType
        val skiMap = MapTypeHelper.find(MAP_TYPE_SKI)
        shouldForceSkiMap =
            skiMap != null && activityType.useSkiMapStyle && selectedMapType != skiMap
    }

    private fun showCommentsDialog(
        showComments: Boolean,
        workout: DomainWorkout
    ) {
        if (showComments) {
            savedStateHandle[EXTRA_SHOW_COMMENTS] = false
            navigationEventDispatcher.dispatchEvent(
                WorkoutDetailsCommentListDialogNavEvent(workout.header)
            )
        }
    }

    private suspend fun getWorkout(): DomainWorkout? {
        val workoutKey: String? = savedStateHandle.get<String>("workoutKey")
        val workoutId: Int? = savedStateHandle.get<Int>("workoutId")
        return when {
            workoutKey != null -> getWorkout(workoutKey)
            workoutId != null -> getWorkout(workoutId)
            else -> {
                Timber.w("getWorkout: Either workoutId, workoutKey or both need to be specified")
                null
            }
        }
    }

    private suspend fun getWorkout(workoutId: Int): DomainWorkout? = withContext(IO) {
        Timber.d("Loading workout data for ID: $workoutId")
        val workout = runSuspendCatching {
            getWorkoutByIdUseCase(workoutId)
        }.getOrElse { e ->
            Timber.w(e, "getWorkout failed(workoutId=$workoutId)")
            // TODO handle the exception and give relevant error event
            _viewState.postValue(failure(ErrorEvent(false, R.string.workout_not_found, false)))
            null
        }
        if (workout == null) {
            _viewState.postValue(failure(ErrorEvent(false, R.string.workout_not_found, false)))
        }
        workout
    }

    private suspend fun getWorkout(workoutKey: String): DomainWorkout? = withContext(IO) {
        Timber.d("Loading workout data for workoutKey: $workoutKey")
        runSuspendCatching {
            val header = workoutHeaderController.find(workoutKey)
            if (header != null) {
                getWorkout(header.id)
            } else {
                val username = savedStateHandle.get<String>("username") ?: ""
                val workout = fetchWorkoutByKeyUseCase(
                    username = username,
                    workoutKey = workoutKey,
                    additionalData = EnumSet.of(AdditionalData.PHOTOS, AdditionalData.VIDEOS)
                )
                // In some deep links the key can be different one from what is usually
                // used in the app, but when the workout is fetched from the backend the response
                // contains the normally used key. Update the savedStateHandle to use the usual key if needed
                if (workoutKey != workout.header.key) {
                    savedStateHandle["workoutKey"] = workout.header.key
                }
                workout
            }
        }.onFailure {
            Timber.w(it, "getWorkout failed(workoutKey=$workoutKey)")
            _viewState.postValue(failure(ErrorEvent(false, R.string.workout_not_found, false)))
        }.getOrNull()
    }

    private fun cancelNotification(workoutKey: String?) {
        val fromNotification = savedStateHandle.get<Boolean>(FROM_NOTIFICATION) ?: false
        if (fromNotification && workoutKey != null) {
            notificationManager.cancel(workoutKey.hashCode())
        }
    }

    fun onMenuClick(menuId: Int) {
        val domainWorkoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        val navEvent = createMenuClickedEvent(menuId, domainWorkoutHeader) ?: return
        navigationEventDispatcher.dispatchEvent(navEvent)
    }

    fun reportWorkout() {
        val workoutKey = _viewState.value?.data?.workoutHeader?.data?.key ?: return
        viewModelScope.launch {
            runSuspendCatching {
                reportWorkoutUseCase(workoutKey)
                _reportSuccess.postValue(true)
                trackReportWorkoutEvent()
            }.onFailure { e ->
                Timber.w(e, "Failed to report a workout with key: $workoutKey")
                _reportSuccess.postValue(false)
            }
        }
    }

    fun updateMapType() {
        if (!shouldForceSkiMap) {
            val selectedMapType = mapSelectionModel.selectedMapType
            coverImageDataLoader.update(selectedMapType)
        }
    }

    fun updateForceSkiMap(mapType: MapType) {
        if (shouldForceSkiMap && !mapType.isSkiMap) {
            shouldForceSkiMap = false
        }
    }

    private fun trackReportWorkoutEvent() {
        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackReportWorkoutEvent()
        }
    }

    fun trackSaveRouteEvent() {
        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackSaveRouteEvent()
        }
    }

    fun exportGpxWorkout() {
        val domainWorkoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        viewModelScope.launch(IO) {
            runSuspendCatching {
                domainWorkoutHeader.key?.let { key ->
                    exportGpxTrackUseCase(
                        ExportGpxTrackUseCase.Params(
                            domainWorkoutHeader.username,
                            key
                        )
                    )?.let { shareGpxTrackLink ->
                        _shareLink.postValue(shareGpxTrackLink)
                        workoutDetailsAnalytics.trackWorkoutExportEvent(
                            AnalyticsPropertyValue.ExportType.WORKOUT_GPX,
                            domainWorkoutHeader.activityTypeId
                        )
                    }
                }
            }.onFailure {
                Timber.w(it, "Error while fetching gpx track link")
                _shareLinkError.postValue(Unit)
                if (it is HttpException) {
                    workoutDetailsAnalytics.trackWorkoutExportError(
                        AnalyticsPropertyValue.ExportType.WORKOUT_GPX,
                        it.code
                    )
                } else {
                    workoutDetailsAnalytics.trackWorkoutExportError(AnalyticsPropertyValue.ExportType.WORKOUT_GPX)
                }
            }
        }
    }

    fun exportGpxRoute() {
        val domainWorkoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        viewModelScope.launch(IO) {
            runSuspendCatching {
                domainWorkoutHeader.key?.let { key ->
                    exportGpxRouteUseCase(
                        ExportGpxRouteUseCase.Params(
                            domainWorkoutHeader.username,
                            key
                        )
                    )?.let { shareGpxRouteLink ->
                        _shareLink.postValue(shareGpxRouteLink)
                        workoutDetailsAnalytics.trackWorkoutExportEvent(
                            AnalyticsPropertyValue.ExportType.ROUTE_GPX,
                            domainWorkoutHeader.activityTypeId
                        )
                    }
                }
            }.onFailure {
                Timber.w(it, "Error while fetching gpx route link")
                _shareLinkError.postValue(Unit)
                if (it is HttpException) {
                    workoutDetailsAnalytics.trackWorkoutExportError(
                        AnalyticsPropertyValue.ExportType.ROUTE_GPX,
                        it.code
                    )
                } else {
                    workoutDetailsAnalytics.trackWorkoutExportError(AnalyticsPropertyValue.ExportType.ROUTE_GPX)
                }
            }
        }
    }

    fun exportKmlRoute() {
        val workoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        viewModelScope.launch(IO) {
            runSuspendCatching {
                workoutHeader.key?.let { key ->
                    exportKmlRouteUseCase(
                        ExportKmlRouteUseCase.Params(
                            workoutHeader.username,
                            key
                        )
                    )?.let { shareKmlRouteLink ->
                        _shareLink.postValue(shareKmlRouteLink)
                        workoutDetailsAnalytics.trackWorkoutExportEvent(
                            AnalyticsPropertyValue.ExportType.ROUTE_KML,
                            workoutHeader.activityTypeId
                        )
                    }
                }
            }.onFailure {
                Timber.w(it, "Error while fetching kml route link")
                _shareLinkError.postValue(Unit)
                if (it is HttpException) {
                    workoutDetailsAnalytics.trackWorkoutExportError(
                        AnalyticsPropertyValue.ExportType.ROUTE_KML,
                        it.code
                    )
                } else {
                    workoutDetailsAnalytics.trackWorkoutExportError(AnalyticsPropertyValue.ExportType.ROUTE_KML)
                }
            }
        }
    }

    fun downloadFitFile() {
        val domainWorkoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        viewModelScope.launch(IO) {
            runSuspendCatching {
                val filename = domainWorkoutHeader.fitFilename
                domainWorkoutHeader.key?.run {
                    downloadFitFileUseCase.executeDownloadFitFile(this, filename)
                    workoutDetailsAnalytics.trackWorkoutExportEvent(
                        AnalyticsPropertyValue.ExportType.WORKOUT_FIT,
                        domainWorkoutHeader.activityTypeId
                    )
                }
            }.onFailure {
                Timber.w(it, "Error triggering download of FIT file")
                workoutDetailsAnalytics.trackWorkoutExportError(AnalyticsPropertyValue.ExportType.WORKOUT_FIT)
            }
        }
    }

    fun downloadJsonFile() {
        val domainWorkoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        viewModelScope.launch(IO) {
            runSuspendCatching {
                val filename = domainWorkoutHeader.jsonFilename
                domainWorkoutHeader.key?.run {
                    downloadJsonFileUseCase.executeDownloadJsonFile(this, filename)
                }
            }.onFailure {
                Timber.w(it, "Error triggering download of JSON file")
            }
        }
    }

    private fun trackWorkoutDetailsScreen(
        analyticsSource: String?
    ) {
        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackWorkoutDetailsScreenEvent(
                analyticsSource
            )
        }
    }

    private fun trackWorkoutAnalysisScreen(
        analyticsSource: String?,
    ) {
        viewModelScope.launch(NonCancellable) {
            workoutDetailsAnalytics.trackWorkoutAnalysisScreenEvent(
                analyticsSource,
                null
            )
        }
    }

    private fun onTrendPageSelected(page: Int) {
        currentTrendPage = page
    }

    private fun onSummaryPageSelected(page: Int) {
        workoutDetailsAnalytics.trackAnalysisThirtyDaySummaryGraphSwipe()
        currentSummaryPage = page
    }

    private fun onTagClicked(
        tagName: String,
        isEditable: Boolean,
        isSubscribedToPremium: Boolean
    ) {
        if (isSubscribedToPremium || isEditable) {
            _openSummariesTagClicked.value = tagName
        } else {
            // When we don't have premium and but we click on an item that says Premium, for example workout tags
            _onPremiumTagClicked.postValue(AnalyticsPropertyValue.PremiumPurchaseFlowScreenSource.WORKOUT_DETAILS_IMPACT_TAG)
        }
    }

    private fun onAerobicZoneInfoClicked(dest: AerobicZonesInfoSheet) {
        _openAerobicZoneInfoClicked.value = dest
    }

    private fun onOpenPremiumPromotionClicked(analyticsSource: String) {
        _openPremiumPromotionClicked.postValue(analyticsSource)
    }

    fun mediaEdited(workoutHeader: WorkoutHeader) {
        viewModelScope.launch {
            getWorkout(workoutHeader.id)?.run {
                coverImageDataLoader.update(this)
            }
        }
    }

    fun onScreenshot() {
        coverImageDataLoader.onScreenshot(currentPhotoPagerPosition)
    }

    fun checkTargetDataMissing() {
        val workoutHeader = viewState.value?.data?.workoutHeader?.data ?: return
        viewModelScope.launch {
            runSuspendCatching {
                val workoutData = workoutDataLoaderController.loadWorkout(workoutHeader)
                val sml = fetchSmlUseCase.fetchSml(workoutHeader.id, workoutHeader.key)
                _targetDataIsMissing.postValue(sml?.streamData?.speed.isNullOrEmpty() || workoutData.routePoints.isNullOrEmpty())
            }.onFailure { e ->
                Timber.w(e, "Failed to check target data missing: ${workoutHeader.key}")
                _targetDataIsMissing.postValue(true)
            }
        }
    }

    companion object {
        const val EXTRA_HIDE_BAR_INFO = "hideBarInfo"
        const val EXTRA_SHOW_COMMENTS = "showComments"
        const val EXTRA_SHOW_MAP_GRAPH_ANALYSIS = "showMapGraphAnalysis"
        const val SAVED_DATA_CURRENT_PHOTO_POSITION = "currentPhotoPosition"
        const val SAVED_DATA_CURRENT_TREND_PAGE = "currentTrendPage"
        const val SAVED_DATA_CURRENT_SUMMARY_PAGE = "currentSummaryPage"
    }
}
