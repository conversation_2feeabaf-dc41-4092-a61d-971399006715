buildscript {
    apply from: 'dependencies.gradle'

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }

    // Exclude kotlin-compiler-embeddable from all buildscript configurations
    configurations.configureEach {
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-compiler-embeddable'
    }

    dependencies {
        classpath libs.android.gradle.plugin
        classpath libs.ktlint.plugin
        classpath libs.dokka.plugin
        classpath libs.google.oss.licenses.plugin
        classpath libs.androidx.navigation.safe.args
    }
}

plugins {
    alias libs.plugins.gradle.versions
    alias libs.plugins.ssh
    // these below are to ensure all submodules will ask for the same plugin version
    alias libs.plugins.android.application apply false
    alias libs.plugins.android.library apply false
    alias libs.plugins.firebase.crashlytics apply false
    alias libs.plugins.firebase.perf apply false
    alias libs.plugins.gms apply false
    alias libs.plugins.kotlin.jvm apply false
    alias libs.plugins.kotlin.android apply false
    alias libs.plugins.kotlin.compose.compiler apply false
    alias libs.plugins.kotlin.kapt apply false
    alias libs.plugins.kotlin.ksp apply false
    alias libs.plugins.kotlin.allopen apply false
    alias libs.plugins.kotlinter apply false
    alias libs.plugins.hilt apply false
    alias libs.plugins.androidx.navigation.safeargs apply false
    alias libs.plugins.androidx.navigation.safeargs.kotlin apply false
    alias libs.plugins.protobuf apply false
    alias libs.plugins.play.publisher apply false
    alias libs.plugins.owasp.dependencycheck apply false
}

apply from: 'config.gradle'
apply from: 'version.gradle'
apply from: 'slack.gradle'
apply from: 'rws.gradle'

allprojects {
    apply plugin: 'org.owasp.dependencycheck'
    afterEvaluate {
        dependencyCheck {
            outputDirectory = './build/reports'
            skipTestGroups = true
            suppressionFile = 'dependency_check_suppressions.xml'
            analyzers {
                assemblyEnabled = false
                centralEnabled = false
            }
            scanConfigurations += project.configurations.findAll {
                !it.name.startsWithAny('androidTest', 'test', 'debug') && it.name.contains(
                    "DependenciesMetadata") &&
                    (it.name.startsWithAny("api", "implementation", "runtimeOnly") || it
                        .name.contains("Api") ||
                        it.name.contains("Implementation") ||
                        it.name.contains("RuntimeOnly"))
            }.collect {
                it.name
            }
            failBuildOnCVSS = 11 // Don't fail the build for now
            nvd {
                // Can be used without an API key, but downloading the database takes a lot longer.
//                apiKey = '<API KEY>'
            }
        }
    }
    repositories {
        // The order in which you list these repositories matter.
        google()
        mavenCentral()
        maven {
            url = "https://appboy.github.io/appboy-android-sdk/sdk"
            allowInsecureProtocol = true
        }
        sallyRelease()
        sallySnapshot()
        maven {
            url = 'https://www.jitpack.io'
            mavenContent {
                excludeGroupByRegex "com\\.asoy.*"
                excludeGroupByRegex "com\\.soy.*"
                excludeGroupByRegex "com\\.suunto.*"
                excludeGroupByRegex "com\\.amersports.*"
            }
        }
        maven {
            url = 'https://api.mapbox.com/downloads/v2/releases/maven'
            authentication {
                basic(BasicAuthentication)
            }
            credentials {
                // Do not change the username below.
                // This should always be `mapbox` (not your username).
                username = 'mapbox'
                password =
                    'sk.eyJ1IjoiYXNkaWdpdGFsIiwiYSI6ImNrZ2hraGVhdzFqMjMycnMxdTBuZ244cXYifQ._PwgqakJtmwmQDl2p3aSBw'
            }
        }
        maven { url = "https://oss.sonatype.org/content/repositories/snapshots" }
        sallyJcenter()
        // tiktok sdk
        maven { url = 'https://artifact.bytedance.com/repository/AwemeOpenSDK' }
        // doubao AI
        maven {  url 'https://artifact.bytedance.com/repository/Volcengine/' }

    }

    tasks.register("printAllDependencies", DependencyReportTask) {}
}

// Global variable to cache git result
project.ext.set("workingBranch", null)
/**
 * Get the name of the working branch of the project
 *
 * @return Name of the working branch
 */
def getWorkingBranch() {
    if (project.workingBranch) {
        return project.workingBranch
    }

    def workingBranch = providers.exec {
        commandLine("git", "--git-dir=${rootDir}/.git", "--work-tree=${rootDir}/", "rev-parse",
            "--abbrev-ref", "HEAD")
    }.standardOutput.asText.get().trim()
    project.ext.set("workingBranch", workingBranch)
    return project.workingBranch
}

static def isRunningOnCi() {
    return System.getenv('TEAMCITY_VERSION')
}

static def ciOnly(closure) {
    if (isRunningOnCi()) {
        closure()
    }
}
