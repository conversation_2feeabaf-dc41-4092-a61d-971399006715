package com.stt.android.session.firstlaunch

import android.app.Application
import com.facebook.FacebookSdk
import com.google.firebase.FirebaseApp
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.helpshift.Core
import com.helpshift.HelpshiftUser
import com.stt.android.BuildConfig
import com.stt.android.FeatureFlags
import com.stt.android.LoggingInitializer
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.FirebaseAnalyticsTracker
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.initializer.AppInitializer
import com.stt.android.di.initializer.AppInitializerPostTermsApproval
import com.stt.android.domain.session.firstlaunch.FirstLaunchDataSource
import com.stt.android.domain.user.GetCurrentUserUseCase
import com.stt.android.help.HelpshiftInitializer
import com.stt.android.logs.VisibleActivityTracker
import com.stt.android.session.configuration.SignInConfiguration
import com.stt.android.tasks.startup.InitializeExceptionHandlerTask
import com.stt.android.usecases.startup.AppStabilityReportingUseCase
import com.stt.android.usecases.startup.LowPriorityStartupUseCase
import io.reactivex.rxkotlin.subscribeBy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OnFirstLaunchTermsAcceptedAppInitializer @Inject constructor(
    private val firstLaunchDataSource: FirstLaunchDataSource,
    private val signInConfiguration: SignInConfiguration,
    private val featureFlags: FeatureFlags,
    private val lowPriorityStartupUseCase: LowPriorityStartupUseCase,
    private val appStabilityReportingUseCase: AppStabilityReportingUseCase,
    private val userSettingsController: UserSettingsController,
    private val postInitializers: Set<@JvmSuppressWildcards AppInitializerPostTermsApproval>,
    private val getCurrentUserUseCase: GetCurrentUserUseCase,
    private val visibleActivityTracker: VisibleActivityTracker,
    private val firebaseAnalyticsTracker: FirebaseAnalyticsTracker,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
    dispatchers: CoroutinesDispatchers,
) : AppInitializer {
    private val coroutineScope = CoroutineScope(dispatchers.main)

    override fun init(app: Application) {
        // Check synchronously to make sure previously accepted terms are handled
        // before any Activities are launched. In practice affects only playstore
        // builds that have been configured to require the acceptance,
        // which we currently don't have (and if we started to, then Firebase
        // dynamic links handling in PlaystoreProxyActivity would need refactoring).
        if (!signInConfiguration.requireFirstLaunchTermsAcceptance ||
            firstLaunchDataSource.isFirstLaunchTermsAcceptedSync()
        ) {
            onFirstLaunchTermsAccepted(app)
        } else {
            coroutineScope.launch {
                firstLaunchDataSource
                    .isFirstLaunchTermsAcceptedFlow()
                    .filter { it }
                    .take(1)
                    .collect {
                        onFirstLaunchTermsAccepted(app)
                    }
            }
        }
    }

    private fun onFirstLaunchTermsAccepted(app: Application) {
        // Firebase is initialized automatically using a ContentProvider in the main process.
        // Other processes need manual initialization.
        if (FirebaseApp.initializeApp(app) == null) {
            Timber.d("FirebaseApp initialization unsuccessful")
        } else {
            Timber.d("FirebaseApp initialization successful")
        }

        InitializeExceptionHandlerTask(app).start()

        // Collection is disabled by default from AndroidManifest#meta-data$firebase_crashlytics_collection_enabled=false
        FirebaseCrashlytics.getInstance()
            .setCrashlyticsCollectionEnabled(!BuildConfig.DEBUG)
        LoggingInitializer().apply {
            initializeTimberPostFirstLaunchTermsCheck()
            initializeForBuildType(app)
        }
        firebaseAnalyticsTracker.init(app)
        amplitudeAnalyticsTracker.initialize(app)

        app.registerActivityLifecycleCallbacks(visibleActivityTracker)
        featureFlags.refreshFeatureFlags()

        initHelpShift(app)
        initFacebook()

        // Was initially in CurrentUserController
        val analyticsUUID: String = userSettingsController.settings.analyticsUUID
        FirebaseCrashlytics.getInstance().setUserId(analyticsUUID)
        // --

        postInitializers.forEach {
            it.init(app)
        }

        coroutineScope.launch {
            runSuspendCatching { lowPriorityStartupUseCase.getStartupTask(app) }
                .onFailure { e -> Timber.w(e, "Error during startup task") }
        }

        val alsoNoNeedToDispose = appStabilityReportingUseCase.getStabilityReportingCompletable()
            .subscribeBy(onError = { Timber.w(it, "Error during stability reporting task") })
    }

    private fun initFacebook() {
        // Automatic SDK Initialization is disabled from the manifest `com.facebook.sdk.AutoInitEnabled=false`
        // For more info: https://developers.facebook.com/docs/app-events/gdpr-compliance/
        FacebookSdk.setAdvertiserIDCollectionEnabled(true) // Disabled from manifest: AdvertiserIDCollectionEnabled
        FacebookSdk.setAutoInitEnabled(true)
        FacebookSdk.fullyInitialize()
    }

    private fun initHelpShift(app: Application) {
        HelpshiftInitializer.initHelpshift(app)
        val currentUser = getCurrentUserUseCase.getCurrentUser()
        if (!currentUser.isAnonymous) {
            Core.login(
                HelpshiftUser.Builder(currentUser.username, null)
                    .setName(currentUser.realNameOrUsername)
                    .build()
            )
        }
    }

    private fun initMapbox() {

    }
}
