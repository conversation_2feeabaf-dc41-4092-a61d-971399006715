package com.stt.android.session.phonenumberverification

import com.google.i18n.phonenumbers.PhoneNumberUtil
import javax.inject.Inject

class IsValidPhoneNumberUseCase @Inject constructor() {

    private val phoneNumberUtil by lazy { PhoneNumberUtil.getInstance() }

    fun run(phoneNumberText: String): Result<Boolean> =
        runCatching {
            val phoneNumber =
                phoneNumberUtil.parse(phoneNumberText, DEFAULT_REGION_CODE)
            phoneNumberUtil.isValidNumber(phoneNumber)
        }

    companion object {
        const val DEFAULT_REGION_CODE = "CN"
    }
}
