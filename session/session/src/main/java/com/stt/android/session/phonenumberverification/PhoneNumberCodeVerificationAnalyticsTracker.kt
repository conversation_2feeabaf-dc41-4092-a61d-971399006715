package com.stt.android.session.phonenumberverification

import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import javax.inject.Inject

interface PhoneNumberCodeVerificationAnalyticsTracker {
    fun trackResendCode()
    fun trackEditPhoneNumber()
    fun trackVerificationError(errorType: String)
}

class PhoneNumberCodeVerificationAnalyticsTrackerImpl @Inject constructor(
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) :
    PhoneNumberCodeVerificationAnalyticsTracker {
    override fun trackResendCode() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ONBOARDING_VERIFICATION_RESEND_CODE)
    }

    override fun trackEditPhoneNumber() {
        amplitudeAnalyticsTracker.trackEvent(AnalyticsEvent.ONBOARDING_VERIFICATION_EDIT_PHONE_NUMBER)
    }

    override fun trackVerificationError(errorType: String) {
        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.ONBOARDING_VERIFICATION_ERROR,
            AnalyticsProperties().put(
                AnalyticsEventProperty.ERROR_TYPE,
                errorType
            )
        )
    }
}
