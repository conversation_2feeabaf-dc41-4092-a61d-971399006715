package com.stt.android.suuntoplusstore.home

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.suuntoplusstore.Item
import com.stt.android.suuntoplusstore.MultiItemTypeCategory
import com.stt.android.suuntoplusstore.features.SuuntoPlusStoreFeatureItem
import com.stt.android.suuntoplusstore.guides.SuuntoPlusStoreGuideItem
import com.stt.android.suuntoplusstore.partners.PartnersScreenViewState.Partner
import com.stt.android.suuntoplusstore.partners.SuuntoPlusStorePartnerItem
import com.stt.android.suuntoplusstore.partners.mockPartnerServiceMetadata
import com.stt.android.compose.widgets.CategoryHeader
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList

@Composable
fun SuuntoPlusStoreHomeScreenCategories(
    categories: ImmutableList<MultiItemTypeCategory>,
    onItemSelected: (Item) -> Unit,
    onPartnerSelected: (Partner) -> Unit,
    onCategorySelected: (MultiItemTypeCategory) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        for (category in categories) {
            if (category.isEmpty()) continue

            val title = category.title
            CategoryHeader(
                title = title,
                contentPadding = PaddingValues(start = MaterialTheme.spacing.medium),
                onCategorySelected = { onCategorySelected(category) }
            )
            HomeScreenCategory(category, onItemSelected, onPartnerSelected)
        }
    }
}

@Composable
private fun HomeScreenCategory(
    category: MultiItemTypeCategory,
    onItemSelected: (Item) -> Unit,
    onPartnerSelected: (Partner) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        if (category.features.isNotEmpty()) {
            CategoryCarousel(category.features, onItemSelected)
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        }

        if (category.partners.isNotEmpty()) {
            CategoryPartnerCarousel(
                partners = category.partners,
                onPartnerSelected = onPartnerSelected,
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        }

        if (category.guides.isNotEmpty()) {
            CategoryCarousel(items = category.guides, onItemSelected = onItemSelected)
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        }
        if (category.trainingPlan.isNotEmpty()) {
            CategoryCarousel(items = category.trainingPlan, onItemSelected = onItemSelected)
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
        }
    }
}

@Composable
private fun CategoryCarousel(
    items: ImmutableList<Item>,
    onItemSelected: (Item) -> Unit,
    modifier: Modifier = Modifier,
    itemModifier: Modifier = Modifier.width(154.dp),
) {
    LazyRow(modifier = modifier) {
        itemsIndexed(items, key = { _, item -> item.id }) { index, item ->
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            when (item) {
                is Item.Feature ->
                    SuuntoPlusStoreFeatureItem(
                        feature = item,
                        onItemSelected = onItemSelected,
                        modifier = itemModifier,
                    )

                is Item.Guide -> SuuntoPlusStoreGuideItem(
                    guide = item,
                    onItemSelected = onItemSelected,
                    modifier = itemModifier,
                )
            }
            if (index == items.lastIndex) {
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            }
        }
    }
}

@Composable
private fun CategoryPartnerCarousel(
    partners: ImmutableList<Partner>,
    onPartnerSelected: (Partner) -> Unit,
    modifier: Modifier = Modifier,
    itemModifier: Modifier = Modifier.width(154.dp),
) {
    LazyRow(modifier = modifier) {
        items(partners, key = Partner::id) { partner ->
            SuuntoPlusStorePartnerItem(
                partner = partner,
                onItemSelected = onPartnerSelected,
                modifier = itemModifier,
            )
            Spacer(modifier = Modifier.width(MaterialTheme.spacing.small))
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewSuuntoPlusStoreHomeScreenCategories() {
    AppTheme {
        SuuntoPlusStoreHomeScreenCategories(
            categories = previewMultiItemTypeCategories,
            onItemSelected = {},
            onPartnerSelected = {},
            onCategorySelected = {},
        )
    }
}

@Preview(name = "empty", showBackground = true)
@Composable
private fun PreviewSuuntoPlusStoreHomeScreenCategoriesEmpty() {
    AppTheme {
        SuuntoPlusStoreHomeScreenCategories(
            categories = persistentListOf(),
            onItemSelected = {},
            onPartnerSelected = {},
            onCategorySelected = {},
        )
    }
}

@Preview(name = "empty category", showBackground = true)
@Composable
private fun PreviewSuuntoPlusStoreHomeScreenCategoriesEmptyCategory() {
    AppTheme {
        SuuntoPlusStoreHomeScreenCategories(
            persistentListOf(
                MultiItemTypeCategory(
                    id = "id",
                    title = "title",
                    description = "description",
                    bannerImageUrl = "",
                    features = persistentListOf(),
                    partners = persistentListOf(),
                    guides = persistentListOf(),
                    trainingPlan = persistentListOf()
                )
            ),
            onItemSelected = {},
            onPartnerSelected = {},
            onCategorySelected = {},
        )
    }
}

@Preview(name = "only features", showBackground = true)
@Composable
private fun PreviewSuuntoPlusStoreHomeScreenCategoriesOnlyFeatures() {
    AppTheme {
        SuuntoPlusStoreHomeScreenCategories(
            persistentListOf(
                MultiItemTypeCategory(
                    id = "id",
                    title = "title",
                    description = "description",
                    bannerImageUrl = "",
                    features = previewMultiItemTypeCategories.first().features,
                    partners = persistentListOf(),
                    guides = persistentListOf(),
                    trainingPlan = persistentListOf()
                )
            ),
            onItemSelected = {},
            onPartnerSelected = {},
            onCategorySelected = {},
        )
    }
}

internal val previewMultiItemTypeCategories = (1..10).map { c ->
    MultiItemTypeCategory(
        id = "category_$c,",
        title = "category #$c",
        description = "description for category #$c",
        bannerImageUrl = "",
        features = (1..c).map { i ->
            Item.Feature(
                id = "feature_$i",
                name = "Sports app $i",
                shortDescription = "short description",
                richDescription = null,
                description = "long description, ".repeat(5),
                detailScreenImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzbrnr01.png",
                ownerTitle = "Suunto",
                ownerLogoUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/suunto/sports-app-owner-logo.png",
                gridItemImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/default.png",
                localizedRichText = "",
                localizedRichTextAutomatically = false,
                isWatchface = false,
            )
        }.toImmutableList(),
        partners = (1..c).map { i ->
            Partner(
                id = "partner_$i",
                gridItemImageUrl = "https://aspartnercontent.blob.core.windows.net/partner-content/strava/icon.png",
                name = "Partner $i",
                description = "description",
                isConnected = true,
                serviceMetadata = mockPartnerServiceMetadata,
            )
        }.toImmutableList(),
        guides = (1..c).map { i ->
            Item.Guide(
                id = "guide_$i",
                name = "Guide $i",
                shortDescription = "short description",
                richDescription = null,
                description = "long description, ".repeat(5),
                detailScreenImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
            )
        }.toImmutableList(),
        trainingPlan = (1..c).map { i ->
            Item.Guide(
                id = "guide_$i",
                name = "Guide $i",
                shortDescription = "short description",
                richDescription = null,
                description = "long description, ".repeat(5),
                detailScreenImageUrl = "https://suuntopluspluginsdev.blob.core.windows.net/guide-icons/57949619c7cd556bc347cd987b816217.png",
            )
        }.toImmutableList()
    )
}.toImmutableList()
