package com.stt.android.suuntoplusstore.guides.domain

import com.stt.android.suuntoplusstore.domain.SuuntoPlusStoreCategory
import kotlinx.collections.immutable.ImmutableList
import com.stt.android.device.remote.suuntoplusguide.SuuntoPlusStoreGuide

data class SuuntoPlusStoreGuideContainer(
    val categories: ImmutableList<SuuntoPlusStoreCategory>,
    val items: ImmutableList<SuuntoPlusStoreGuide>,
)
