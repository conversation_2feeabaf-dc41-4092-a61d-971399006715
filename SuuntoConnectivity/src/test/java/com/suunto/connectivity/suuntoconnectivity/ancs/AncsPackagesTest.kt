package com.suunto.connectivity.suuntoconnectivity.ancs

import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class AncsPackagesTest {
    @Test
    fun packageCategories() {
        assertEquals(
            AncsConstants.CategoryID.INCOMING_CALL,
            AncsPackages.getCategory(0, "com.android.phone", null)
        )
        assertEquals(
            AncsConstants.CategoryID.INCOMING_CALL,
            AncsPackages.getCategory(0, "com.google.android.dialer", null)
        )
        assertEquals(
            AncsConstants.CategoryID.INCOMING_CALL,
            AncsPackages.getCategory(0, "com.android.incallui", null)
        )

        assertEquals(
            AncsConstants.CategoryID.MISSED_CALL,
            AncsPackages.getCategory(1, "com.android.phone", null)
        )
        assertEquals(
            AncsConstants.CategoryID.MISSED_CALL,
            AncsPackages.getCategory(1, "com.google.android.dialer", null)
        )
        assertEquals(
            AncsConstants.CategoryID.MISSED_CALL,
            AncsPackages.getCategory(0, "com.android.server.telecom", null)
        )

        assertEquals(
            AncsConstants.CategoryID.EMAIL,
            AncsPackages.getCategory(0, "com.android.mms", null)
        )
        assertEquals(
            AncsConstants.CategoryID.EMAIL,
            AncsPackages.getCategory(0, "com.google.android.talk", null)
        )

        assertEquals(
            AncsConstants.CategoryID.SYSTEM,
            AncsPackages.getCategory(0, "com.android.systemui", null)
        )
        assertEquals(
            AncsConstants.CategoryID.SYSTEM,
            AncsPackages.getCategory(0, "android", null)
        )

        assertEquals(
            AncsConstants.CategoryID.OTHER,
            AncsPackages.getCategory(0, "com.foo.bar", null)
        )
    }

    @Test
    fun testIsCallPackage() {
        assertTrue(AncsPackages.isCallPackage("com.android.phone"))
        assertFalse(AncsPackages.isCallPackage("com.suunto.phone"))
    }

    @Test
    fun testIsSmsPackage() {
        assertTrue(AncsPackages.isSmsPackage("com.android.mms"))
        assertFalse(AncsPackages.isSmsPackage("com.suunto.sms"))
    }

    @Test
    fun testIsClockPackage() {
        assertTrue(AncsPackages.isClockPackage("com.android.clock"))
        assertFalse(AncsPackages.isClockPackage("android"))
    }
}
