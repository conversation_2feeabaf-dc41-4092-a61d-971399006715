package com.suunto.connectivity.suuntoconnectivity.ancs;

import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

public class AncsNotificationTest {

    private static final int NOTIFICATION_UID = 12345;
    private static final int TITLE_MAX_LENGTH = 10;
    private static final int SUBTITLE_MAX_LENGTH = 20;
    private static final int MESSAGE_MAX_LENGTH = 20;
    private static final String TITLE = "Title äö long title";
    private static final String MESSAGE = "Message äö long text here also does not fit";
    private static final byte CATEGORY_ID = AncsConstants.CategoryID.EMAIL;

    private AncsNotification notification;
    private Date timestamp;

    @Before
    public void setUp() {
        timestamp = new Date();
        notification = new AncsNotification(
            NOTIFICATION_UID, TITLE, MESSAGE, CATEGORY_ID, timestamp);
    }

    @Test
    public void getters() {
        assertEquals(NOTIFICATION_UID, notification.getUid());
        assertEquals(TITLE, notification.getTitle());
        assertEquals(MESSAGE, notification.getMessage());
        assertEquals(CATEGORY_ID, notification.getCategoryId());
        assertEquals(timestamp, notification.getTimestamp());
        assertEquals((TITLE + MESSAGE).hashCode(), notification.getContentHash());
    }

    @Test
    public void fillAttributeValues() {
        List<AncsNotificationAttribute> attributes = new ArrayList<>();
        attributes.add(new AncsNotificationAttribute(
                AncsConstants.NotificationAttributeID.TITLE, TITLE_MAX_LENGTH));
        attributes.add(new AncsNotificationAttribute(
                AncsConstants.NotificationAttributeID.SUBTITLE, SUBTITLE_MAX_LENGTH));
        attributes.add(new AncsNotificationAttribute(
                AncsConstants.NotificationAttributeID.MESSAGE, MESSAGE_MAX_LENGTH));
        attributes.add(new AncsNotificationAttribute(
                AncsConstants.NotificationAttributeID.DATE));

        AncsProtocol.NotificationAttributesPacket packet =
                new AncsProtocol.NotificationAttributesPacket(
                        NOTIFICATION_UID, attributes);

        notification.fillNotificationAttributeValues(packet);

        // ä and ö take two bytes, title length in characters is smaller than in bytes
        String title = packet.getAttributes().get(0).getValue();
        assertNotNull(title);
        assertEquals(TITLE_MAX_LENGTH - 2, title.length());
        assertEquals("Title äö", title);

        // Subtitle is not supported
        String subtitle = packet.getAttributes().get(1).getValue();
        assertNull(subtitle);

        String message = packet.getAttributes().get(2).getValue();
        assertNotNull(message);
        assertEquals(MESSAGE_MAX_LENGTH - 2, message.length());
        assertEquals("Message äö long te", message);

        String date = packet.getAttributes().get(3).getValue();
        assertEquals(AncsConstants.DATE_FORMAT.format(timestamp), date);
    }

}
