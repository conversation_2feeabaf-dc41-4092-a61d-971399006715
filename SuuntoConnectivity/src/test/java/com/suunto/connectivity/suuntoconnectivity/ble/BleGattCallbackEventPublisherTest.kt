package com.suunto.connectivity.suuntoconnectivity.ble

import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothProfile
import android.os.Handler
import com.google.common.truth.Truth.assertThat
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattCharacteristicChangedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattCharacteristicReadEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattCharacteristicWriteEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattConnectionStateChangedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattDescriptorReadEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattDescriptorWriteEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattMtuChangedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattPhyChangedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattReadRemoteRssiEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattReliableWriteCompletedEvent
import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServicesDiscoveredEvent
import org.greenrobot.eventbus.EventBus
import org.junit.Before
import org.junit.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.check
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class BleGattCallbackEventPublisherTest {

    private val bluetoothGatt = mock<BluetoothGatt>()
    private val characteristic = mock<BluetoothGattCharacteristic>()
    private val descriptor = mock<BluetoothGattDescriptor>()
    private val eventBus = mock<EventBus>()

    private lateinit var eventPublisher: BleGattCallbackEventPublisher

    @Before
    fun setUp() {
        whenever(characteristic.value).thenReturn(VALUE)
        whenever(descriptor.value).thenReturn(VALUE)

        val handler = mock<Handler> {
            on { post(any()) }.then { invocation ->
                val runnable = invocation.arguments.first() as Runnable
                runnable.run()
                true
            }
        }

        eventPublisher = BleGattCallbackEventPublisher(eventBus, handler)
    }

    @Test
    fun bleGattCharacteristicChangedEvent() {
        eventPublisher.onCharacteristicChanged(bluetoothGatt, characteristic)
        verify(eventBus).post(
            check {
                val event = it as BleGattCharacteristicChangedEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.characteristic).isSameInstanceAs(characteristic)
                assertThat(event.value).isEqualTo(VALUE)
            }
        )
    }

    @Test
    fun bleGattCharacteristicReadEvent() {
        eventPublisher.onCharacteristicRead(
            bluetoothGatt,
            characteristic,
            BluetoothGatt.GATT_READ_NOT_PERMITTED
        )
        verify(eventBus).post(
            check {
                val event = it as BleGattCharacteristicReadEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.characteristic).isSameInstanceAs(characteristic)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_READ_NOT_PERMITTED)
                assertThat(event.value).isEqualTo(VALUE)
            }
        )
    }

    @Test
    fun bleGattCharacteristicWriteEvent() {
        eventPublisher.onCharacteristicWrite(
            bluetoothGatt,
            characteristic,
            BluetoothGatt.GATT_WRITE_NOT_PERMITTED
        )
        verify(eventBus).post(
            check {
                val event = it as BleGattCharacteristicWriteEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.characteristic).isSameInstanceAs(characteristic)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_WRITE_NOT_PERMITTED)
            }
        )
    }

    @Test
    fun bleGattConnectionStateChangedEvent() {
        eventPublisher.onConnectionStateChange(
            bluetoothGatt,
            BluetoothGatt.GATT_SUCCESS,
            BluetoothProfile.STATE_CONNECTED
        )
        verify(eventBus).post(
            check {
                val event = it as BleGattConnectionStateChangedEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_SUCCESS)
                assertThat(event.newState).isEqualTo(BluetoothProfile.STATE_CONNECTED)
            }
        )
    }

    @Test
    fun bleGattDescriptorReadEvent() {
        eventPublisher.onDescriptorRead(
            bluetoothGatt,
            descriptor,
            BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION
        )
        verify(eventBus).post(
            check {
                val event = it as BleGattDescriptorReadEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.descriptor).isSameInstanceAs(descriptor)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION)
                assertThat(event.value).isEqualTo(VALUE)
            }
        )
    }

    @Test
    fun bleGattDescriptorWriteEvent() {
        eventPublisher.onDescriptorWrite(
            bluetoothGatt,
            descriptor,
            BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION
        )
        verify(eventBus).post(
            check {
                val event = it as BleGattDescriptorWriteEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.descriptor).isSameInstanceAs(descriptor)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION)
            }
        )
    }

    @Test
    fun bleGattMtuChangedEvent() {
        val mtu = 100
        eventPublisher.onMtuChanged(bluetoothGatt, mtu, BluetoothGatt.GATT_SUCCESS)
        verify(eventBus).post(
            check {
                val event = it as BleGattMtuChangedEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.mtu).isEqualTo(mtu)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_SUCCESS)
            }
        )
    }

    @Test
    fun bleGattPhyChangedEvent() {
        val rxPhy = 1
        val txPhy = 1
        eventPublisher.onPhyUpdate(bluetoothGatt, txPhy, rxPhy, BluetoothGatt.GATT_SUCCESS)
        verify(eventBus).post(
            check {
                val event = it as BleGattPhyChangedEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.rxPhy).isEqualTo(rxPhy)
                assertThat(event.txPhy).isEqualTo(txPhy)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_SUCCESS)
            }
        )
    }

    @Test
    fun bleGattReadRemoteRssiEvent() {
        val rssi = -10
        eventPublisher.onReadRemoteRssi(bluetoothGatt, rssi, BluetoothGatt.GATT_SUCCESS)
        verify(eventBus).post(
            check {
                val event = it as BleGattReadRemoteRssiEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.rssi).isEqualTo(rssi)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_SUCCESS)
            }
        )
    }

    @Test
    fun bleGattReliableWriteCompletedEvent() {
        eventPublisher.onReliableWriteCompleted(bluetoothGatt, BluetoothGatt.GATT_FAILURE)
        verify(eventBus).post(
            check {
                val event = it as BleGattReliableWriteCompletedEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_FAILURE)
            }
        )
    }

    @Test
    fun bleGattServicesDiscoveredEvent() {
        eventPublisher.onServicesDiscovered(bluetoothGatt, BluetoothGatt.GATT_SUCCESS)
        verify(eventBus).post(
            check {
                val event = it as BleGattServicesDiscoveredEvent
                assertThat(event.bluetoothGatt).isSameInstanceAs(bluetoothGatt)
                assertThat(event.status).isEqualTo(BluetoothGatt.GATT_SUCCESS)
            }
        )
    }

    companion object {
        private val VALUE = "testing".toByteArray()
    }
}
