package com.suunto.connectivity.suuntoconnectivity.ble.operation;

import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattService;

import com.suunto.connectivity.suuntoconnectivity.ble.event.BleGattServicesDiscoveredEvent;
import com.suunto.connectivity.suuntoconnectivity.ble.exception.GattDiscoverServicesException;
import static com.suunto.connectivity.suuntoconnectivity.ble.operation.BleOperation.OPERATION_START_EXCEPTION_MESSAGE;
import com.suunto.connectivity.util.workqueue.QueueOperation;

import org.greenrobot.eventbus.EventBus;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.anyList;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BleGattOperationDiscoverServicesTest {

    @Mock
    private BluetoothDevice bluetoothDevice;

    @Mock
    private BluetoothGatt bluetoothGatt;

    @Mock
    private List<BluetoothGattService> bluetoothGattServices;

    private BleGattOperationDiscoverServices operationDiscoverServices;

    @Before
    public void initialize() {
        when(bluetoothGatt.getDevice()).thenReturn(bluetoothDevice);
        when(bluetoothGatt.getServices()).thenReturn(bluetoothGattServices);

        operationDiscoverServices = spy(new BleGattOperationDiscoverServices(bluetoothGatt));
    }

    @Test
    public void discoverServicesSuccess() {
        when(bluetoothGatt.discoverServices()).thenReturn(true);

        operationDiscoverServices.run();

        assertEquals(QueueOperation.STATE_RUNNING, operationDiscoverServices.getState());
        verify(operationDiscoverServices, never()).onCompleted(anyList());

        EventBus.getDefault().post(new BleGattServicesDiscoveredEvent(bluetoothGatt, BluetoothGatt.GATT_SUCCESS));

        assertEquals(QueueOperation.STATE_FINISHED, operationDiscoverServices.getState());
        verify(operationDiscoverServices).onCompleted(bluetoothGattServices);
    }

    @Test
    public void discoverServicesFailure() {
        when(bluetoothGatt.discoverServices()).thenReturn(true);

        operationDiscoverServices.run();

        EventBus.getDefault().post(new BleGattServicesDiscoveredEvent(bluetoothGatt, BluetoothGatt.GATT_FAILURE));

        assertEquals(QueueOperation.STATE_FINISHED, operationDiscoverServices.getState());
        ArgumentCaptor<GattDiscoverServicesException> argument = ArgumentCaptor.forClass(GattDiscoverServicesException.class);
        verify(operationDiscoverServices).onError(argument.capture());
        assertEquals(BluetoothGatt.GATT_FAILURE, argument.getValue().getStatus());
    }

    @Test
    public void discoverServicesStartFailure() {
        when(bluetoothGatt.discoverServices()).thenReturn(false);

        operationDiscoverServices.run();
        ArgumentCaptor<GattDiscoverServicesException> argument = ArgumentCaptor.forClass(GattDiscoverServicesException.class);
        verify(operationDiscoverServices).onError(any(GattDiscoverServicesException.class));
        verify(operationDiscoverServices).onError(argument.capture());
        assertEquals(OPERATION_START_EXCEPTION_MESSAGE,
            argument.getValue().getMessage());
    }

}
