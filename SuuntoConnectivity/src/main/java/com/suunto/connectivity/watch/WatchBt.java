package com.suunto.connectivity.watch;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.Gson;
import com.movesense.mds.MdsResponse;
import com.stt.android.utils.NearbyDevicesUtilsKt;
import com.suunto.connectivity.battery.BatteryLevel;
import com.suunto.connectivity.battery.ChargingState;
import com.suunto.connectivity.battery.UsbCableState;
import com.suunto.connectivity.files.FileListMdsResponse;
import com.suunto.connectivity.files.LogFilesMdsResponse;
import com.suunto.connectivity.gps.entities.GpsFormat;
import com.suunto.connectivity.gps.entities.GpsFormatType;
import com.suunto.connectivity.gps.entities.GpsTimestamp;
import com.suunto.connectivity.gps.entities.NavigationSystem;
import com.suunto.connectivity.notifications.MdsNotificationCategoryEnabled;
import com.suunto.connectivity.notifications.NotificationsDevice;
import com.suunto.connectivity.offlinemusic.OfflineMusicSettings;
import com.suunto.connectivity.repository.AppInfo;
import com.suunto.connectivity.repository.RepositoryConfiguration;
import com.suunto.connectivity.repository.commands.InstallSelectedFirmwareResponse;
import com.suunto.connectivity.repository.commands.SetupPreferenceContract;
import com.suunto.connectivity.sdsmanager.MdsRx;
import com.suunto.connectivity.settings.Gender;
import com.suunto.connectivity.settings.UnitSystem;
import com.suunto.connectivity.settings.WearDirection;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_CONTRACT_EMPTY;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_SCHEME_PREFIX;
import static com.suunto.connectivity.suuntoconnectivity.SuuntoConnectivityConstants.MDS_URI_AUTHORITY;
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoBtDevice;
import com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor;
import static com.suunto.connectivity.suuntoconnectivity.utils.BtStateMonitor.BtEvent.DEVICE_PAIRED;
import com.suunto.connectivity.suuntoconnectivity.utils.ConnectMetadata;
import com.suunto.connectivity.sync.SynchronizerStorage;
import com.suunto.connectivity.sync.WatchSynchronizer;
import com.suunto.connectivity.trainingzone.BESTrainingZoneSyncData;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncExercises;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncProgress;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncRecovery;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncThresholds;
import com.suunto.connectivity.trainingzone.TrainingZoneSyncTraining;
import com.suunto.connectivity.voicefeedback.AutoLapMdsResponse;
import com.suunto.connectivity.voicefeedback.HeartRateMdsResponse;
import com.suunto.connectivity.watch.activitydata.ActivityDataHolder;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoResult;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareSuggestResult;
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareTransferStatus;
import com.suunto.connectivity.watch.sportmodes.SportModesDataHolder;
import com.suunto.connectivity.watch.userprofile.UserProfileBirthYearItem;
import com.suunto.connectivity.watch.userprofile.UserProfileGenderItem;
import com.suunto.connectivity.watch.userprofile.UserProfileHeightItem;
import com.suunto.connectivity.watch.userprofile.UserProfileMaxHeartItem;
import com.suunto.connectivity.watch.userprofile.UserProfileRestHeartItem;
import com.suunto.connectivity.watch.userprofile.UserProfileWeightItem;
import com.suunto.connectivity.wifi.AccessPointCollectionElement;
import com.suunto.connectivity.wifi.AccessPointEncryption;
import com.suunto.connectivity.wifi.AccessPointSecurity;
import com.suunto.connectivity.wifi.WifiAP;
import com.suunto.connectivity.wifi.WifiResult;
import com.suunto.connectivity.zonesense.ZoneSenseBaselineValuesSyncData;
import java.time.DayOfWeek;
import java.util.List;
import java.util.Locale;
import rx.Completable;
import rx.Observable;
import rx.Single;
import rx.Subscription;
import timber.log.Timber;

public abstract class WatchBt extends WatchStateHolder {

    protected static final String GNSS_URI =
        MDS_SCHEME_PREFIX + "MDS/GNSS/%s/EphemerisData";
    protected static final String GNSS_VERSION_URI =
        MDS_SCHEME_PREFIX + "%s/Device/GNSS/ExtendedEphemerisData/Date";
    protected static final String LOGBOOK_DATA_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Logbook/%s/byId/%d/Data";
    protected static final String LOGBOOK_DATA_TO_FILES_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Logbook/%s/byId/%d/ToFile";
    protected static final String LOGBOOK_ENTRIES_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Logbook/%s/Entries";
    protected static final String UNSYNCED_LOGS_URI = "%s/Logbook/UnsynchronisedLogs";
    protected static final String WATCH_BUSY_STATE_URI = "%s/Sync/BusyState";
    protected static final String SETTINGS_URI =
        MDS_SCHEME_PREFIX + MDS_URI_AUTHORITY + "Settings";
    private static final String VOICE_FEEDBACK_AUTOLAP_SUBSCRIPTION_URI = "%s/Voice/AutoLap";
    private static final String VOICE_FEEDBACK_HEARTRATE_SUBSCRIPTION_URI =
        "%s/Voice/HeartRateZone";

    private static final String VOICE_FEEDBACK_STATUS_SUBSCRIPTION_URI = "%s/Voice/Status";

    /**
     * OpenWeather related uri
     */
    protected static final String OPEN_WEATHER_SUBSCRIPTION_URI = "%s/Weather/Sync";
    protected static final String OPEN_WEATHER_INFO_URI = MDS_SCHEME_PREFIX + "%s/Weather/Info";
    protected static final String OPEN_WEATHER_CURRENT_URI =
        MDS_SCHEME_PREFIX + "%s/Weather/Current";
    protected static final String OPEN_WEATHER_FORECAST_URI =
        MDS_SCHEME_PREFIX + "%s/Weather/Forecast/%d";
    protected static final String OPEN_WEATHER_DAILY_URI =
        MDS_SCHEME_PREFIX + "%s/Weather/Daily/%d";
    protected static final String OPEN_WEATHER_DAILY_UVI_URI =
        MDS_SCHEME_PREFIX + "%s/Weather/DailyUvi/%d";
    protected static final String OPEN_WEATHER_HOURLY_AQI_URI =
        MDS_SCHEME_PREFIX + "%s/Weather/HourlyAqi/%d";
    protected static final String OPEN_WEATHER_SYNC_TIME_URI =
        MDS_SCHEME_PREFIX + "%s/Weather/Sync/LastTime";

    protected static final String LAST_KNOWN_LOCATION_URI =
        MDS_SCHEME_PREFIX + "%s/Fusion/Location/LastKnownGeoCoordinates";

    protected static final String FIND_PHONE_SUBSCRIPTION_URI = "%s/FindPhone/PhoneState";

    protected static final String FIND_PHONE_STATE_URI =
        MDS_SCHEME_PREFIX + "%s/FindPhone/PhoneState";

    protected static final String WATCH_OTA_CHECK_SUBSCRIPTION_URI = "%s/Update/Check";

    protected static final String UPDATE_OTA_TAG =
        MDS_SCHEME_PREFIX + "%s/Update/Force/Tag";

    protected static final String UPDATE_OTA_MANUAL_DOWNLOAD_FAG =
        MDS_SCHEME_PREFIX + "%s/Update/ManualDownloadFlag";

    public final Context context;
    private final SuuntoBtDevice suuntoBtDevice;
    private final WatchConnector watchConnector;
    @Nullable private final NotificationsDevice notificationsDevice;
    private final MdsRx mdsRx;
    private final Subscription mdsConnectedSubscription;
    private final BtStateMonitor.Listener btStateListener;
    private final BtStateMonitor btStateMonitor;

    protected WatchBt(
        Context context,
        SuuntoBtDevice suuntoBtDevice,
        WatchConnector watchConnector,
        MdsRx mdsRx,
        BluetoothAdapter bluetoothAdapter,
        BtStateMonitor btStateMonitor,
        @Nullable NotificationsDevice notificationsDevice) {
        this.context = context;
        this.suuntoBtDevice = suuntoBtDevice;
        this.watchConnector = watchConnector;
        this.notificationsDevice = notificationsDevice;
        this.mdsRx = mdsRx;
        this.btStateMonitor = btStateMonitor;

        initializeState(bluetoothAdapter);

        btStateListener = (event, deviceAddress, deviceName) -> {
            switch (event) {
                case DEVICE_UNPAIRED:
                case DEVICE_PAIRED:
                    if (suuntoBtDevice.getMacAddress().equals(deviceAddress)) {
                        setPaired(event == DEVICE_PAIRED);
                    }
                    break;
            }
        };
        btStateMonitor.registerListener(btStateListener);

        mdsConnectedSubscription = this.watchConnector.getMdsConnectedSubscription(
            this::onMdsConnected,
            throwable -> Timber.w(throwable, "Mds ConnectedDevice error."));
    }

    protected void onMdsConnected(@NonNull String serial) {
        Timber.d("Mds connected to serial: %s", serial);
    }

    /**
     * Is ble connected? This is used only for analytics purposes.
     *
     * @return True, if already connected.
     */
    public boolean isAlreadyConnected() {
        return watchConnector.isAlreadyConnected();
    }

    /**
     * Connects the watch. Connection will not reconnect automatically. Connect must be called again
     * after connection is lost.
     *
     * @return {@link Single} that emits the mac address of the device if success or failure of the
     * connection
     */
    public Single<String> connect(@NonNull ConnectMetadata connectMetadata) {
        return watchConnector.connectWatch(suuntoBtDevice, this, connectMetadata);
    }

    /**
     * Disconnects the watch
     *
     * @return Completable that emits success or failure of the disconnection
     */
    public Completable disconnect() {
        return watchConnector.disconnectWatch(suuntoBtDevice);
    }

    public Completable destroyBleDevice() {
        return watchConnector.destroyBleDevice(suuntoBtDevice);
    }

    /**
     * Forces BLE to disconnect if it is connected. This used only for special connectivity error
     * resolving.
     *
     * @return Completable for force disconnecting BLE.
     */
    public Completable forceBleDisconnect() {
        return watchConnector.forceBleDisconnect(suuntoBtDevice.getMacAddress(),
            false);
    }

    /**
     * Removes watch pairing
     *
     * @return Completable that emits success or failure of the unpairing
     */
    public Completable unpair() {
        return watchConnector.unpairWatch(suuntoBtDevice);
    }

    public Completable unpairPairDeviceFromPhone() {
        return watchConnector.unpairDeviceFromPhone(suuntoBtDevice);
    }

    /**
     * Gets the underlying SuuntoBtDevice
     *
     * @return Original SuuntoBtDevice
     */
    public SuuntoBtDevice getSuuntoBtDevice() {
        return suuntoBtDevice;
    }

    /**
     * @return WatchSynchronizer to use for syncing this SpartanBt
     */
    public abstract WatchSynchronizer getWatchSynchronizer();

    /**
     * Gets version of current GNSS file as a String
     *
     * @return {@link Single} that emits GNSS version
     */
    public Single<GpsTimestamp> getGNSSTimestamp() {
        return Single.error(new UnsupportedOperationException());
    }

    /**
     * Fetch trend data e.g. steps and energy
     *
     * @param timestamp timestamp for which to query trend data from
     * @return Single that emits trend data
     */
    public Single<MdsResponse> fetchTrendData(final long timestamp) {
        return Single.error(new UnsupportedOperationException());
    }

    public Single<MdsResponse> fetchRecoveryData(final long timestamp) {
        return Single.error(new UnsupportedOperationException());
    }

    /**
     * Fetch sleep samples from device.
     *
     * @param timestamp Timestamp.
     * @return all samples from device when timestamp=0. Otherwise returns only samples newer than
     * given timestamp.
     */
    public Single<MdsResponse> fetchSleepSamples(final long timestamp) {
        return Single.error(new UnsupportedOperationException());
    }

    /**
     * Gets format of GNSS as a String
     *
     * @return {@link Single} that emits GNSS format
     */
    public Single<GpsFormat> getGNSSFormat() {
        return Single.error(new UnsupportedOperationException());
    }

    /**
     * @return {@link Single} that emits currently selected navigation systems
     */
    public Single<List<NavigationSystem>> getNavigationSystems() {
        return Single.error(new UnsupportedOperationException());
    }

    /**
     * Provides MDS with an array of file paths with GPS fixes for specified GPS type
     *
     * @param navigationSystems navigation systems which are selected
     * @param gpsType           type of GPS for the connected watch
     * @return {@link Completable} that emits success or failure depending on the sync result
     */
    public Completable syncGpsFiles(List<NavigationSystem> navigationSystems,
        GpsFormatType gpsType) {
        return Completable.error(new UnsupportedOperationException());
    }

    /**
     * Fetch system events
     *
     * @return Mds response containing system events json body
     */
    public Single<MdsResponse> fetchSystemEvents() {
        return Single.error(new UnsupportedOperationException());
    }

    /**
     * Gets an {@link Observable} that will emit new value each time unsynced logs count changes in
     * the watch
     *
     * @return {@link Observable} emitting the amount of unsynchronized logs
     */
    public Observable<Integer> getUnsyncedMovesObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Observable<Integer> getWatchBusyObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Single<FirmwareInfoResult> getUploadedFirmwares() {
        return Single.error(new UnsupportedOperationException());
    }

    public Observable<FirmwareTransferStatus> getFirmwareTransferStatusObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Completable startFirmwareTransfer(String path) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable stopFirmwareTransfer(String path) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable requestFirmwareSelect(boolean forceUpdate, long packageId) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Single<FirmwareSuggestResult> suggestFirmware(String firmwareFileName) {
        return Single.error(new UnsupportedOperationException());
    }

    public Single<InstallSelectedFirmwareResponse> installSelectedFirmware(long packageId,
        boolean forceUpdate) {
        return Single.error(new UnsupportedOperationException());
    }

    public Completable setLegacyMSyncFlag(boolean mSyncFlag) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable getSettingsFile(String path) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setSettingsFile(String path) {
        return Completable.error(new UnsupportedOperationException());
    }

    /**
     * Gets an Obserable that emits a new notification reply when triggered on the watch
     */
    public Observable<Integer> getNotificationReplyObservable() {
        // todo
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setPredefinedReplies(@NonNull List<String> predefinedReplies) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setFirstDayOfTheWeek(@NonNull DayOfWeek firstDayOfWeek) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<FileListMdsResponse> getFileList(@NonNull String path) {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable getFile(@NonNull String deviceFilePath, @NonNull String localFilePath) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable putFile(@NonNull String localFilePath, @NonNull String deviceFilePath) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable deleteFile(@NonNull String deviceFilePath) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Observable<List<Integer>> getSavedWifiNetworkIndices() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<AccessPointCollectionElement> getSavedWifiNetwork(int index) {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<WifiResult> connectWifiNetwork(@NonNull String ssid, @Nullable String key) {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable forgetWifiNetwork(int index) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setWifiGeneralSettings(@NonNull String countryCode) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setOfflineMapsUrl(@NonNull String url) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setAuthToken(@NonNull String token) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable notifyAreaSelectionChanged() {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable notifyAreaUnderDownloadDeleted(@NonNull String areaId) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Observable<Boolean> getWifiEnabledObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setWifiEnabled(@NonNull Boolean enabled) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable enableInboxWifi() {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<Integer> numberOfAreas() {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<WifiResult> getWifiConnectionStatus() {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Observable<WifiResult> getWifiConnectionStatusObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable saveWifiNetwork(
        int index,
        @NonNull String ssid,
        @Nullable String key,
        @NonNull AccessPointSecurity security,
        @NonNull AccessPointEncryption encryption
    ) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<WifiResult> scanAvailableWifiNetworks() {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<WifiAP> getWifiNetwork(int index) {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Observable<BatteryLevel> getBatteryLevel() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<ChargingState> getChargingState() {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<UsbCableState> getUsbCableState() {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Observable<UsbCableState> getUsbCableStateObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable lockSportsApp(@NonNull String pluginId) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable unlockSportsApp(@NonNull String pluginId) {
        return Completable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<String> getZappPluginDirectory() {
        return Single.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setDebugLocationCoordinates(int latitude, int longitude) {
        return Completable.error(new UnsupportedOperationException());
    }

    /**
     * Gets the SpartanSettings object that allows easy access to getting or putting settings values
     * to the watch
     *
     * @return SpartanSettings object
     */
    @Nullable
    public SpartanSettings getSettings() {
        return null;
    }

    @Nullable
    public SpartanDeviceSettings getDeviceSettings() {
        return null;
    }

    @Nullable
    public OfflineMusicSettings getOfflineMusicSettings() {
        return null;
    }

    /**
     * Gets the ActivityDataHolder object that allows easy access to current values of 24/7 data on
     * the watch
     *
     * @return {@link ActivityDataHolder} object
     */
    public ActivityDataHolder getActivityDataHolder() {
        return null;
    }

    /**
     * Gets the SportModesDataHolderObject object that allows easy access to sport modes data on the
     * watch
     *
     * @return {@link SportModesDataHolder} object
     */
    public SportModesDataHolder getSportModesDataHolder() {
        return null;
    }

    /**
     * Generic SDS get to a URI. This method assumes given Uri to be relative uri to domain suunto,
     * meaning it is the last part of following uri format: suunto://{serial}/{uri}
     *
     * @param uri URI to get
     * @return Single which emits the body of response
     */
    public Single<String> get(@NonNull String uri) {
        return get(uri, MDS_CONTRACT_EMPTY);
    }

    /**
     * Generic SDS get to a URI. This method assumes given Uri to be relative uri to domain suunto,
     * meaning it is the last part of following uri format: suunto://{serial}/{uri}
     *
     * @param uri      URI to get
     * @param contract Parameters for the request
     * @return Single which emits the body of response
     */
    public Single<String> get(@NonNull String uri, @NonNull String contract) {
        return mdsRx.get(buildUri(uri), contract);
    }

    @NonNull
    public abstract Single<String> getLogbookJson();

    public abstract Completable getLogEntryJsonsToFiles(
        long entryId, SynchronizerStorage synchronizerStorage);

    protected Completable getLogEntryJsonsToFiles(
        Gson gson,
        long entryId,
        SynchronizerStorage synchronizerStorage) {
        return Single.fromCallable(() -> {
                if (!synchronizerStorage.ensureLogbookEntryStorage(getMacAddress(), entryId)) {
                    throw new Exception("Can not create logbook entry storage.");
                }
                RepositoryConfiguration configuration =
                    synchronizerStorage.getRepositoryConfiguration();
                String summaryPath = configuration.getLogbookEntrySummaryPath(getMacAddress(),
                    entryId);
                String samplesPath = configuration.getLogbookEntrySamplesPath(getMacAddress(),
                    entryId);
                return gson.toJson(new GetLogEntryJsonsToFileContract(summaryPath, samplesPath));
            })
            .flatMapCompletable(contract -> {
                String uri =
                    String.format(Locale.US, LOGBOOK_DATA_TO_FILES_URI, suuntoBtDevice.getSerial(),
                        entryId);
                return mdsRx.get(uri, contract)
                    .toCompletable();
            });
    }

    @NonNull
    public abstract Completable updateTimeZoneInfo();

    /**
     * Initializes bluetooth radio state and paired state
     *
     * @param bluetoothAdapter BluetoothAdapter to get BT state from
     */
    @SuppressLint("MissingPermission")
    private void initializeState(BluetoothAdapter bluetoothAdapter) {
        if (suuntoBtDevice.getDeviceType().isDataLayerDevice()) {
            setPaired(true);
        } else {
            if (NearbyDevicesUtilsKt.isNearbyDevicesPermissionGranted(context)) {
                BluetoothDevice device =
                    bluetoothAdapter.getRemoteDevice(suuntoBtDevice.getMacAddress());
                setPaired(device != null && device.getBondState() == BluetoothDevice.BOND_BONDED);
            } else {
                setPaired(false);
            }
        }
    }

    @Nullable
    public NotificationsDevice getNotificationsDevice() {
        return notificationsDevice;
    }

    /**
     * Build full URI for watch resource
     *
     * @param uri  The resource URI - omitting the serial in the beginning. The URI can have
     *             formatting placeholder e.g.: %s, %d
     * @param args The arguments to inject in the placeholders
     * @return The absolute URI to the resource
     */
    String buildUri(@NonNull String uri, Object... args) {
        return buildUri(getSerial(), uri, args);
    }

    static String buildUri(@NonNull String serial, @NonNull String uri, Object... args) {
        String deviceUriPrefix = MDS_SCHEME_PREFIX + serial;
        return String.format(Locale.US,
            (uri.startsWith("/") ? deviceUriPrefix + uri : deviceUriPrefix + "/" + uri),
            args);
    }

    /**
     * Gets serial of the device.
     *
     * @return Serial of the device
     */
    public String getSerial() {
        return suuntoBtDevice.getSerial();
    }

    /**
     * Gets mac address of the device.
     *
     * @return Mac address of the device
     */
    public String getMacAddress() {
        return suuntoBtDevice.getMacAddress();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        watchConnector.watchRemoved(getSuuntoBtDevice());
        mdsConnectedSubscription.unsubscribe();
        btStateMonitor.unRegisterListener(btStateListener);
    }

    /**
     * Analytics method. Has there been invalid packet(s) since last watch connect.
     *
     * @return True, if invalid packets detected.
     */
    public boolean isInvalidPacketDetectedAfterLastWatchConnect() {
        return watchConnector.isInvalidPacketDetectedAfterLastWatchConnect();
    }

    /**
     * Subscribe weather uri, if success it'll callback every 60 minutes.
     *
     * @return observable result.
     */
    public Observable<String> subscribeOpenWeatherObservable() {
        String uri =
            String.format(Locale.US, OPEN_WEATHER_SUBSCRIPTION_URI, getSerial());
        Timber.v("Subscribe weather, uri: %s", uri);
        return mdsRx.subscribe(uri, String.class, true);
    }

    /**
     * Sync Weather Info.
     */
    public Single<String> putWeatherInfo(String contract) {
        String uri = String.format(Locale.US, OPEN_WEATHER_INFO_URI, getSerial());
        return putWeatherContent(uri, contract, "putWeatherInfo");
    }

    /**
     * Sync current weather.
     *
     * @param contract Weather info.
     * @return Single result.
     */
    public Single<String> putCurrentWeather(String contract) {
        String uri = String.format(Locale.US, OPEN_WEATHER_CURRENT_URI, getSerial());
        return putWeatherContent(uri, contract, "putCurrentWeather");
    }

    /**
     * Sync forecast weather.
     *
     * @param contract Weather info.
     * @return Single result.
     */
    public Single<String> putForecastWeather(String contract, int index) {
        String uri = String.format(Locale.US, OPEN_WEATHER_FORECAST_URI, getSerial(), index);
        return putWeatherContent(uri, contract, "putForecastWeather");
    }

    /**
     * Sync daily weather.
     *
     * @param contract DailyWeather.
     * @return Single result.
     */
    public Single<String> putDailyWeather(String contract, int index) {
        String uri = String.format(Locale.US, OPEN_WEATHER_DAILY_URI, getSerial(), index);
        return putWeatherContent(uri, contract, "daily");
    }

    public Single<String> putDailyUvi(String contract, int index) {
        String uri = String.format(Locale.US, OPEN_WEATHER_DAILY_UVI_URI, getSerial(), index);
        return putWeatherContent(uri, contract, "dailyUvi");
    }

    public Single<String> putHourlyAqi(String contract, int index) {
        String uri = String.format(Locale.US, OPEN_WEATHER_HOURLY_AQI_URI, getSerial(), index);
        return putWeatherContent(uri, contract, "hourlyAqi");
    }

    /**
     * Sync time.
     *
     * @param contract mds contract.
     * @return Single result.
     */
    public Single<String> putSyncTime(String contract) {
        String uri = String.format(Locale.US, OPEN_WEATHER_SYNC_TIME_URI, getSerial());
        return putWeatherContent(uri, contract, "putSyncTime");
    }


    /**
     * Common method for weather sync.
     *
     * @param uri      mds uri.
     * @param contract mds contract.
     * @return Single result.
     */
    private Single<String> putWeatherContent(String uri, String contract, String from) {
        return mdsRx.put(uri, contract)
            .doOnSuccess(response -> Timber.v("%s onSuccess: %s", from, response))
            .doOnError(error -> Timber.w(error, "%s onError.", from));
    }

    /**
     * Subscribe voice feedback autolap uri, if success it'll callback when the watch sends the
     * message of autolap
     *
     * @return observable result.
     */
    public Observable<AutoLapMdsResponse> subscribeVoiceFeedbackAutoLapObservable() {
        String uri =
            String.format(Locale.US, VOICE_FEEDBACK_AUTOLAP_SUBSCRIPTION_URI, getSerial());
        Timber.v("Subscribe voice feedback autolap, uri: %s", uri);
        return mdsRx.subscribe(uri, AutoLapMdsResponse.class, true);
    }

    /**
     * Subscribe voice feedback heart rate uri, if success it'll callback when the watch sends the
     * message of heart rate
     *
     * @return observable result.
     */
    public Observable<HeartRateMdsResponse> subscribeVoiceFeedbackHeartRateObservable() {
        String uri =
            String.format(Locale.US, VOICE_FEEDBACK_HEARTRATE_SUBSCRIPTION_URI, getSerial());
        Timber.v("Subscribe voice feedback heart rate, uri: %s", uri);
        return mdsRx.subscribe(uri, HeartRateMdsResponse.class, true);
    }

    public Observable<Integer> subscribeVoiceFeedbackStatusObservable() {
        String uri =
            String.format(Locale.US, VOICE_FEEDBACK_STATUS_SUBSCRIPTION_URI, getSerial());
        Timber.v("Subscribe voice feedback status, uri: %s", uri);
        return mdsRx.subscribe(uri, Integer.class, true);
    }

    public Observable<Boolean> subscribeFindPhoneObservable() {
        String uri = String.format(Locale.US, FIND_PHONE_SUBSCRIPTION_URI, getSerial());
        Timber.v("Subscribe find phone, uri: %s", uri);
        return mdsRx.subscribe(uri, Boolean.class, false);
    }

    public Single<String> putFindPhoneState(String contract) {
        String uri = String.format(Locale.US, FIND_PHONE_STATE_URI, getSerial());
        Timber.v("put find phone state, uri: %s, %s", uri, contract);
        return mdsRx.put(uri, contract);
    }

    public Observable<Boolean> getNotificationEnabledObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Single<String> putNotificationEnabled(boolean enabled) {
        return Single.error(new UnsupportedOperationException());
    }

    public Single<Boolean> getNotificationEnabled() {
        return Single.error(new UnsupportedOperationException());
    }

    public Observable<MdsNotificationCategoryEnabled> getNotificationCategoryEnabledObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Single<String> putNotificationCategoryEnabled(
        MdsNotificationCategoryEnabled categoryEnabled
    ) {
        return Single.error(new UnsupportedOperationException());
    }

    public Completable setTrainingZoneTrainingData(@NonNull TrainingZoneSyncTraining training) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setZoneSenseBaselineValuesData(
        @NonNull ZoneSenseBaselineValuesSyncData baselineValues
    ) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setTrainingZoneProgressData(@NonNull TrainingZoneSyncProgress progress) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setTrainingZoneRecoveryData(@NonNull TrainingZoneSyncRecovery recovery) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setTrainingZoneThresholdsData(@NonNull TrainingZoneSyncThresholds thresholds) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setTrainingZoneExercisesData(@NonNull TrainingZoneSyncExercises exercises) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Observable<String> getCurrentWatchfaceId() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Single<String> getDefaultWatchfaceId() {
        return Single.error(new UnsupportedOperationException());
    }

    public Single<String> setCurrentWatchfaceId(String watchfaceId) {
        return Single.error(new UnsupportedOperationException());
    }

    public Observable<Boolean> subscribeWatchOtaCheckObservable() {
        String uri = String.format(Locale.US, WATCH_OTA_CHECK_SUBSCRIPTION_URI, getSerial());
        Timber.v("Subscribe watch ota check, uri: %s", uri);
        return mdsRx.subscribe(uri, Boolean.class, false);
    }

    public Single<String> updateOtaForceTag(String contract) {
        String uri = String.format(Locale.US, UPDATE_OTA_TAG, getSerial());
        return mdsRx.put(uri, contract);
    }

    public Completable updateOtaManualDownloadFlag() {
        return Completable.error(new UnsupportedOperationException());
    }

    public Observable<SetupPreferenceContract.StateInfo> subscribeSetupPreferenceMessage() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Single<SetupPreferenceContract.GetStateInfo> getSetupPreferenceState() {
        return Single.error(new UnsupportedOperationException());
    }

    public Completable setSetupPreferenceState(SetupPreferenceContract.PutStateInfo stateInfo) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setAppInfo(AppInfo appInfo) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setupWearDirection(WearDirection direction) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Observable<Boolean> subscribeOfflineMusicUpdate() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Single<LogFilesMdsResponse> getLogFiles() {
        return Single.error(new UnsupportedOperationException());
    }

    public Completable setUserGender(Gender gender) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setUserWeight(float weight) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setUserBirthYear(int year) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setUserHeight(float height) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setUserMaxHR(int maxHR) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setUserRestHR(int restHR) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Completable setUserUnitSystem(UnitSystem unitSystem) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Observable<UserProfileGenderItem> subscribeUserGenderSettingObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Observable<UserProfileHeightItem> subscribeUserHeightSettingObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Observable<UserProfileWeightItem> subscribeUserWeightSettingObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Observable<UserProfileBirthYearItem> subscribeUserBirthYearSettingObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Observable<UserProfileMaxHeartItem> subscribeUserMaxHRSettingObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    public Observable<UserProfileRestHeartItem> subscribeUserRestHRSettingObservable() {
        return Observable.error(new UnsupportedOperationException());
    }

    @NonNull
    public Completable setBESTrainingZoneSyncData(@NonNull BESTrainingZoneSyncData besTrainingZoneSyncData) {
        return Completable.error(new UnsupportedOperationException());
    }

    public Observable<Boolean> subscribeBESTrainingZoneSync() {
        return Observable.error(new UnsupportedOperationException());
    }
}
