package com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine

import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import com.stt.android.moshi.MoshiProvider
import com.suunto.connectivity.repository.RepositoryConfiguration
import com.suunto.connectivity.util.FileUtils
import com.suunto.connectivity.watch.WatchBt
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers
import okhttp3.OkHttpClient
import okhttp3.Request
import timber.log.Timber
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.util.concurrent.TimeUnit

@JsonClass(generateAdapter = true)
data class FirmwareUpdateStatus(
    @Json(name = "forcedUpdateGoingOn")
    val suggestInstallAfterTransfer: Boolean = false,
    @Json(name = "timestampBackendChecked")
    val timeStampBackendChecked: Long = 0,
    @<PERSON><PERSON>(name = "currentlyTransferredFirmwareVersion")
    val currentlyTransferredFirmwareVersion: String = "",
    @Json(name = "currentlyTransferredFirmwareVersionLog")
    val currentlyTransferredFirmwareVersionLog: String = "",
    @Json(name = "currentlyTransferredFirmwarePackageId")
    val currentlyTransferredFirmwarePackageId: Long? = null,
    @Json(name = "transferProgressPercentage")
    val transferProgressPercentage: Int? = 0,
    @Json(name = "timestampTransferStarted")
    val timeStampTransferStarted: Long? = 0,
)

class FirmwareFileUtils(
    private val watchBt: WatchBt,
    private val context: Context,
    private val repositoryConfiguration: RepositoryConfiguration
) {
    private val updateStatusAdapter =
        MoshiProvider.instance.adapter(FirmwareUpdateStatus::class.java)
    private var firmwareUpdateStatus: FirmwareUpdateStatus

    init {
        val firmwareUpdateStatusFile = File(
            context.filesDir,
            repositoryConfiguration.getFirmwareUpdateStatusFolder(watchBt.macAddress) +
                FIRMWARE_UPDATE_STATUS_JSON
        )
        firmwareUpdateStatus = if (firmwareUpdateStatusFile.exists()) {
            val json = FileUtils.readJsonFile(firmwareUpdateStatusFile)
            try {
                updateStatusAdapter.fromJson(json) ?: FirmwareUpdateStatus()
            } catch (Exception: Exception) {
                FirmwareUpdateStatus()
            }
        } else {
            FirmwareUpdateStatus()
        }
    }

    private fun saveFirmwareUpdateStatusToFile() {
        try {
            ensureFirmwareFoldersExist()
            if (!FileUtils().writeJsonTo(
                    context,
                    updateStatusAdapter.toJson(firmwareUpdateStatus),
                    repositoryConfiguration.getFirmwareUpdateStatusFolder(watchBt.macAddress) + FIRMWARE_UPDATE_STATUS_JSON
                )
            ) {
                throw IOException("Firmware checked timestamp not saved")
            }
        } catch (e: Exception) {
            Timber.e(e, "Saving firmware update status failed.")
        }
    }

    @Synchronized
    fun firmwareUpdateStarting(
        firmwareVersion: String,
        firmwarePackageId: Long?,
        firmwareVersionLog: String,
        suggestInstallAfterTransfer: Boolean
    ) {
        firmwareUpdateStatus =
            FirmwareUpdateStatus(
                suggestInstallAfterTransfer = suggestInstallAfterTransfer,
                timeStampBackendChecked = timestampBackendChecked(),
                currentlyTransferredFirmwareVersion = firmwareVersion,
                currentlyTransferredFirmwareVersionLog = firmwareVersionLog,
                timeStampTransferStarted = System.currentTimeMillis()
            )
        saveFirmwareUpdateStatusToFile()
        watchBt.setFirmwareUpdateStatus(true, 0)
        watchBt.setFirmwareVersionCurrentlyTransferred(firmwareVersion, firmwarePackageId, firmwareVersionLog)
    }

    @Synchronized
    fun firmwareUpdateProgress(progressPercentage: Int) {
        if (progressPercentage > 0) {
            firmwareUpdateStatus =
                firmwareUpdateStatus.copy(transferProgressPercentage = progressPercentage)
            saveFirmwareUpdateStatusToFile()
            watchBt.setFirmwareUpdateStatus(true, progressPercentage)
        } else {
            watchBt.setFirmwareUpdateStatus(true, firmwareUpdateStatus.transferProgressPercentage)
        }
    }

    @Synchronized
    fun firmwareUpdateFinished() {
        firmwareUpdateStatus =
            FirmwareUpdateStatus(timeStampBackendChecked = timestampBackendChecked())
        saveFirmwareUpdateStatusToFile()
        watchBt.setFirmwareFileSizeInBytes(0)
        watchBt.setFirmwareUpdateStatus(false, 0)
        watchBt.setFirmwareVersionCurrentlyTransferred("", null, "")
        deleteFirmwareFiles()
    }

    @Synchronized
    fun firmwareUpdateChecked() {
        firmwareUpdateStatus =
            firmwareUpdateStatus.copy(timeStampBackendChecked = System.currentTimeMillis())
        saveFirmwareUpdateStatusToFile()
    }

    fun currentlyTransferredFirmwareVersion(): String {
        return firmwareUpdateStatus.currentlyTransferredFirmwareVersion
    }

    fun currentlyTransferredFirmwareVersionLog(): String {
        return firmwareUpdateStatus.currentlyTransferredFirmwareVersionLog
    }

    fun timestampBackendChecked(): Long {
        return firmwareUpdateStatus.timeStampBackendChecked
    }

    fun getTransferProgressPercentage(): Int {
        return firmwareUpdateStatus.transferProgressPercentage ?: 0
    }

    fun transferTimeInSeconds(): Long? {
        return firmwareUpdateStatus.timeStampTransferStarted?.let {
            return TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis() - it)
        }
    }

    fun suggestInstallAfterTransfer(): Boolean {
        return firmwareUpdateStatus.suggestInstallAfterTransfer
    }

    private fun ensureFirmwareFoldersExist() {
        val firmwareUpdateStatusFolder = File(
            context.filesDir,
            repositoryConfiguration.getFirmwareUpdateStatusFolder(watchBt.macAddress)
        )
        if (!firmwareUpdateStatusFolder.exists() && !firmwareUpdateStatusFolder.mkdir()) {
            throw IOException("Firmware update status folder could not be created")
        }
        val firmwareFolder =
            File(context.filesDir, repositoryConfiguration.getFirmwareFolder(watchBt.macAddress))
        if (!firmwareFolder.exists() && !firmwareFolder.mkdir()) {
            throw IOException("Firmware folder could not be created")
        }
    }

    fun pathToCurrentFirmwareFile(): String? {
        val firmwareFolder =
            File(context.filesDir, repositoryConfiguration.getFirmwareFolder(watchBt.macAddress))
        return if (!firmwareFolder.exists()) {
            null
        } else {
            val files = firmwareFolder.list()
            if (!files.isNullOrEmpty()) {
                val firmwareFile = File(
                    context.filesDir,
                    repositoryConfiguration.getFirmwareFolder(watchBt.macAddress) + files[0]
                )
                firmwareFile.absolutePath
            } else {
                null
            }
        }
    }

    fun fileExistsAlready(fileName: String): Boolean {
        val firmwareFolder =
            File(context.filesDir, repositoryConfiguration.getFirmwareFolder(watchBt.macAddress))
        val file = File(firmwareFolder, fileName)
        return file.exists()
    }

    private fun deleteFirmwareFiles() {
        val firmwareFolder =
            File(context.filesDir, repositoryConfiguration.getFirmwareFolder(watchBt.macAddress))
        val files = firmwareFolder.listFiles()
        files?.forEach { file ->
            file.delete()
        }
    }

    /**
     * Carrying over [OkHttpClient] to support auth headers when requesting the file
     */
    fun downloadFirmwareFileIfNotLoaded(
        urlStr: String,
        okHttpClient: OkHttpClient
    ): Single<String> {
        return Single.fromCallable {
            ensureFirmwareFoldersExist()
            val firmwareFileFolder =
                repositoryConfiguration.getFirmwareFolder(watchBt.macAddress)
            val outputFilePath = firmwareFileFolder + parseFileName(urlStr)
            val firmwareFile = File(context.filesDir, outputFilePath)
            if (!firmwareFile.exists()) {
                deleteFirmwareFiles()
                Timber.d("Starting download from %s to %s", urlStr, outputFilePath)
                val request = Request.Builder().url(urlStr).build()
                val response = okHttpClient.newCall(request).execute()
                if (response.code != 200) {
                    Timber.e("Firmware download failed on error: $response.code")
                    throw RuntimeException("Invalid connect response")
                }
                FileOutputStream(firmwareFile).use { fos ->
                    response.body?.byteStream()?.use { inputStream ->
                        inputStream.copyTo(fos)
                    } ?: throw RuntimeException("Response missing body")
                }
                Timber.v("Firmware download complete!")
            }
            firmwareFile.absolutePath
        }
            .subscribeOn(Schedulers.io())
            .doOnError {
                Timber.w(it, "Firmware file load error. Delete partial file, if any.")
                deleteFirmwareFiles()
            }
    }

    @Synchronized
    fun copyFirmwareFileFromUri(macAddress: String, firmwareUri: Uri, fileName: String): String? {
        val firmwareFolder: String = repositoryConfiguration.getFirmwareFolder(macAddress)
        deleteFirmwareFiles()
        val folder = File(context.filesDir, firmwareFolder)
        if (!(folder.exists() || folder.mkdirs())) {
            return null
        }

        val targetFile =
            File(context.filesDir, firmwareFolder + fileName)

        return copyFileFromUri(
            context = context,
            targetFile = targetFile,
            firmwareUri = firmwareUri
        )
    }

    companion object {
        private const val FIRMWARE_UPDATE_STATUS_JSON = "firmwareUpdateStatus.json"

        @JvmStatic
        fun parseFileName(url: String): String? {
            val nameStartPosition = url.lastIndexOf("/") + 1
            return if (nameStartPosition < url.length) {
                url.substring(nameStartPosition)
            } else {
                null
            }
        }

        /**
         * Parse firmware package ID from URI or path.
         */
        @JvmStatic
        fun firmwarePackageId(firmwareUriOrPath: String): Long? {
            val fileName =
                parseFileName(firmwareUriOrPath)
            return fileName?.let {
                val idStartPosition = it.lastIndexOf('-')
                if (idStartPosition > 0) {
                    try {
                        it.substring(idStartPosition + 1, fileName.lastIndexOf('.')).toLong(16)
                    } catch (e: Exception) {
                        Timber.w(e, "Error in resolving the package id.")
                        null
                    }
                } else {
                    null
                }
            }
        }

        @JvmStatic
        fun resolveFileNameFromUri(uri: Uri, context: Context): String? {
            var result: String? = null
            if (uri.scheme == "content") {
                context.contentResolver.query(
                    uri,
                    null,
                    null,
                    null,
                    null
                ).use { cursor ->
                    if (cursor != null && cursor.moveToFirst()) {
                        val displayNameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                        if (displayNameIndex >= 0) result = cursor.getString(displayNameIndex)
                    }
                }
            } else if (uri.scheme == "file") {
                result = uri.lastPathSegment
            } else if (uri.scheme == "http" || uri.scheme == "https") {
                result = parseFileName(uri.toString())
            }
            return result
        }

        @JvmStatic
        fun copyFileFromUri(context: Context, targetFile: File, firmwareUri: Uri): String? {
            val sourceStream: InputStream? = try {
                context.contentResolver.openInputStream(firmwareUri)
            } catch (e: FileNotFoundException) {
                Timber.e(e, "Firmware file not found")
                return null
            }
            return if (sourceStream != null) {
                try {
                    FileUtils.copyFromStreamToFile(
                        sourceStream,
                        targetFile
                    )
                } catch (e: IOException) {
                    Timber.e(e, "Copying firmware file not succeeded")
                    return null
                }
                Timber.d(
                    "Firmware file created. File: %s size %d",
                    targetFile.absolutePath,
                    targetFile.length()
                )
                targetFile.absolutePath
            } else {
                null
            }
        }
    }
}
