package com.suunto.connectivity.repository

import android.annotation.SuppressLint
import android.content.Context
import com.google.gson.Gson
import com.movesense.mds.Mds
import com.suunto.connectivity.sdsmanager.MdsRx
import com.suunto.connectivity.util.ConfigFileCopier
import com.suunto.obi2.Obi2

@SuppressLint("StaticFieldLeak")
private var globalMds: Mds? = null

fun createMdsRx(context: Context, gson: Gson, configFileCopier: ConfigFileCopier): MdsRx {
    // Create Mds instance and Rx wrapper for it
    val mds = globalMds ?: run {
        // Copy necessary config files
        configFileCopier.initConfigFiles()
        // Create obi2 instance
        val obi2 = Obi2()
        Mds.builder().legacyHandler(obi2).build(context.applicationContext)
    }.also {
        globalMds = it
    }
    return MdsRx(mds, gson)
}
