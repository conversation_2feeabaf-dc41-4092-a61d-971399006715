package com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine

import android.net.Uri
import androidx.core.net.toUri
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.domain.firmware.Version
import com.suunto.connectivity.firmware.FirmwareInformationInterface
import com.suunto.connectivity.firmware.WatchFirmwareInfo
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.repository.commands.CheckForOtaUpdatesResponse
import com.suunto.connectivity.repository.stateMachines.base.State
import com.suunto.connectivity.repository.stateMachines.base.Transition
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.FirmwareFileUtils.Companion.firmwarePackageId
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.sync.SynchronizerStorage
import com.suunto.connectivity.util.SupportedDevices
import com.suunto.connectivity.util.downloadUpdateStartedProperties
import com.suunto.connectivity.util.updateCheckAnalyticsProperties
import com.suunto.connectivity.watch.WatchBt
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoResult
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareInfoState
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareSuggestResult
import com.suunto.connectivity.watch.firmwareTransfer.Suggest
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.Single
import io.reactivex.disposables.Disposable
import io.reactivex.rxkotlin.zipWith
import okhttp3.OkHttpClient
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * Resolves the need for firmware update and downloads the firmware if not yet downloaded.
 * Will fire the following state machine triggers.
 * - TransferFirmwareFile
 * - NewFirmwareCheckSkipped
 * - NewFirmwareAlreadyInWatch
 * - NewFirmwareCheckFailed
 */
class CheckNeedForFirmwareUpdateState(
    private val watchBt: WatchBt,
    private val firmwareInformationInterface: FirmwareInformationInterface,
    private val firmwareFileUtils: FirmwareFileUtils,
    private val synchronizerStorage: SynchronizerStorage,
    private val supportedDevices: SupportedDevices,
    private val stOkHttpClient: OkHttpClient
) : State("CheckFirmwareState") {
    private var findFirmwareDisposable: Disposable? = null

    override fun onEntry(transition: Transition?) {
        super.onEntry(transition)

        val currentTime = System.currentTimeMillis()
        val lastChecked = firmwareFileUtils.timestampBackendChecked()
        val pathToCurrentFirmwareFile = firmwareFileUtils.pathToCurrentFirmwareFile()

        // Continue loading of existing firmware file or skip check if firmware check has been done recently.
        if (!pathToCurrentFirmwareFile.isNullOrEmpty()) {
            triggerNext(Triggers.TransferFirmwareFile, pathToCurrentFirmwareFile)
        } else {
            // Always check for new firmware if forced_update capability is found.
            if (!supportedDevices.requiresForcedUpdate(watchBt) && TimeUnit.MILLISECONDS.toHours(
                    currentTime - lastChecked
                ) <= 24
            ) {
                // No need to check for new firmware, because the last check is quite recent.
                triggerNext(Triggers.NewFirmwareCheckSkipped)
                return
            }
        }
        // Check for new firmware
        watchBt.currentState.deviceInfo?.let { _ ->
            findFirmwareDisposable =
                checkNewFirmware(
                    watchBt,
                    firmwareInformationInterface,
                )
                    .flatMap { (suggestResult, latestFirmwareInServer) ->
                        Suggest.getByValue(suggestResult.suggest)?.let { suggest ->
                            handleSuggest(suggest, latestFirmwareInServer)
                        } ?: Single.error(Exception("Unknown suggest result: $suggestResult"))
                    }
                    .subscribe({ (trigger, filename) ->
                        firmwareFileUtils.firmwareUpdateChecked()
                        triggerNext(trigger, filename)
                    }, {
                        Timber.w(it, "Checking the firmware failed")
                        sendUpdateCheckAnalytics(
                            result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.ERROR,
                            errorReason = it.toString()
                        )
                        triggerNext(Triggers.NewFirmwareCheckFailed)
                    })
        } ?: errorDeviceInfoMissing()
    }

    private fun errorDeviceInfoMissing() {
        sendUpdateCheckAnalytics(
            result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.ERROR,
            errorReason = "Device information missing"
        )
        triggerNext(Triggers.NewFirmwareCheckFailed)
    }

    /**
     * Handles suggest result. Downloads the firmware from server if needed and sends the analytics.
     * Returns a pair containing next state machine trigger and path to firmware file.
     * Path to firmware file is empty if new firmware is not found or check fails.
     */
    private fun handleSuggest(
        suggest: Suggest,
        latestFirmwareOnServer: WatchFirmwareInfo
    ): Single<Pair<Triggers, String>> {
        return when (suggest) {
            Suggest.DUPLICATE -> {
                // Todo:Upload firmwares can be removed after suggest API returns "CURRENT" for the current firmware in use.
                // Keeping it for now as there still are firmwares not returning CURRENT but DUPLICATE.
                RxJavaInterop.toV2Single(watchBt.uploadedFirmwares)
                    .doOnSuccess { firmwaresInDevice ->
                        if (isCurrentFirmware(
                                watchBt.currentState.deviceInfo,
                                latestFirmwareOnServer,
                                firmwaresInDevice
                            )
                        ) {
                            sendUpdateCheckAnalytics(
                                result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.WATCH_UP_TO_DATE
                            )
                        } else {
                            sendUpdateCheckAnalytics(
                                result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_ALREADY_DOWNLOADED
                            )
                        }
                    }
                    .flatMap {
                        if (supportedDevices.requiresForcedUpdate(watchBt) &&
                            !latestFirmwareOnServer.existInDeviceFirmwares(
                                watchBt.currentState.deviceInfo,
                                it
                            )
                        ) {
                            // This can happen while testing, if watch had the latest FW before
                            // transferring orca inbox FW to the watch. Forced update capability is found and
                            // we know for sure that we want to transfer the latest FW from server.
                            Timber.d("Forced update required, latest fw from server :${latestFirmwareOnServer.latestFirmwareURI}")
                            handleTransferFirmwareFile(latestFirmwareOnServer)
                        } else {
                            Single.just(Triggers.NewFirmwareAlreadyInWatch to "")
                        }
                    }
                    .onErrorReturn {
                        // Error in current firmware check is not considered fatal as it only
                        // impacts on check for updates result sent to analytics.
                        sendUpdateCheckAnalytics(
                            result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_ALREADY_DOWNLOADED
                        )
                        Triggers.NewFirmwareAlreadyInWatch to ""
                    }
            }

            Suggest.COMPATIBLE -> {
                sendUpdateCheckAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_FOUND_BUT_NOT_RECOMMENDED
                )
                Single.just(Triggers.NewFirmwareCheckSkipped to "")
            }

            Suggest.RECOMMENDED -> {
                if (!supportedDevices.requiresForcedUpdate(watchBt) && deviceAutomaticUpdatesDisabled()) {
                    Timber.d("Recommended update found but user has disabled automatic updates")
                    return Single.just(Triggers.NewFirmwareCheckSkipped to "")
                }
                handleTransferFirmwareFile(latestFirmwareOnServer)
            }

            Suggest.INCOMPATIBLE -> {
                Timber.d("New firmware file skipped. Update incompatible")
                sendUpdateCheckAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.ERROR,
                    errorReason = "Update incompatible"
                )
                Single.just(Triggers.NewFirmwareCheckSkipped to "")
            }

            Suggest.INVALID -> {
                Timber.d("New firmware file skipped. Update incompatible")
                sendUpdateCheckAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.ERROR,
                    errorReason = "Update invalid"
                )
                Single.just(Triggers.NewFirmwareCheckSkipped to "")
            }

            Suggest.CURRENT -> {
                sendUpdateCheckAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.WATCH_UP_TO_DATE
                )
                Single.just(Triggers.NewFirmwareAlreadyInWatch to "")
            }
        }
    }

    private fun handleTransferFirmwareFile(latestFirmwareOnServer: WatchFirmwareInfo): Single<Pair<Triggers, String>> {
        return firmwareFileUtils.downloadFirmwareFileIfNotLoaded(
            latestFirmwareOnServer.latestFirmwareURI,
            stOkHttpClient
        )
            .map {
                Triggers.TransferFirmwareFile to it
            }
            .doOnSuccess {
                firmwareFileUtils.firmwareUpdateStarting(
                    firmwareVersion = latestFirmwareOnServer.latestFirmwareVersion,
                    firmwarePackageId = firmwarePackageId(latestFirmwareOnServer.latestFirmwareURI),
                    firmwareVersionLog = latestFirmwareOnServer.versionLog,
                    suggestInstallAfterTransfer = false
                )
                sendUpdateCheckAnalytics(
                    result = AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.UPDATE_FOUND
                )
                sendDownloadUpdateStartedAnalytics(
                    latestFirmwareOnServer.latestFirmwareVersion,
                    watchBt.currentState.deviceInfo
                )
            }
    }

    private fun triggerNext(trigger: Triggers, fileName: String? = null) {
        if (fileName.isNullOrEmpty()) {
            stateMachine().fire(trigger)
        } else {
            if (trigger == Triggers.TransferFirmwareFile) {
                watchBt.setFirmwareVersionCurrentlyTransferred(
                    firmwareFileUtils.currentlyTransferredFirmwareVersion(),
                    firmwarePackageId(fileName),
                    firmwareFileUtils.currentlyTransferredFirmwareVersionLog(),
                )
            }
            stateMachine().fire(trigger, fileName, String::class.java)
        }
    }

    override fun onExit(transition: Transition?) {
        super.onExit(transition)
        findFirmwareDisposable?.dispose()
        findFirmwareDisposable = null
    }

    private fun sendUpdateCheckAnalytics(
        @AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.Value result: String,
        errorReason: String? = null
    ) {
        val properties = updateCheckAnalyticsProperties(
            mdsDeviceInfo = watchBt.currentState.deviceInfo,
            result = result,
            method = AnalyticsPropertyValue.WatchManagementUpdateCheckMethod.AUTOMATIC,
            errorReason = errorReason,
            isDowngrade = false,
        )
        AnalyticsUtils.sendAnyEvent(
            AnalyticsEvent.WATCH_MANAGEMENT_CHECK_FOR_UPDATES,
            properties
        )
    }

    private fun sendDownloadUpdateStartedAnalytics(
        newVersion: String,
        mdsDeviceInfo: MdsDeviceInfo?
    ) {
        AnalyticsUtils.sendAnyEvent(
            AnalyticsEvent.WATCH_MANAGEMENT_DOWNLOAD_UPDATE_STARTED,
            downloadUpdateStartedProperties(newVersion, mdsDeviceInfo, false)
        )
    }

    private fun deviceAutomaticUpdatesDisabled(): Boolean {
        val deviceInfo = watchBt.currentState.deviceInfo
        return if (deviceInfo != null) {
            val userSettings = synchronizerStorage.readUserSettingsToWatch()
            val automaticUpdateDisabledWatches =
                userSettings?.automaticUpdateDisabledWatches ?: emptyList()
            automaticUpdateDisabledWatches.contains(deviceInfo.serial)
        } else {
            true
        }
    }

    companion object {
        fun checkNewFirmware(
            watchBt: WatchBt,
            firmwareInformationInterface: FirmwareInformationInterface,
        ): Single<Pair<FirmwareSuggestResult, WatchFirmwareInfo>> {
            return watchBt.currentState.deviceInfo?.let { mdsDeviceInfo ->
                val isSupportOtaUpdateCheck =
                    watchBt.suuntoBtDevice.deviceType.supportOtaUpdateCheck(mdsDeviceInfo)
                firmwareInformationInterface.getLatestFirmwareInfo(
                    mdsDeviceInfo = mdsDeviceInfo,
                    isSupportOtaUpdateCheck = isSupportOtaUpdateCheck
                ).flatMap { latestFirmwareInServer ->
                    otaUpdateCheck(watchBt, latestFirmwareInServer, isSupportOtaUpdateCheck)
                        .flatMap { updatedFirmware ->
                            // When the current watch version is up-to-date, the api does not
                            // return any information (e.g., latestFirmwareURI),
                            // but still needs to show the version logs
                            if (updatedFirmware.latestFirmwareURI.isEmpty() && updatedFirmware.latestFirmwareVersion == mdsDeviceInfo.swVersion) {
                                return@flatMap Single.just(FirmwareSuggestResult(Suggest.CURRENT.value) to updatedFirmware)
                            }
                            val firmwareUri = firmwareDescriptor(updatedFirmware.latestFirmwareURI)
                            if (firmwareUri != null) {
                                RxJavaInterop.toV2Single(watchBt.suggestFirmware(firmwareUri))
                                    .map { suggestResult ->
                                        suggestResult to updatedFirmware
                                    }
                            } else {
                                Single.error(Throwable("Descriptor could not be parsed from URI"))
                            }
                        }
                }
            } ?: Single.error(Throwable("Mds device info does not exist"))
        }

        private fun otaUpdateCheck(
            watchBt: WatchBt,
            firmware: WatchFirmwareInfo,
            isSupportOtaUpdateCheck: Boolean
        ): Single<WatchFirmwareInfo> {
            return if (!isSupportOtaUpdateCheck) {
                Single.just(firmware)
            } else {
                Timber.d("support otaUpdateCheck")
                RxJavaInterop.toV2Single(
                    watchBt.updateOtaForceTag(firmware.forceUpdateToContract())
                        .map {
                            firmware
                        }
                )
            }
        }

        private fun checkDeepLinkFirmware(
            watchBt: WatchBt,
            deepLinkFirmware: Uri?
        ): Single<Pair<FirmwareSuggestResult, WatchFirmwareInfo>> {
            return deepLinkFirmware?.toString()?.let { firmwarePath ->
                firmwareDescriptor(firmwarePath)?.let { firmwareDescriptor ->
                    RxJavaInterop.toV2Single(watchBt.suggestFirmware(firmwareDescriptor))
                        .map { suggestResult ->
                            suggestResult to WatchFirmwareInfo(
                                deviceName = watchBt.suuntoBtDevice.name,
                                firmwareUploadDate = "",
                                latestFirmwareVersion = firmwareDescriptor,
                                version = "",
                                versionLog = "",
                                latestFirmwareURI = firmwarePath,
                                releaseType = null
                            )
                        }
                }
            } ?: Single.error(Throwable("Firmware path not resolved from URI"))
        }

        @JvmStatic
        fun checkForUpdates(
            watchBt: WatchBt,
            firmwareInformationInterface: FirmwareInformationInterface,
            deepLinkFirmware: Uri?,
        ): Single<CheckForOtaUpdatesResponse> {
            val checkFirmwareSingle = if (deepLinkFirmware == null) {
                checkNewFirmware(
                    watchBt,
                    firmwareInformationInterface,
                )
            } else {
                checkDeepLinkFirmware(watchBt, deepLinkFirmware)
            }

            return checkFirmwareSingle
                // Todo:Upload firmwares can be removed after suggest API returns "CURRENT" for the current firmware in use.
                // Keeping it for now as there still are firmwares not returning CURRENT but DUPLICATE.
                .zipWith(RxJavaInterop.toV2Single(watchBt.uploadedFirmwares))
                .map { (checkNewFirmwarePair, firmwaresInWatch) ->
                    val (suggestResult, latestFirmwareInServer) = checkNewFirmwarePair
                    val isDowngrade =
                        isDowngrade(watchBt.currentState.deviceInfo, latestFirmwareInServer)

                    val descriptor = firmwareDescriptor(latestFirmwareInServer.latestFirmwareURI)
                    val packageId = firmwarePackageId(latestFirmwareInServer.latestFirmwareURI)
                    Timber.d("Firmware in server: $descriptor")
                    for (firmware in firmwaresInWatch.content.data) {
                        Timber.d("${firmware.metadata.descriptor} State: ${firmware.state}")
                    }

                    if (isCurrentFirmware(
                            watchBt.currentState.deviceInfo,
                            latestFirmwareInServer,
                            firmwaresInWatch
                        )
                    ) {
                        Timber.d("Firmware up to date: $descriptor")
                        CheckForOtaUpdatesResponse.deviceUpToDate(
                            versionLog = latestFirmwareInServer.versionLog
                        )
                    } else {
                        Suggest.getByValue(suggestResult.suggest)?.let { suggest ->
                            when (suggest) {
                                Suggest.DUPLICATE -> {
                                    val fwInWatch = firmwaresInWatch
                                        .content
                                        .data
                                        .firstOrNull { it.metadata.descriptor == descriptor }
                                    val isAlreadySelected =
                                        fwInWatch?.state == FirmwareInfoState.SELECTED.value

                                    if (isDowngrade) {
                                        if (isAlreadySelected) {
                                            Timber.d("Downgrade waiting to be installed")
                                            CheckForOtaUpdatesResponse.downgradeWaitingToBeInstalled(
                                                newFirmwareVersion = latestFirmwareInServer.latestFirmwareVersion,
                                                newFirmwarePackageId = packageId,
                                                newFirmwareVersionLog = latestFirmwareInServer.versionLog,
                                            )
                                        } else {
                                            Timber.d("Downgrade already on watch needs confirmation before installing")
                                            CheckForOtaUpdatesResponse.downgradeReadyToInstallNeedsConfirmation(
                                                newFirmwareVersion = latestFirmwareInServer.latestFirmwareVersion,
                                                newFirmwarePackageId = packageId,
                                                newFirmwareVersionLog = latestFirmwareInServer.versionLog,
                                            )
                                        }
                                    } else {
                                        if (isAlreadySelected) {
                                            Timber.d("Update waiting to be installed")
                                            CheckForOtaUpdatesResponse.updateWaitingToBeInstalled(
                                                newFirmwareVersion = latestFirmwareInServer.latestFirmwareVersion,
                                                newFirmwarePackageId = packageId,
                                                newFirmwareVersionLog = latestFirmwareInServer.versionLog,
                                            )
                                        } else {
                                            Timber.d("Update already on watch needs confirmation before installing")
                                            CheckForOtaUpdatesResponse.updateReadyToInstallNeedsConfirmation(
                                                newFirmwareVersion = latestFirmwareInServer.latestFirmwareVersion,
                                                newFirmwarePackageId = packageId,
                                                newFirmwareVersionLog = latestFirmwareInServer.versionLog,
                                            )
                                        }
                                    }
                                }

                                Suggest.RECOMMENDED,
                                Suggest.COMPATIBLE -> {
                                    if (isDowngrade) {
                                        Timber.d("Firmware downgrade available: ${latestFirmwareInServer.latestFirmwareVersion}")
                                        CheckForOtaUpdatesResponse.downgradeAvailable(
                                            newFirmwareVersion = latestFirmwareInServer.latestFirmwareVersion,
                                            newFirmwarePackageId = packageId,
                                            newFirmwareVersionLog = latestFirmwareInServer.versionLog,
                                            firmwareFileUri = latestFirmwareInServer.latestFirmwareURI.toUri()
                                        )
                                    } else {
                                        Timber.d("Firmware update available: ${latestFirmwareInServer.latestFirmwareVersion}")
                                        CheckForOtaUpdatesResponse.updateAvailable(
                                            newFirmwareVersion = latestFirmwareInServer.latestFirmwareVersion,
                                            newFirmwarePackageId = packageId,
                                            newFirmwareVersionLog = latestFirmwareInServer.versionLog,
                                            firmwareFileUri = latestFirmwareInServer.latestFirmwareURI.toUri(),
                                        )
                                    }
                                }

                                Suggest.INCOMPATIBLE,
                                Suggest.INVALID -> {
                                    Timber.d("Firmware update not accepted: ${suggestResult.suggest}")
                                    CheckForOtaUpdatesResponse.error("Firmware not accepted")
                                }

                                Suggest.CURRENT -> {
                                    Timber.d("Firmware up to date: $descriptor")
                                    CheckForOtaUpdatesResponse.deviceUpToDate(
                                        versionLog = latestFirmwareInServer.versionLog
                                    )
                                }
                            }
                        } ?: CheckForOtaUpdatesResponse.error("Suggest result not recognised")
                    }
                }.doOnError {
                    Timber.w(it, "checkForUpdates failed")
                }
        }

        /**
         * Parse firmware descriptor from URI to the format known by device.
         */
        @JvmStatic
        fun firmwareDescriptor(firmwareUri: String): String? {
            val fileName =
                FirmwareFileUtils.parseFileName(firmwareUri)
            return fileName?.let {
                if (it.lastIndexOf('.') > 0) {
                    it.substring(0, fileName.lastIndexOf('.'))
                } else {
                    null
                }
            }
        }

        /**
         * Check if the [firmwareToCheck] is an older firmware than what is currently in the watch.
         * If the information can't be parsed from the arguments, defaults to false
         */
        @JvmStatic
        fun isDowngrade(deviceInfo: MdsDeviceInfo?, firmwareToCheck: WatchFirmwareInfo): Boolean {
            val toCheckVersion =
                Version.fromWatchFirmwareName(firmwareToCheck.latestFirmwareVersion)

            return if (deviceInfo != null && toCheckVersion != null) {
                // Keep only major minor patch to match Version.fromWatchFirmwareName
                toCheckVersion < Version(deviceInfo.sw, maxVersionComponentsToKeep = 3)
            } else {
                false
            }
        }

        private fun isCurrentFirmware(
            deviceInfo: MdsDeviceInfo?,
            latestFirmwareOnServer: WatchFirmwareInfo,
            firmwaresInWatch: FirmwareInfoResult
        ): Boolean {
            // 1. First check uploaded firmwares(CURRENT and UPDATED state) info from /Update/Info watch resource
            val firmwareDescriptorFromServer =
                firmwareDescriptor(latestFirmwareOnServer.latestFirmwareURI)
            val currentAndUpdatedFirmwares = firmwaresInWatch.getCurrentAndUpdatedFirmwares()
            if (currentAndUpdatedFirmwares.isNotEmpty()) {
                Timber.d("Latest fw from server: $firmwareDescriptorFromServer")
                return currentAndUpdatedFirmwares.any { it.metadata.descriptor == firmwareDescriptorFromServer }
            }

            // 2. Check fw version from MdsDeviceInfo
            return latestFirmwareOnServer.isDeviceVersion(deviceInfo)
        }

        private fun WatchFirmwareInfo.isDeviceVersion(deviceInfo: MdsDeviceInfo?): Boolean {
            val fwInServer = Version.fromWatchFirmwareName(latestFirmwareVersion)
            return if (deviceInfo != null && fwInServer != null) {
                // Keep only major minor patch to match Version.fromWatchFirmwareName
                val fwInWatch = Version(deviceInfo.sw, maxVersionComponentsToKeep = 3)
                Timber.d("Compare fw in server: $fwInServer to fw in watch: $fwInWatch")
                fwInServer == fwInWatch
            } else {
                false
            }
        }

        private fun WatchFirmwareInfo.existInDeviceFirmwares(
            deviceInfo: MdsDeviceInfo?,
            firmwaresInWatch: FirmwareInfoResult
        ): Boolean {
            // 1. First check uploaded firmwares info from /Update/Info watch resource
            val firmwareDescriptorFromServer = firmwareDescriptor(latestFirmwareURI)
            val existInFirmwares = firmwaresInWatch.content.data.any {
                it.metadata.descriptor == firmwareDescriptorFromServer
            }

            // 2. Check fw version from MdsDeviceInfo
            val isCurrentVersion = isDeviceVersion(deviceInfo)

            return existInFirmwares || isCurrentVersion
        }
    }
}
