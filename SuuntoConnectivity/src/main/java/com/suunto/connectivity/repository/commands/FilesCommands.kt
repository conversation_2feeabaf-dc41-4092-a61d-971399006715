package com.suunto.connectivity.repository.commands

import android.annotation.SuppressLint
import androidx.annotation.RestrictTo
import com.suunto.connectivity.repository.SuuntoRepositoryService
import kotlinx.parcelize.Parcelize

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetFileListQuery(
    override val macAddress: String,
    val path: String
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_FILE_LIST
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetFileListResponse(
    val files: List<String>,
    val dirs: List<String>
) : Response

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class GetFileQuery(
    override val macAddress: String,
    val deviceFilePath: String,
    val localFilePath: String
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_FILE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class PutFileQuery(
    override val macAddress: String,
    val localFilePath: String,
    val deviceFilePath: String
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_PUT_FILE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@SuppressLint("ParcelCreator")
@Parcelize
data class DeleteFileQuery(
    override val macAddress: String,
    val deviceFilePath: String
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_DELETE_FILE
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetLogFilesQuery(
    override val macAddress: String,
) : DeviceSpecificQuery {
    override val messageType: Int
        get() = SuuntoRepositoryService.MSG_GET_LOG_FILES
}

@RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
@Parcelize
data class GetLogFilesResponse(
    val list: List<String>
) : Response
