package com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine

import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.exceptions.extensions.messageWithCauses
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.repository.stateMachines.base.State
import com.suunto.connectivity.repository.stateMachines.base.Transition
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.CheckNeedForFirmwareUpdateState.Companion.firmwareDescriptor
import com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine.FirmwareFileUtils.Companion.firmwarePackageId
import com.suunto.connectivity.util.downloadUpdateFailureProperties
import com.suunto.connectivity.util.sendDownloadUpdateCompletedEvent
import com.suunto.connectivity.watch.WatchBt
import com.suunto.connectivity.watch.firmwareTransfer.Suggest
import rx.Completable
import rx.Subscription
import timber.log.Timber
import java.io.File

class TransferringFirmwareState(
    val watchBt: WatchBt,
    private val firmwareFileUtils: FirmwareFileUtils
) : State("TransferringFirmwareState") {
    private var subscription: Subscription? = null
    private var firmwarePath: String? = null

    override fun <TArg : Any?> onEntry(arg: TArg, transition: Transition?) {
        super.onEntry(arg, transition)
        Timber.d("TransferringState entry on trigger %s", transition?.trigger)
        if (arg is String) {
            watchBt.setFirmwareUpdateStatus(true, firmwareFileUtils.getTransferProgressPercentage())
            startFirmwareTransfer(arg)
        }
    }

    private fun startFirmwareTransfer(path: String) {
        firmwarePath = path
        subscription = if (watchBt.suuntoBtDevice.deviceType.isSuuntoRun) {
            watchBt.suggestFirmware(firmwareDescriptor(path))
                .map { Suggest.getByValue(it.suggest) }
                .onErrorReturn {
                    Timber.w(it, "Suggest firmware error, default is Suggest.RECOMMENDED")
                    Suggest.RECOMMENDED
                }
                .flatMapCompletable { suggest ->
                    Timber.d("Suggest firmware: ${firmwareDescriptor(path)}, ${suggest?.value}")
                    if (suggest == Suggest.DUPLICATE) {
                        Completable.complete() // Terminate the chain if it's a duplicate
                    } else {
                        startTransferStatusCompletable(path)
                    }
                }
        } else {
            // If it's not a SuuntoRun device, directly start the firmware transfer
            startTransferStatusCompletable(path)
        }
            .andThen(
                // Launch install UI on watch if needed
                if (firmwareFileUtils.suggestInstallAfterTransfer()) {
                    Timber.d("About to launch watch install UI.")
                    firmwarePackageId(path)?.let {
                        watchBt.requestFirmwareSelect(false, it)
                            .doOnError { e -> Timber.w(e, "failed to requestFirmwareSelect") }
                            .doOnCompleted { Timber.d("requestFirmwareSelect success") }
                            .onErrorComplete()
                    } ?: Completable.complete()
                } else {
                    Completable.complete()
                }
            ).doOnSubscribe {
                watchBt.setFirmwareFileSizeInBytes(File(path).length())
            }.subscribe({
                sendDownloadUpdateCompletedEvent(
                    newFirmwareVersion = watchBt.currentState.firmwareUpdateStatus.firmwareVersioCurrentlyTransferred,
                    mdsDeviceInfo = watchBt.currentState.deviceInfo,
                    timeFromTransferStartSeconds = firmwareFileUtils.transferTimeInSeconds(),
                    isDowngrade = null
                )
                firmwareFileUtils.firmwareUpdateFinished()
                stateMachine()?.fire(Triggers.TransferCompleted)
            }, {
                handleTransferError(it)
            })
    }

    private fun startTransferStatusCompletable(path: String): Completable {
        return watchBt.startFirmwareTransfer(path)
            // Transform to FirmwareTransferStartException if possible
            .onErrorResumeNext { throwable ->
                val firmwareStartException =
                    FirmwareTransferStartException.createIfKnownException(throwable)
                Completable.error(firmwareStartException ?: throwable)
            }
            .mergeWith(
                watchBt.firmwareTransferStatusObservable
                    .doOnNext { status ->
                        Timber.d("Firmware transfer status. %s", status.toString())
                        firmwareFileUtils.firmwareUpdateProgress(status.progressPercentage)
                        if (status.errorCode != null) {
                            // Generate FirmwareTransferException if possible.
                            throw FirmwareTransferException.createIfKnownException(
                                message = status.errorDetails,
                                errorCode = status.errorCode
                            )
                                ?: Exception(status.errorCode.toString() + ":" + status.errorDetails.orEmpty())
                        }
                    }
                    .filter { transferStatus -> transferStatus.completed }
                    .first()
                    .toCompletable()
            )
    }

    private fun handleTransferError(throwable: Throwable) {
        val temporaryError: Boolean
        val errorDescription: String
        when (throwable) {
            is FirmwareTransferStartException -> {
                errorDescription = throwable.messageWithCauses
                temporaryError =
                    when (throwable.error) {
                        FirmwareTransferStartException.ErrorCode.DEVICE_NOT_CONNECTED,
                        FirmwareTransferStartException.ErrorCode.DEVICE_IS_BUSY,
                        FirmwareTransferStartException.ErrorCode.FAILED_TO_COMMUNICATE_WITH_DEVICE ->
                            true

                        FirmwareTransferStartException.ErrorCode.IMAGE_FILE_NOT_SOF,
                        FirmwareTransferStartException.ErrorCode.REQUEST_URL_INVALID,
                        FirmwareTransferStartException.ErrorCode.INTERNAL_ERROR ->
                            false
                    }
            }

            is FirmwareTransferException -> {
                errorDescription = throwable.messageWithCauses
                temporaryError = when (throwable.error) {
                    FirmwareTransferException.ErrorCode.SERVICE_UNAVAILABLE ->
                        true

                    FirmwareTransferException.ErrorCode.BAD_REQUEST,
                    FirmwareTransferException.ErrorCode.INSUFFICIENT_STORAGE,
                    FirmwareTransferException.ErrorCode.INTERNAL_SERVER_ERROR ->
                        false
                }
            }

            else -> {
                Timber.e(throwable, "OTA update error not detected. Stopping update")
                errorDescription = "Not detected: $throwable"
                temporaryError = false
            }
        }

        if (temporaryError) {
            Timber.d("Temporary firmware transfer error: $errorDescription")
        } else {
            Timber.w(throwable, "Unrecoverable firmware transfer error. Finishing transfer.")
            AnalyticsUtils.sendAnyEvent(
                AnalyticsEvent.WATCH_MANAGEMENT_DOWNLOAD_UPDATE_FAILED,
                downloadUpdateFailureProperties(
                    // Sometimes watchBt.currentState is null, see:
                    // https://console.firebase.google.com/project/suunto-app/crashlytics/app/android:com.stt.android.suunto/issues/8a8a146d931a905acd136e763d5a969a?time=last-thirty-days&types=crash&sessionEventKey=6703852D00D6000179F0411F97DB2301_2001369981481640346
                    newFirmwareVersion = watchBt.currentState?.firmwareUpdateStatus?.firmwareVersioCurrentlyTransferred,
                    mdsDeviceInfo = watchBt.currentState?.deviceInfo,
                    failureReason = "Unrecoverable error: $errorDescription",
                    isDowngrade = null,
                )
            )
            firmwareFileUtils.firmwareUpdateFinished()
        }
        stateMachine()?.fire(Triggers.TransferError)
    }

    override fun onExit(transition: Transition?) {
        super.onExit(transition)
        Timber.d("TransferringState exit on trigger %s", transition?.trigger)
        subscription?.unsubscribe()
        if (transition?.trigger == Triggers.PrepareForForcedFirmwareTransfer ||
            transition?.trigger == Triggers.StopOtaUpdate ||
            transition?.trigger == Triggers.StopStateMachine
        ) {
            // In case forced updates is requested, on going transfer is stopped forcefully.
            firmwareFileUtils.firmwareUpdateFinished()
            // Todo: This should be done in onEntry method of next state
            watchBt.stopFirmwareTransfer(firmwarePath)
                .subscribe({}, {})
        }
    }
}
