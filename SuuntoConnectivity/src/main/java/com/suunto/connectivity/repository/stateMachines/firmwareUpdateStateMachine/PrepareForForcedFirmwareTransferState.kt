package com.suunto.connectivity.repository.stateMachines.firmwareUpdateStateMachine

import android.content.Context
import android.net.Uri
import com.suunto.connectivity.repository.commands.FirmwareTransferStartResponse
import com.suunto.connectivity.repository.stateMachines.base.State
import com.suunto.connectivity.repository.stateMachines.base.Transition
import com.suunto.connectivity.watch.WatchBt
import com.suunto.connectivity.watch.firmwareTransfer.FirmwareSuggestResult
import com.suunto.connectivity.watch.firmwareTransfer.Suggest
import hu.akarnokd.rxjava.interop.RxJavaInterop
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import okhttp3.OkHttpClient
import rx.subjects.PublishSubject
import timber.log.Timber
import io.reactivex.Single as SingleV2
import rx.Single as SingleV1

class PrepareForForcedFirmwareTransferState(
    private val watchBt: WatchBt,
    private val firmwareFileUtils: FirmwareFileUtils,
    private val context: Context,
    private val stOkHttpClient: OkHttpClient
) : State("PrepareForForcedFirmwareTransferState") {
    private val responseSubject: PublishSubject<FirmwareTransferStartResponse> =
        PublishSubject.create()
    private var disposable: Disposable? = null

    data class EntryParam(
        val uri: Uri,
        val firmwareVersion: String?
    )

    override fun <TArg : Any?> onEntry(arg: TArg, transition: Transition?) {
        super.onEntry(arg, transition)
        if (arg is EntryParam) {
            FirmwareFileUtils.resolveFileNameFromUri(arg.uri, context)?.let { fileName ->
                if (firmwareFileUtils.fileExistsAlready(fileName)) {
                    emitResponse(true, "Already force updating")
                } else {
                    // At this point, we won't continue on-going firmware update in any case.
                    firmwareFileUtils.firmwareUpdateFinished()
                    val descriptor = fileName.substring(0, fileName.lastIndexOf('.'))
                    disposable =
                        RxJavaInterop.toV2Single(watchBt.suggestFirmware(descriptor))
                            .flatMap {
                                handleSuggestApiResult(it, arg.uri, fileName)
                            }
                            .subscribeOn(Schedulers.io())
                            .subscribe({ (success, message) ->
                                if (success) {
                                    firmwareFileUtils.firmwareUpdateStarting(
                                        firmwareVersion = arg.firmwareVersion ?: "",
                                        firmwarePackageId = FirmwareFileUtils.firmwarePackageId(fileName),
                                        firmwareVersionLog = firmwareFileUtils.currentlyTransferredFirmwareVersionLog(),
                                        suggestInstallAfterTransfer = true
                                    )
                                }
                                emitResponse(success, message)
                            }, {
                                emitResponse(false, it.message ?: "Unknown reason")
                            })
                }
            } ?: emitResponse(false, "Unable to resolve file name")
        } else {
            emitResponse(false, "Wrong argument")
        }
    }

    private fun handleSuggestApiResult(
        firmwareSuggestResult: FirmwareSuggestResult,
        fileUri: Uri,
        fileName: String
    ): io.reactivex.Single<Pair<Boolean, String>> {
        return Suggest.getByValue(firmwareSuggestResult.suggest)?.let { suggest ->
            when (suggest) {
                Suggest.RECOMMENDED,
                Suggest.COMPATIBLE -> {
                    return if (fileUri.scheme == "content") {
                        SingleV2.fromCallable {
                            firmwareFileUtils.copyFirmwareFileFromUri(
                                watchBt.macAddress,
                                fileUri,
                                fileName
                            ) ?: throw Throwable("Unable to copy file")
                        }
                            .map {
                                Timber.d("Copy firmware $fileName from URL success")
                                true to ""
                            }
                            .onErrorReturn {
                                Timber.w(it, "Copy firmware $fileName from URL failed")
                                false to "Unable to copy file"
                            }
                    } else if (fileUri.scheme == "http" || fileUri.scheme == "https") {
                        firmwareFileUtils.downloadFirmwareFileIfNotLoaded(
                            fileUri.toString(),
                            stOkHttpClient
                        )
                            .map {
                                true to ""
                            }
                            .onErrorReturn {
                                false to "Unable to fetch OTA image from server."
                            }
                    } else {
                        SingleV2.just(false to "Unhandled URI scheme")
                    }
                }
                Suggest.DUPLICATE -> {
                    Timber.d("Copy firmware from file from URL success")
                    SingleV2.just(false to "Firmware already downloaded to watch")
                }
                Suggest.INCOMPATIBLE -> {
                    Timber.d("Firmware file incompatible")
                    SingleV2.just(false to "Firmware file incompatible")
                }
                Suggest.INVALID -> {
                    Timber.d("Firmware file invalid")
                    SingleV2.just(false to "Firmware file invalid")
                }
                Suggest.CURRENT -> {
                    Timber.d("Firmware file is current")
                    SingleV2.just(false to "Firmware file is current")
                }
            }
        } ?: suggestNotRecognised(firmwareSuggestResult.suggest)
    }

    private fun suggestNotRecognised(suggestValue: Int): SingleV2<Pair<Boolean, String>> {
        val message = "Unknown suggest result $suggestValue"
        Timber.d(message)
        return SingleV2.just(false to message)
    }

    private fun emitResponse(success: Boolean, message: String) {
        responseSubject.onNext(FirmwareTransferStartResponse(success, message))
        stateMachine().fire(Triggers.ForcedUpdatePrepared)
    }

    fun response(): SingleV1<FirmwareTransferStartResponse> {
        return responseSubject.first().toSingle()
    }

    override fun onExit(transition: Transition?) {
        super.onExit(transition)
        disposable?.dispose()
        disposable = null
    }
}
