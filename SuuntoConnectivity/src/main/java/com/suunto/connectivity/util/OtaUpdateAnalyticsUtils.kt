package com.suunto.connectivity.util

import com.stt.android.analytics.AnalyticsEvent.WATCH_MANAGEMENT_DOWNLOAD_UPDATE_COMPLETED
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsEventProperty.WATCH_MANAGEMENT_DOWNLOAD_DURATION
import com.stt.android.analytics.AnalyticsEventProperty.WATCH_MANAGEMENT_FAILURE_REASON
import com.stt.android.analytics.AnalyticsEventProperty.WATCH_MANAGEMENT_NEW_FIRMWARE_IS_DOWNGRADE
import com.stt.android.analytics.AnalyticsEventProperty.WATCH_MANAGEMENT_NEW_FIRMWARE_VERSION
import com.stt.android.analytics.AnalyticsEventProperty.WATCH_MANAGEMENT_UPDATE_FLOW_STATUS
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.analytics.toYesNo
import com.suunto.connectivity.repository.AnalyticsUtils
import com.suunto.connectivity.sdsmanager.model.MdsDeviceInfo
import com.suunto.connectivity.suuntoconnectivity.device.AnalyticsDevicePropertyHelper.getWatchModelNameForVariantName

fun updateCheckAnalyticsProperties(
    mdsDeviceInfo: MdsDeviceInfo?,
    @AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.Value result: String,
    @AnalyticsPropertyValue.WatchManagementUpdateCheckMethod.Value method: String,
    errorReason: String? = null,
    isDowngrade: Boolean? = null,
): AnalyticsProperties {
    return AnalyticsProperties().apply {
        put(
            AnalyticsEventProperty.WATCH_MANAGEMENT_UPDATE_CHECK_METHOD,
            method
        )
        put(
            AnalyticsEventProperty.RESULT,
            result
        )
        isDowngrade?.let {
            put(AnalyticsEventProperty.WATCH_MANAGEMENT_FIRMWARE_DOWNGRADE_SUGGESTED, it.toYesNo())
        }
        if (result == AnalyticsPropertyValue.WatchManagementCheckForUpdatesResult.ERROR) {
            errorReason?.let {
                put(
                    AnalyticsEventProperty.ERROR_REASON,
                    errorReason
                )
            }
        }
        addDeviceProperties(this, mdsDeviceInfo)
    }
}

private fun addDeviceProperties(
    analyticsProperties: AnalyticsProperties,
    mdsDeviceInfo: MdsDeviceInfo?
) {
    mdsDeviceInfo?.let {
        analyticsProperties
            .put(
                AnalyticsEventProperty.SUUNTO_WATCH_MODEL,
                getWatchModelNameForVariantName(it.variant)
            )
            .put(AnalyticsEventProperty.SUUNTO_WATCH_FIRMWARE_VERSION, it.swVersion)
    }
}

fun downloadUpdateStartedProperties(
    newFirmwareVersion: String?,
    mdsDeviceInfo: MdsDeviceInfo?,
    isDowngrade: Boolean?
): AnalyticsProperties {
    return AnalyticsProperties().apply {
        newFirmwareVersion?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_VERSION, it)
        }
        isDowngrade?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_IS_DOWNGRADE, isDowngrade.toYesNo())
        }
        addDeviceProperties(this, mdsDeviceInfo)
    }
}

fun downloadUpdateFailureProperties(
    newFirmwareVersion: String?,
    mdsDeviceInfo: MdsDeviceInfo?,
    failureReason: String,
    isDowngrade: Boolean?
): AnalyticsProperties {
    return AnalyticsProperties().apply {
        newFirmwareVersion?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_VERSION, it)
        }
        put(WATCH_MANAGEMENT_FAILURE_REASON, failureReason)
        isDowngrade?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_IS_DOWNGRADE, isDowngrade.toYesNo())
        }
        addDeviceProperties(this, mdsDeviceInfo)
    }
}

fun watchUpdateLearnMoreProperties(
    newFirmwareVersion: String?,
    mdsDeviceInfo: MdsDeviceInfo?,
    @AnalyticsPropertyValue.WatchManagementUpdateFlowStatus.Value updateFlowStatus: String?,
    isDowngrade: Boolean?
): AnalyticsProperties {
    return AnalyticsProperties().apply {
        newFirmwareVersion?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_VERSION, it)
        }
        updateFlowStatus?.let {
            put(WATCH_MANAGEMENT_UPDATE_FLOW_STATUS, it)
        }
        isDowngrade?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_IS_DOWNGRADE, isDowngrade.toYesNo())
        }
        addDeviceProperties(this, mdsDeviceInfo)
    }
}

fun sendDownloadUpdateCompletedEvent(
    newFirmwareVersion: String?,
    mdsDeviceInfo: MdsDeviceInfo?,
    timeFromTransferStartSeconds: Long?,
    isDowngrade: Boolean?
) {
    val properties = AnalyticsProperties().apply {
        newFirmwareVersion?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_VERSION, it)
        }
        timeFromTransferStartSeconds?.let {
            put(WATCH_MANAGEMENT_DOWNLOAD_DURATION, it)
        }
        isDowngrade?.let {
            put(WATCH_MANAGEMENT_NEW_FIRMWARE_IS_DOWNGRADE, isDowngrade.toYesNo())
        }
        addDeviceProperties(this, mdsDeviceInfo)
    }
    AnalyticsUtils.sendAnyEvent(WATCH_MANAGEMENT_DOWNLOAD_UPDATE_COMPLETED, properties)
}
