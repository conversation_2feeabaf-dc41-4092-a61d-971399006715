package com.stt.android.di.analytics

// comment-out to enable Tencent SDK in debug

import com.stt.android.analytics.tencent.TencentAnalytics
import com.stt.android.analytics.tencent.TencentAnalyticsImpl
import dagger.Binds
import dagger.Module
import javax.inject.Singleton

@Module
abstract class StoreAnalyticsModule {
    @Singleton
    @Binds
    abstract fun bindTencentAnalytics(tencentAnalyticsImpl: TencentAnalyticsImpl): TencentAnalytics
}
