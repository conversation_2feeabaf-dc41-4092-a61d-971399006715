package com.stt.android.di.remoteconfig

import android.app.Application
import android.content.Context
import android.net.ConnectivityManager
import com.squareup.moshi.Moshi
import com.stt.android.remote.DefaultAskoRemoteConfig
import com.stt.android.remote.RemoteConfigBaseUrl
import com.stt.android.remote.RemoteConfigCacheDirectory
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getOfflineCachingOkHttpConfig
import com.stt.android.remote.di.BrandOkHttpConfigFactory.getResponseSourceLoggingOfflineCachingOkHttpConfig
import com.stt.android.remote.di.RestApiFactory.buildRestApi
import com.stt.android.remote.remoteconfig.AmplitudeEventSampling
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse
import com.stt.android.remote.remoteconfig.AskoRemoteConfigRestApi
import com.stt.android.remote.remoteconfig.CompanionLinkingParameters
import com.stt.android.remote.remoteconfig.GraphhopperBaseUrl
import com.stt.android.remote.remoteconfig.STFusedLocationParameters
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults.Companion.COMPANION_LINKING_PARAMETERS_DEFAULTS
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults.Companion.DEFAULT_GRAPHHOPPER_BASE_URL
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults.Companion.ST_FUSED_LOCATION_DEFAULTS
import com.stt.android.remoteconfig.AskoRemoteConfigDefaults.Companion.singleCondition
import com.stt.android.utils.STTConstants
import dagger.Module
import dagger.Provides
import okhttp3.Cache
import okhttp3.OkHttpClient
import java.io.File

@Module
object RemoteConfigModule {
    private const val CACHE_SIZE = 1024 * 1024 * 2

    @JvmStatic
    @Provides
    @DefaultAskoRemoteConfig
    fun provideDefaultAskoRemoteConfig(): AskoRemoteConfigResponse {
        return AskoRemoteConfigResponse( // all partner connections enabled by default
            singleCondition<Boolean>(AskoRemoteConfigDefaults.DEFAULT_PARTNER_CONNECTIONS_ENABLED),  // movescount partner connection enabled by default on china
            singleCondition<Boolean>(true),  // movescountImport partner connection disabled by default
            singleCondition<Boolean>(false),  // movescountImport partner disconnect enabled by default
            singleCondition<Boolean>(true),  // sml to backend enabled by default
            singleCondition<Boolean>(AskoRemoteConfigDefaults.DEFAULT_SML_TO_BACKEND),  // delete account disabled by default
            singleCondition<Boolean>(false),  // st fused location parameters
            singleCondition<STFusedLocationParameters>(ST_FUSED_LOCATION_DEFAULTS),  // companion linking disabled by default
            singleCondition<Boolean>(false),  // companion linking parameters
            singleCondition<CompanionLinkingParameters>(COMPANION_LINKING_PARAMETERS_DEFAULTS),  // Emarsys Enabled
            singleCondition<Boolean>(true),  // no amplitude event sampling by default
            singleCondition<List<AmplitudeEventSampling>>(emptyList<AmplitudeEventSampling>()),  // Chat bot not shown
            singleCondition<Boolean>(false),  // default Graphhopper base URL
            singleCondition<GraphhopperBaseUrl>(DEFAULT_GRAPHHOPPER_BASE_URL),
            aiPlannerEnabled = singleCondition(AskoRemoteConfigDefaults.DEFAULT_AI_PLANNER_ENABLED)
        )
    }

    @JvmStatic
    @Provides
    fun provideAskoRemoteConfigRestApi(
        @SharedOkHttpClient sharedClient: OkHttpClient,
        @RemoteConfigBaseUrl baseUrl: String,
        @UserAgent userAgent: String,
        moshi: Moshi,
        @RemoteConfigCacheDirectory cacheDirectoryName: String,
        application: Application
    ): AskoRemoteConfigRestApi {
        val cm = application.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val okHttpConfig =
            if (STTConstants.DEBUG) getResponseSourceLoggingOfflineCachingOkHttpConfig(
                userAgent,
                cm
            ) else getOfflineCachingOkHttpConfig(userAgent, cm)
        return buildRestApi(
            sharedClient,
            baseUrl,
            AskoRemoteConfigRestApi::class.java,
            okHttpConfig,
            moshi,
            Cache(File(application.cacheDir, cacheDirectoryName), CACHE_SIZE.toLong())
        )
    }
}
