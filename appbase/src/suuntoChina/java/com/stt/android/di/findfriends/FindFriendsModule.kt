package com.stt.android.di.findfriends

import com.squareup.moshi.Moshi
import com.stt.android.findfriends.InviteFriendsHelperImpl
import com.stt.android.home.people.InviteFriendsHelper
import com.stt.android.remote.AuthProvider
import com.stt.android.remote.BaseUrl
import com.stt.android.remote.BaseUrlV2
import com.stt.android.remote.SharedOkHttpClient
import com.stt.android.remote.UserAgent
import com.stt.android.remote.di.BrandOkHttpConfigFactory
import com.stt.android.remote.di.RestApiFactory
import com.stt.android.remote.findfriends.PhoneContactFollowStateRestApi
import com.stt.android.remote.findfriends.PhoneContactFollowStateRestApiV2
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient

@Module
@InstallIn(SingletonComponent::class)
abstract class FindFriendsModule {
    @Binds
    abstract fun bindFindFriendsHelper(inviteFriendsHelperImpl: InviteFriendsHelperImpl): InviteFriendsHelper

    companion object {
        @Provides
        fun providePhoneContactFollowStateRestApi(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrl baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): PhoneContactFollowStateRestApi {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                PhoneContactFollowStateRestApi::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }

        @Provides
        fun providePhoneContactFollowStateRestApiV2(
            @SharedOkHttpClient sharedClient: OkHttpClient,
            @BaseUrlV2 baseUrl: String,
            @UserAgent userAgent: String,
            authProvider: AuthProvider,
            moshi: Moshi
        ): PhoneContactFollowStateRestApiV2 {
            return RestApiFactory.buildRestApi(
                sharedClient,
                baseUrl,
                PhoneContactFollowStateRestApiV2::class.java,
                BrandOkHttpConfigFactory.getStOkHttpConfig(authProvider, userAgent),
                moshi
            )
        }
    }
}
