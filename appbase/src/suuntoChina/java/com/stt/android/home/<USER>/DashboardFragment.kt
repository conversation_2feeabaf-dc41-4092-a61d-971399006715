package com.stt.android.home.dashboardv2

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.view.View
import androidx.core.content.edit
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.analytics.MessageSource
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.di.EmarsysCustomAttributePreferences
import com.stt.android.social.notifications.inbox.MarketingInboxActivity
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
internal class DashboardFragment : BaseSuuntoDashboardFragment() {
    @Inject
    @EmarsysCustomAttributePreferences
    lateinit var emarsysSharedPreferences: SharedPreferences

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        showAnnualReportPromptIfNeeded()
    }

    private fun showAnnualReportPromptIfNeeded() {
        val alreadyShown = emarsysSharedPreferences
            .getBoolean(
                STTConstants.EmarsysCustomAttributePreferences.ANNUAL_REPORT_DIALOG_PROMPT,
                false
            )
        if (alreadyShown) {
            return
        }

        viewLifecycleOwner.lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.showLocalAnnualReportFlow
                    .collect { localMessage ->
                        showAnnualReportPrompt(localMessage)
                    }
            }
        }
        viewModel.checkIfLocalAnnualReportIsNeeded()
    }

    private fun showAnnualReportPrompt(localInboxMessage: EmarsysInboxItem) {
        try {
            val dialogFragment = SimpleDialogFragment.newInstance(
                localInboxMessage.body,
                localInboxMessage.title,
                getString(R.string.annual_report_dialog_open),
                getString(R.string.annual_report_dialog_cancel),
            )
            @Suppress("DEPRECATION")
            dialogFragment.setTargetFragment(this, ANNUAL_REPORT_PROMPT_REQUEST)
            dialogFragment.show(parentFragmentManager, "annual_report_prompt_dialog")
        } catch (_: IllegalStateException) {
        }

        emarsysSharedPreferences.edit {
            putBoolean(
                STTConstants.EmarsysCustomAttributePreferences.ANNUAL_REPORT_DIALOG_PROMPT,
                true
            )
        }
    }

    @Deprecated("Deprecated in Java")
    @Suppress("DEPRECATION")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == ANNUAL_REPORT_PROMPT_REQUEST && resultCode == SimpleDialogFragment.RESULT_POSITIVE) {
            startActivity(MarketingInboxActivity.newIntent(requireContext(), MessageSource.POPUP))
        }
    }

    companion object {
        private const val ANNUAL_REPORT_PROMPT_REQUEST = 1011
    }
}
