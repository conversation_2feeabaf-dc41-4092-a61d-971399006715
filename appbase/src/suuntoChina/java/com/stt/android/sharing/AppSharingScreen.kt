package com.stt.android.sharing

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import com.stt.android.workouts.sharepreview.customshare.getIconRes
import com.stt.android.workouts.sharepreview.customshare.getNameRes
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun AppSharingScreen(
    shareTargets: ImmutableList<ShareTarget>,
    onClickItem: (ShareTarget) -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .clip(
                RoundedCornerShape(
                    MaterialTheme.spacing.medium,
                    MaterialTheme.spacing.medium,
                    0.dp,
                    0.dp
                )
            )
            .background(MaterialTheme.colorScheme.surface)
            .padding(MaterialTheme.spacing.medium)
    ) {
        DraggableBottomSheetHandle(topPadding = 0.dp, bottomPadding = MaterialTheme.spacing.large)
        Text(
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.smaller),
            text = stringResource(R.string.share),
            style = MaterialTheme.typography.bodyXLargeBold,
            color = MaterialTheme.colorScheme.onSurface
        )
        LazyVerticalGrid(
            columns = GridCells.Fixed(5),
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            items(shareTargets) {
                ShareItem(it, onClickItem)
            }
        }
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentSize()
                .padding(MaterialTheme.spacing.medium)
                .clickable(onClick = onDismissRequest),
            text = stringResource(id = R.string.cancel).uppercase(),
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun ShareItem(
    item: ShareTarget,
    onClickItem: (ShareTarget) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .clickable(onClick = { onClickItem(item) })
    ) {
        Image(
            painter = painterResource(id = item.getIconRes()),
            contentDescription = "",
            modifier = Modifier.size(52.dp)
        )
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = stringResource(item.getNameRes()),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurface,
            overflow = TextOverflow.Ellipsis,
            maxLines = 2
        )
    }
}

@Preview(showBackground = false)
@Composable
private fun SharingTargetsScreenPreview() {
    M3AppTheme {
        AppSharingScreen(
            shareTargets = persistentListOf(
                ShareTarget.CustomTarget("", "", R.drawable.share_wechat, R.string.we_chat),
                ShareTarget.CustomTarget(
                    "",
                    "",
                    R.drawable.share_wechat_moments,
                    R.string.we_chat_moments
                ),
                ShareTarget.CustomTarget("", "", R.drawable.share_wechat, R.string.we_chat),
                ShareTarget.CustomTarget(
                    "",
                    "",
                    R.drawable.share_wechat_moments,
                    R.string.we_chat_moments
                ),
                ShareTarget.CustomTarget("", "", R.drawable.share_wechat, R.string.we_chat),
                ShareTarget.CustomTarget(
                    "",
                    "",
                    R.drawable.share_wechat_moments,
                    R.string.we_chat_moments
                ),
            ),
            onClickItem = {},
            onDismissRequest = {}
        )
    }
}
