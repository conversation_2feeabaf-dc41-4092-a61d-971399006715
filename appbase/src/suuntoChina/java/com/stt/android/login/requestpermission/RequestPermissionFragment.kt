package com.stt.android.login.requestpermission

import androidx.navigation.NavDirections
import com.stt.android.login.signuplogindone.SignUpLoginDoneActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RequestPermissionFragment : BaseRequestPermissionFragment() {
    override fun navDirection(): NavDirections {
        val isSignUp = activity?.intent?.getBooleanExtra(
            SignUpLoginDoneActivity.KEY_IS_NEW_USER,
            false
        ) ?: false
        return RequestPermissionFragmentDirections
            .actionRequestPermissionFragmentToNewsletterSubscriptionFragment(isSignUp)
    }
}
