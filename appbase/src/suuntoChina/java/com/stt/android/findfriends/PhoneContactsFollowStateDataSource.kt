package com.stt.android.findfriends

import android.content.SharedPreferences
import com.stt.android.di.FeatureTogglePreferences
import com.stt.android.remote.findfriends.PhoneContactFollowStateRestApi
import com.stt.android.remote.findfriends.PhoneContactFollowStateRestApiV2
import com.stt.android.remote.findfriends.RemoteFollowStateChangeData
import com.stt.android.remote.findfriends.RemoteUserFollowStateInfo
import com.stt.android.utils.STTConstants
import javax.inject.Inject

class PhoneContactsFollowStateDataSource @Inject constructor(
    private val phoneContactFollowStateRestApi: PhoneContactFollowStateRestApi,
    private val phoneContactFollowStateRestApiV2: PhoneContactFollowStateRestApiV2,
    @FeatureTogglePreferences private val featureTogglePrefs: SharedPreferences,
) {
    suspend fun loadUserFollowState(phones: List<String>): List<RemoteUserFollowStateInfo>? {
        return phoneContactFollowStateRestApi.loadUserFollowStateV2(phones)
            .payloadNullableOrThrow()
    }

    suspend fun follow(userName: String): RemoteFollowStateChangeData? {
        if (featureTogglePrefs.getBoolean(
                STTConstants.FeatureTogglePreferences.KEY_ENABLE_APPROVING_FOLLOWERS,
                STTConstants.FeatureTogglePreferences.KEY_ENABLE_APPROVING_FOLLOWERS_DEFAULT,
            )
        ) {
            return phoneContactFollowStateRestApi.follow(userName).payloadNullableOrThrow()
        }
        return phoneContactFollowStateRestApiV2.follow(userName).payloadNullableOrThrow()
    }

    suspend fun unFollow(userName: String): RemoteFollowStateChangeData? {
        return phoneContactFollowStateRestApi.unFollow(userName).payloadNullableOrThrow()
    }
}
