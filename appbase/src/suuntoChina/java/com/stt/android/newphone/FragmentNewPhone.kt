package com.stt.android.newphone

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.R
import com.stt.android.compose.base.BaseFragment
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.TextFieldInputWithError
import com.stt.android.home.settings.PhoneNumberUtil.PHONE_NUMBER_LENGTH_CHINA
import com.stt.android.home.settings.PhoneNumberUtil.PHONE_REGION_CHINA
import com.stt.android.resetpassword.BaseContentBody
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class FragmentNewPhone : BaseFragment() {
    private val viewModel: FragmentNewPhoneViewModel by viewModels()
    private val newPhoneArgs: FragmentNewPhoneArgs by navArgs()

    @Composable
    override fun SetContentView() {
        NewPhoneScreen()
    }

    @Composable
    private fun NewPhoneScreen() {
        val uiState = viewModel.uiState
        val commonUIState by viewModel.commonUIState.collectAsState()
        BackHandler {
            backHandled()
        }
        LaunchedEffect(uiState.sendPhoneVerificationCodeSuccess) {
            uiState.sendPhoneVerificationCodeSuccess?.let {
                findNavController().navigate(
                    FragmentNewPhoneDirections.actionNewPhoneVerifyCode()
                        .setPhone(uiState.phoneNumber)
                        .setFromLogin(newPhoneArgs.fromLogin)
                )
                viewModel.clearSendPhoneVerificationCodeSuccess()
            }
        }

        BaseContentBody(
            viewModel = viewModel,
            onBackClick = {
                backHandled()
            }
        ) {
            ContentBody(
                isBtnEnable = uiState.isBtnEnable,
                isLoading = commonUIState.isLoading,
                phoneNumber = uiState.phoneNumber,
                phoneNumberError = uiState.phoneNumberError,
                onInputPhoneNumber = { viewModel.inputPhoneNumber(it) },
                onSendPhoneVerificationCode = { phoneNumber ->
                    viewModel.sendPhoneVerificationCode(phoneNumber)
                },
                onContactClick = {
                    activity?.apply {
                        CustomTabsUtils.launchCustomTab(
                            this,
                            getString(R.string.contact_support_link_suunto)
                        )
                    }
                }
            )
        }
    }

    private fun backHandled() {
        viewModel.logoutIfFromLogin()
        activity?.finish()
    }
}

@Preview
@Composable
private fun NewPhoneScreenPreview() {
    AppTheme {
        Surface {
            ContentBody(
                isLoading = false,
                phoneNumber = "",
                phoneNumberError = null,
                isBtnEnable = false
            )
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun ContentBody(
    isBtnEnable: Boolean,
    isLoading: Boolean = false,
    phoneNumber: String = "",
    phoneNumberError: PhoneNumberError? = null,
    onInputPhoneNumber: (String) -> Unit = {},
    onSendPhoneVerificationCode: (String) -> Unit = {},
    onContactClick: () -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top,
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xlarge))
        Text(
            text = stringResource(id = R.string.add_phone_number),
            color = MaterialTheme.colors.nearBlack,
            style = MaterialTheme.typography.bodyLargeBold,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = MaterialTheme.spacing.xxxxlarge)
        )

        TextFieldInputWithError(
            currentText = phoneNumber,
            keyboardType = KeyboardType.Number,
            onActionDone = {
                if (isBtnEnable) {
                    onSendPhoneVerificationCode.invoke(phoneNumber)
                }
            },
            onChanged = {
                if (it.length <= PHONE_NUMBER_LENGTH_CHINA) {
                    onInputPhoneNumber.invoke(it)
                }
            },
            errorMessage = phoneNumberError?.let { stringResource(id = it.resId) } ?: "",
            prefixText = PHONE_REGION_CHINA,
            placeholderText = stringResource(id = R.string.phone),
        )
        val keyboardController = LocalSoftwareKeyboardController.current
        PrimaryButton(
            onClick = {
                onSendPhoneVerificationCode.invoke(phoneNumber)
                keyboardController?.hide()
            },
            backgroundColor = MaterialTheme.colors.primary,
            text = stringResource(R.string.continue_str),
            enabled = isBtnEnable,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = MaterialTheme.spacing.medium,
                    vertical = MaterialTheme.spacing.medium
                )
        )

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )

        TextButton(
            onClick = onContactClick,
            modifier = Modifier
                .padding(vertical = MaterialTheme.spacing.xxxxlarge)
        ) {
            Text(
                text = stringResource(id = R.string.contact_support),
                style = MaterialTheme.typography.bodyBold,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.primary,
            )
        }
    }
    LoadingContent(isLoading)
}

fun isChinesePhoneNumberValid(phoneNumber: String): Boolean {
    val chinaPhoneRegex = Regex("^1[3456789]\\d{9}$")

    return chinaPhoneRegex.matches(phoneNumber)
}
