package com.stt.android.deleteaccount

import android.content.Context
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.UserSettingsController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.deleteaccount.usecase.ConfirmDeleteAccountUseCase
import com.stt.android.deleteaccount.usecase.GetVerificationCodeTokenUseCase
import com.stt.android.deleteaccount.usecase.SendVerificationCodeUseCase
import com.stt.android.exceptions.remote.AskoError
import com.stt.android.exceptions.remote.HttpException
import com.stt.android.ui.tasks.LogoutTask
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.ObsoleteCoroutinesApi
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ticker
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.seconds

@HiltViewModel
class DeleteAccountViewModel @Inject constructor(
    userSettingsController: UserSettingsController,
    private val sendVerificationCodeUseCase: SendVerificationCodeUseCase,
    private val getVerificationCodeTokenUseCase: GetVerificationCodeTokenUseCase,
    private val confirmDeleteAccountUseCase: ConfirmDeleteAccountUseCase,
    private val logoutTask: LogoutTask,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    val realName: String = userSettingsController.settings.realName ?: ""
    val phoneNumber: String = userSettingsController.settings.phoneNumber ?: ""

    private val _deleteAccountState =
        MutableStateFlow(DeleteAccountState(verificationCodeSent = false, remainSeconds = 0))
    val deleteAccountState = _deleteAccountState.asStateFlow()

    private val _event = Channel<DeleteAccountEvent>()
    val event = _event.receiveAsFlow()

    private var countdownJob: Job? = null
    private var countdownStartTimestamp: Long = 0L

    private val _verificationCodeTokenMap = mutableMapOf<String, String>()

    init {
        initialState()
    }

    private fun initialState() {
        val remainSeconds =
            if ((System.currentTimeMillis() - lastSendCodeTimestamp).milliseconds.inWholeSeconds in (1..TOTAL_COUNTDOWN_SECONDS)) {
                (TOTAL_COUNTDOWN_SECONDS - (System.currentTimeMillis() - lastSendCodeTimestamp).milliseconds.inWholeSeconds).toInt()
            } else {
                return
            }
        onSendCodeClick(remainSeconds)
    }

    fun onSendCodeClick(remainSeconds: Int = TOTAL_COUNTDOWN_SECONDS) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            countdownStartTimestamp =
                System.currentTimeMillis() + (remainSeconds - TOTAL_COUNTDOWN_SECONDS).seconds.inWholeMilliseconds
            if (remainSeconds == TOTAL_COUNTDOWN_SECONDS) {
                runSuspendCatching {
                    onBackendProcessing(true)
                    sendVerificationCodeUseCase(phoneNumber)
                }.onFailure { e ->
                    onBackendProcessing(false)
                    Timber.w(e, "send verification code failed")
                    when (e) {
                        is AskoError.TooManyFollowRequestError -> {
                            sendEvent(DeleteAccountEvent.TooManyRequestsError)
                            _deleteAccountState.update { it.copy(tooManyRequests = true) }
                        }

                        is HttpException -> {
                            sendEvent(DeleteAccountEvent.GeneralHttpError)
                        }

                        else -> {
                            sendEvent(DeleteAccountEvent.NetworkError)
                        }
                    }
                    return@launch
                }.onSuccess {
                    onBackendProcessing(false)
                    onCodeSend(remainSeconds)
                    lastSendCodeTimestamp = countdownStartTimestamp
                }
            } else {
                onCodeSend(remainSeconds)
            }
            startCountdownJob()
        }
    }

    fun onInputVerificationCode(code: String) {
        _deleteAccountState.update {
            it.copy(
                inputtedVerificationCode = code,
                verificationCodeError = false,
            )
        }
    }

    fun onVerifyClick() {
        viewModelScope.launch(coroutinesDispatchers.io) {
            val verificationCode = deleteAccountState.value.inputtedVerificationCode
            _verificationCodeTokenMap[verificationCode]?.let {
                sendEvent(DeleteAccountEvent.RequestConfirmDelete(it))
                return@launch
            }
            runSuspendCatching {
                onBackendProcessing(true)
                getVerificationCodeTokenUseCase(phoneNumber, verificationCode)
            }.onFailure { e ->
                onBackendProcessing(false)
                Timber.w(e, "get verification code token failed")
                if (e is HttpException) {
                    _deleteAccountState.update { it.copy(verificationCodeError = true) }
                } else {
                    sendEvent(DeleteAccountEvent.NetworkError)
                }
                return@launch
            }.onSuccess {
                onBackendProcessing(false)
                _verificationCodeTokenMap[verificationCode] = it
                sendEvent(DeleteAccountEvent.RequestConfirmDelete(it))
            }
        }
    }

    fun onConfirmDelete(token: String, context: Context, fragmentManager: FragmentManager) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                onBackendProcessing(true)
                confirmDeleteAccountUseCase(token)
            }.onFailure { e ->
                onBackendProcessing(false)
                Timber.w(e, "confirm delete account failed")
                when (e) {
                    is DeleteAccountError, is HttpException -> {
                        sendEvent(DeleteAccountEvent.GeneralHttpError)
                    }

                    else -> {
                        sendEvent(DeleteAccountEvent.NetworkError)
                    }
                }
            }.onSuccess {
                onBackendProcessing(false)
                withContext(coroutinesDispatchers.main) {
                    runSuspendCatching {
                        logoutTask.logoutWithProgressDialog(context, fragmentManager).await()
                    }.onFailure { e ->
                        Timber.w(e, "logout failed")
                    }
                }
            }
        }
    }

    private fun onBackendProcessing(processing: Boolean) {
        _deleteAccountState.update {
            it.copy(
                backendProcessing = processing,
            )
        }
    }

    private fun onCodeSend(remainSeconds: Int) {
        _deleteAccountState.update {
            it.copy(
                verificationCodeSent = true,
                remainSeconds = remainSeconds,
            )
        }
    }

    @OptIn(ObsoleteCoroutinesApi::class)
    private fun startCountdownJob() {
        countdownJob?.cancel()
        countdownJob = viewModelScope.launch {
            val ticker = ticker(delayMillis = 1000, initialDelayMillis = 0)
            for (event in ticker) {
                val elapsed =
                    (System.currentTimeMillis() - countdownStartTimestamp).milliseconds.inWholeSeconds.toInt()
                val remaining = (TOTAL_COUNTDOWN_SECONDS - elapsed).coerceAtLeast(0)

                _deleteAccountState.update { it.copy(remainSeconds = remaining) }

                if (remaining <= 0) {
                    ticker.cancel()
                    break
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        countdownJob?.cancel()
    }

    private fun sendEvent(event: DeleteAccountEvent) {
        viewModelScope.launch {
            _event.send(event)
        }
    }

    private companion object {
        private var lastSendCodeTimestamp: Long = 0L
        private const val TOTAL_COUNTDOWN_SECONDS = 60
    }
}
