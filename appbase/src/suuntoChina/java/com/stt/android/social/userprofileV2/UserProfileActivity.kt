package com.stt.android.social.userprofileV2

import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.social.userprofile.CustomerServiceBuilder
import com.stt.android.utils.CustomTabsUtils
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class UserProfileActivity : SuuntoUserProfileActivity() {

    @Inject
    lateinit var customerServiceBuilder: CustomerServiceBuilder
    override fun getSettingMenuList(): List<SettingMenuInfo> {
        return listOf(
            SettingMenuInfo(
                SettingMenuType.HEADPHONES,
                R.drawable.ic_headphones,
                getString(R.string.device_type_select_headphones),
            ),
            SettingMenuInfo(
                SettingMenuType.FIND_PEOPLE,
                R.drawable.ic_find_people,
                getString(R.string.find_friends),
            ),
            SettingMenuInfo(
                SettingMenuType.SETTING,
                R.drawable.ic_settings,
                getString(R.string.settings),
            ),
            SettingMenuInfo(
                SettingMenuType.FEEDBACK,
                R.drawable.ic_feedback,
                getString(R.string.feedback),
            ),
            SettingMenuInfo(
                SettingMenuType.REPAIR_SERVICE,
                R.drawable.ic_repair,
                getString(R.string.settings_repair_service),
            ),
            SettingMenuInfo(
                SettingMenuType.CONTACT_CUSTOMER_SERVICE,
                R.drawable.ic_chat_bot,
                getString(R.string.settings_contact_customer_service),
            ),
            SettingMenuInfo(
                SettingMenuType.PARTNER_SERVICE,
                R.drawable.ic_partner,
                getString(R.string.partner_connections_title),
            ),
            SettingMenuInfo(
                SettingMenuType.SUPPORT,
                R.drawable.ic_support,
                getString(R.string.settings_help),
            ),
        )
    }

    override fun onContactCustomerServiceClicked() {
        trackAnalytics(event = AnalyticsEvent.OPEN_CUSTOMER_SERVICE)
        val url = getString(R.string.customer_service_url)
        CustomTabsUtils.launchCustomTab(this, url)
    }

    override fun onAfterSalesServiceClicked() {
        val url = getString(R.string.repair_service_url)
        CustomTabsUtils.launchCustomTab(this, url)
    }
}
