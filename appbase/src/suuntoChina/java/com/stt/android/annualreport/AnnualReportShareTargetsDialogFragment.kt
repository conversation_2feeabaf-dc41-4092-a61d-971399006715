package com.stt.android.annualreport

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.view.View
import android.widget.Toast
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.OnModelBuildFinishedListener
import com.stt.android.BuildConfig
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.ShareBroadcastReceiver
import com.stt.android.annualreport.AnnualReportShareTargetsViewModel.Companion.WEIBO_IMAGE_LIMIT_COUNT
import com.stt.android.common.viewstate.ViewStateBottomSheetListFragment
import com.stt.android.databinding.FragmentAnnualReportShareTargetsBinding
import com.stt.android.multimedia.MediaType
import com.stt.android.sharingplatform.SharingResultState
import com.stt.android.workouts.sharepreview.customshare.ShareTarget
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareLinkTargets
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File

@AndroidEntryPoint
class AnnualReportShareTargetsDialogFragment :
    ViewStateBottomSheetListFragment<WorkoutShareLinkTargets, AnnualReportShareTargetsViewModel>() {

    override val viewModel: AnnualReportShareTargetsViewModel by viewModels()

    override val layoutId: Int = R.layout.fragment_annual_report_share_targets

    private val binding: FragmentAnnualReportShareTargetsBinding get() = requireBinding()

    private var resourceUris: List<Uri>? = null

    private var shareVideo = false

    private val buildFinishedListener = OnModelBuildFinishedListener {
        binding.list.requestLayout()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        controller.addModelBuildListener(buildFinishedListener)
        resourceUris = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            arguments?.getParcelableArrayList(KEY_RESOURCE_URI, Uri::class.java)
        } else {
            arguments?.getParcelableArrayList(KEY_RESOURCE_URI)
        }
        shareVideo = arguments?.getBoolean(KEY_VIDEO) ?: false
        if (resourceUris == null || resourceUris?.isEmpty() == true) {
            Timber.w("annual report share failed, image uris is empty")
            dismiss()
        }
        initViewAndEvent()
        viewModel.loadTargets(resourceUris?.size == 1)
    }

    private fun initViewAndEvent() {
        binding.list.layoutManager = GridLayoutManager(requireContext(), 5)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                launch {
                    viewModel.shareTargetEvent.collect { shareTarget ->
                        // share to os will send analysis by other way
                        if (shareTarget !is ShareTarget.DelegateToOS) {
                            viewModel.sendAnnualReportAnalysis(
                                shareTarget = shareTarget,
                                shareVideo = shareVideo,
                                imageCount = resourceUris?.size ?: 0,
                                imageIndexes = arguments?.getIntegerArrayList(KEY_IMAGE_INDEXES)
                                    ?: emptyList()
                            )
                        }
                        when (shareTarget) {
                            is ShareTarget.CustomTarget -> {
                                if (viewModel.hasThirdPlatform(
                                        shareTarget.appId,
                                        requireActivity()
                                    )
                                ) {
                                    resourceUris?.let { uris ->
                                        viewModel.handleCustomShareTarget(
                                            shareTarget,
                                            uris,
                                            requireActivity(),
                                            {
                                                showShareResultMessage(it)
                                            },
                                            resources.getStringArray(R.array.hash_tags).toList(),
                                            shareVideo
                                        )
                                    }
                                } else {
                                    showErrorMessage(ShareErrorType.NOT_INSTALL_PLATFORM)
                                }
                            }

                            is ShareTarget.SaveToMedia -> {
                                context?.let {
                                    viewModel.saveMedia(
                                        requireContext().contentResolver,
                                        resourceUris,
                                        if (shareVideo) MediaType.VIDEO else MediaType.IMAGE
                                    )
                                    val destFolder = File(
                                        Environment.getExternalStoragePublicDirectory(if (shareVideo) Environment.DIRECTORY_MOVIES else Environment.DIRECTORY_PICTURES),
                                        BuildConfig.DIRECTORY_APPLICATION
                                    )
                                    Toast.makeText(
                                        requireContext().applicationContext,
                                        getString(R.string.image_saved_at, destFolder),
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                                dismiss()
                            }

                            is ShareTarget.DelegateToOS -> {
                                context?.let {
                                    resourceUris?.apply {
                                        viewModel.shareToOS(
                                            it.contentResolver,
                                            this,
                                            if (shareVideo) MediaType.VIDEO else MediaType.IMAGE
                                        ) {
                                            ShareBroadcastReceiver.shareToSystem(
                                                requireActivity(),
                                                it,
                                                AnalyticsEvent.H5SHARE,
                                                viewModel.getAnnualReportSharingAnalysis(
                                                    shareVideo,
                                                    imageCount = resourceUris?.size ?: 0,
                                                    imageIndexes = arguments?.getIntegerArrayList(
                                                        KEY_IMAGE_INDEXES
                                                    )
                                                        ?: emptyList()
                                                )
                                            )
                                        }
                                    }
                                }
                                dismiss()
                            }
                        }
                    }
                }
                launch {
                    viewModel.shareErrorEvent.collect {
                        showErrorMessage(it)
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        data?.let {
            viewModel.setShareResultForWeibo(it, ::showShareResultMessage)
        }
    }

    private fun showShareResultMessage(state: SharingResultState) {
        val message = when (state) {
            SharingResultState.Success -> getString(R.string.share_suucess)
            SharingResultState.Fail -> getString(R.string.share_fail)
            SharingResultState.Cancel -> getString(R.string.share_cancel)
        }
        Toast.makeText(requireContext().applicationContext, message, Toast.LENGTH_SHORT).show()
        dismissAllowingStateLoss()
    }

    private fun showErrorMessage(type: ShareErrorType) {
        val message = when (type) {
            ShareErrorType.WEIBO_OVER_IMAGE_COUNT -> getString(
                R.string.weibo_share_image_limit,
                WEIBO_IMAGE_LIMIT_COUNT
            )

            ShareErrorType.NOT_INSTALL_PLATFORM -> getString(R.string.share_fail)

            ShareErrorType.CANCEL -> getString(R.string.share_cancel)
        }
        Toast.makeText(requireContext().applicationContext, message, Toast.LENGTH_SHORT).show()
        dismissAllowingStateLoss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        controller.removeModelBuildListener(buildFinishedListener)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.removeShareCallback()
    }

    companion object {
        private const val KEY_RESOURCE_URI = "com.stt.android.annualreport.KEY_RESOURCE_URI"
        private const val KEY_VIDEO = "com.stt.android.annualreport.KEY_VIDEO"
        private const val KEY_IMAGE_INDEXES = "com.stt.android.annualreport.KEY_IMAGE_INDEXES"

        fun newAnnualReportShareTargetsDialogFragment(
            resourceUris: List<Uri>,
            video: Boolean = false,
            imageIndexes: List<Int> = emptyList()
        ): AnnualReportShareTargetsDialogFragment {
            return AnnualReportShareTargetsDialogFragment().apply {
                arguments = Bundle().apply {
                    putParcelableArrayList(KEY_RESOURCE_URI, ArrayList(resourceUris))
                    putBoolean(KEY_VIDEO, video)
                    putIntegerArrayList(KEY_IMAGE_INDEXES, ArrayList(imageIndexes))
                }
            }
        }
    }
}
