package com.stt.android.annualreport

import android.content.SharedPreferences
import android.content.res.AssetFileDescriptor
import android.graphics.Rect
import android.os.Handler
import android.os.HandlerThread
import android.text.TextUtils
import android.view.PixelCopy
import android.view.Window
import androidx.core.content.edit
import androidx.core.graphics.createBitmap
import com.squareup.moshi.Json
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.JsonClass
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import com.stt.android.analytics.MessageSource
import com.stt.android.common.coroutines.CoroutineViewModel
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.VersionName
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.utils.STTConstants.CacheFileSharedPreferences
import com.stt.android.videoencode.EncodeFinishListener
import com.stt.android.videoencode.MP4Encoder
import com.stt.android.videoencode.MergeAudioAndVideo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject

@JsonClass(generateAdapter = true)
data class ImageBase64Data(
    @Json(name = "images")
    val images: List<String>?
)

@HiltViewModel
class AnnualReportViewModel @Inject constructor(
    private val currentUserController: CurrentUserController,
    private val cacheFileSharedPreferences: SharedPreferences,
    private val annualReportAnalysis: AnnualReportAnalysis,
    @VersionName private val version: String,
    dispatchers: CoroutinesDispatchers
) : CoroutineViewModel(dispatchers) {
    private val moshi = Moshi.Builder().build()
    private val _annualReportBaseData = MutableSharedFlow<String?>()
    val annualReportBaseData: Flow<String?> = _annualReportBaseData.asSharedFlow()
    private val _shareImagesEvent = MutableSharedFlow<List<File>>()
    val shareImagesEvent: Flow<List<File>> = _shareImagesEvent
    private val _backEvent = MutableSharedFlow<Boolean>()
    val backEvent: Flow<Boolean> = _backEvent
    private val _previewAnnualReportEvent = MutableSharedFlow<Boolean>()
    val previewAnnualReportEvent: Flow<Boolean> = _previewAnnualReportEvent
    private val _shareVideo = MutableSharedFlow<Boolean>()
    val shareVideo: Flow<Boolean> = _shareVideo
    private val _handleSharedImageLoading = MutableSharedFlow<Boolean>()
    val handleSharedImageLoading = _handleSharedImageLoading.asSharedFlow()

    private var mP4Encoder: MP4Encoder? = null
    private var encoderJob: Job? = null
    private var screenshotListenerThread = HandlerThread("screenshot")
    private var shareAnnualReportJob: Job? = null

    fun initAnnualAnalysis(targetUrl: String, messageSource: MessageSource) {
        launch {
            annualReportAnalysis.init(targetUrl, messageSource)
        }
    }

    /**
     * get the base data for h5
     */
    fun getAnnualReportBaseData(preview: Boolean) {
        launch(io) {
            val sessionkey = currentUserController.session?.sessionKey
            val baseDataJson = sessionkey?.let { key ->
                val annualReportBaseData =
                    AnnualReportBaseData(sessionkey = key, preview = preview, version = version)
                val adapter = moshi.adapter(AnnualReportBaseData::class.java)
                adapter.toJson(annualReportBaseData)
            }
            _annualReportBaseData.emit(baseDataJson)
        }
    }

    fun back() {
        launch(io) {
            _backEvent.emit(true)
        }
    }

    fun previewAnnualReport() {
        launch(io) {
            _previewAnnualReportEvent.emit(true)
        }
    }

    fun initMp4Encoder(savedPath: String, webViewRect: Rect) {
        mP4Encoder = MP4Encoder().apply {
            init(outputFilePath = savedPath, outputSizeRect = webViewRect)
        }
    }

    fun startEncoder(window: Window) {
        if (encoderJob?.isActive == true) {
            return
        }
        screenshotListenerThread.start()
        encoderJob = launch(io) {
            runSuspendCatching {
                mP4Encoder?.apply {
                    startEncode()
                    val webViewRect = getOutputSize()
                    val widthAndHeight = getWH()
                    Timber.w("video and screenshot image size: ${widthAndHeight.width}: ${widthAndHeight.height}")
                    while (isActive) {
                        val bitmap = createBitmap(widthAndHeight.width, widthAndHeight.height)
                        try {
                            PixelCopy.request(
                                window,
                                webViewRect,
                                bitmap,
                                {
                                    if (it == PixelCopy.SUCCESS) {
                                        mP4Encoder?.addFrame(bitmap)
                                    }
                                },
                                Handler(screenshotListenerThread.looper)
                            )
                        } catch (e: Exception) {
                            Timber.w(e, "PixelCopy failed")
                        }
                        delay(AnnualReportConstant.FRAME_DELAY)
                    }
                }
            }.onFailure { Timber.w(it, "encode video failed") }
        }
    }

    fun stopEncoder() {
        mP4Encoder?.setEncodeFinishListener(object : EncodeFinishListener {
            override fun onEncodeFinished() {
                launch {
                    _shareVideo.emit(true)
                }
            }
        })
        screenshotListenerThread.quitSafely()
        encoderJob?.cancel()
        mP4Encoder?.stopEncode()
    }

    fun mergeAudioAndVideo(
        videoPath: String,
        audioFileDescriptor: AssetFileDescriptor,
        outputPath: String,
        onSuccess: () -> Unit
    ) {
        launch {
            val result = withContext(io) {
                runSuspendCatching {
                    MergeAudioAndVideo.merge(
                        videoPath = videoPath,
                        audioFileDescriptor = audioFileDescriptor,
                        outputPath = outputPath
                    )
                }
            }
            audioFileDescriptor.close()
            result.onFailure {
                Timber.w(it, "merge video and audio failed")
            }.onSuccess {
                onSuccess.invoke()
            }
        }
    }

    fun shareAnnualReport(externalFile: File, imagesJson: String) {
        if (shareAnnualReportJob?.isActive == true) return
        shareAnnualReportJob = launch(io) {
            _handleSharedImageLoading.emit(true)
            try {
                val adapter = moshi.adapter(ImageBase64Data::class.java)
                val imageBase64Data = adapter.fromJson(imagesJson)
                if (imageBase64Data != null) {
                    imageBase64Data.images?.apply {
                        // delete last cached files
                        deleteCacheImageFile(externalFile)
                        val savedImageFiles = MediaStoreUtils.saveImagesToExternalFileDir(
                            externalFile,
                            this
                        )
                        val fileNames = savedImageFiles.map {
                            it.name
                        }
                        saveCacheImageFileName(fileNames)
                        _shareImagesEvent.emit(savedImageFiles)
                    }
                } else {
                    Timber.d("parse image data failed")
                }
            } catch (e: Exception) {
                Timber.d(e, "save image file to cache dir failed")
            }
            _handleSharedImageLoading.emit(false)
        }
    }

    private fun saveCacheImageFileName(fileNames: List<String>) {
        val fileNamesJson = moshiAdapterForStringList().toJson(fileNames)
        cacheFileSharedPreferences.edit {
            putString(
                CacheFileSharedPreferences.KEY_ANNUAL_REPORT_URIS,
                fileNamesJson
            )
        }
    }

    private fun moshiAdapterForStringList(): JsonAdapter<List<String>> {
        val parameterizedType = Types.newParameterizedType(List::class.java, String::class.java)
        return moshi.adapter(parameterizedType)
    }

    private fun deleteCacheImageFile(externalFile: File) {
        try {
            val cacheFileNamesJson = cacheFileSharedPreferences.getString(
                CacheFileSharedPreferences.KEY_ANNUAL_REPORT_URIS,
                ""
            )
            if (!TextUtils.isEmpty(cacheFileNamesJson)) {
                val cacheFileNames = moshiAdapterForStringList().fromJson(cacheFileNamesJson ?: "")
                cacheFileNames?.forEach {
                    val imageFile = File(externalFile, it)
                    if (imageFile.exists()) {
                        imageFile.delete()
                    }
                }
            }
        } catch (e: Exception) {
            Timber.w(e, "delete cache image file failed")
        }
    }

    fun sendAnnualReportAnalysisForH5Click(eventType: H5ClickEventType) {
        annualReportAnalysis.sendAnnualReportAnalysisForH5Click(eventType)
    }

    fun sendAnnualReportAnalysisForH5Exposure(pageNumber: String) {
        annualReportAnalysis.sendAnnualReportAnalysisForH5Exposure(pageNumber)
    }
}
