<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="com.sec.android.iap.permission.BILLING" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION"/>
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/>
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <!-- Needed by SyncInForegroundService -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <application>
        <activity
            android:name=".analytics.AppOpenAnalyticsActivity"
            android:exported="false" />
        <!-- Disable WorkManager's own initializer since we provide our own custom one -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="merge"
            android:exported="false">
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup"
                tools:node="remove" />
        </provider>

        <receiver android:name=".systemwidget.TrainingDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_training_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_training_info" />
        </receiver>

        <receiver android:name=".systemwidget.AscentDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_ascent_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_ascent_info" />
        </receiver>

        <receiver android:name=".systemwidget.CommuteThisMonthDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_commute_this_month_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_commute_this_month_info" />
        </receiver>

        <receiver android:name=".systemwidget.GoalDashboardWidgetAsSystemWidgetProvider"
            android:label="@string/dashboard_widget_goal_name"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data android:name="android.appwidget.provider"
                android:resource="@xml/systemwidget_goal_info" />
        </receiver>

        <receiver
            android:name=".glance.MonthlyCalendarHomeWidgetReceiver"
            android:exported="true"
            android:label="@string/home_widget_monthly_calendar_name">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/home_widget_monthly_calendar" />
        </receiver>

        <receiver
            android:name=".glance.ProgressHomeWidgetReceiver"
            android:exported="true"
            android:label="@string/dashboard_widget_progress_name">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/home_widget_progress" />
        </receiver>

        <activity
            android:name="com.stt.android.home.workouts.DiaryTotalsActivity"
            android:theme="@style/WhiteTheme"/>

        <activity android:name=".workouts.sharepreview.EditActivityTypeAndStartTimeDisplayActivity"
            android:theme="@style/WhiteTheme"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize"
            />

        <activity android:name=".menstrualcycle.settings.MenstrualCycleSettingsActivity"
            android:exported="false"
            android:theme="@style/WhiteTheme">
            <intent-filter>
                <action android:name="com.stt.android.MENSTRUAL_CYCLE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:label="@string/new_email_address"
            android:name="com.stt.android.newemail.NewEmailActivity"
            android:theme="@style/WhiteTheme"/>

        <activity
            android:name=".home.settings.exportmydata.ExportMyDataActivity"
            android:label="@string/export_my_data_title"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name=".appupdates.AppUpdatesActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.social.userprofileV2.UserProfileActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.social.workoutlist.AllWorkoutActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.social.workoutlist.search.SearchWorkoutActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.social.workoutlistv2.search.SearchWorkoutActivity"
            android:theme="@style/WhiteTheme" />
        <activity
            android:name="com.stt.android.social.friends.search.SearchFriendsActivity"
            android:theme="@style/WhiteTheme"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.stt.android.social.friends.facebook.FacebookFriendsActivity"
            android:theme="@style/WhiteTheme" />

        <activity
            android:name="com.stt.android.ui.crop.CropImageActivity"
            android:theme="@style/DarkTheme" />

        <activity
            android:name="com.stt.android.home.settings.v2.location.LocationSelectionActivity"
            android:theme="@style/WhiteTheme" />

        <activity android:name=".home.marketing.MarketingH5Activity"
            android:theme="@style/WhiteTheme"/>

        <receiver android:name=".menstrualcycle.alarm.AlarmNotificationReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.stt.android.ALARM_ACTION_PREDICT" />
                <action android:name="com.stt.android.ALARM_ACTION_LOG" />
                <action android:name="com.stt.android.ALARM_ACTION_REMINDER_LATER" />
            </intent-filter>
        </receiver>

        <!-- SyncRequestHandlerWorker adds dataSync to the ForegroundInfo -->
        <service android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:foregroundServiceType="dataSync"
            tools:node="merge" />
    </application>

    <queries>
        <!-- For checking for Huawei battery saver -->
        <package android:name="com.huawei.systemmanager" />

        <!-- For opening content in specific external apps, see StartActivityHelper -->
        <package android:name="com.google.android.youtube" />
        <package android:name="com.android.chrome" />

        <!-- For ShareIconsView -->
        <intent>
            <action android:name="android.intent.action.SEND" />
            <data android:mimeType="text/plain" />
        </intent>

        <!-- Audio feedback when tracking using the app -->
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>
</manifest>
