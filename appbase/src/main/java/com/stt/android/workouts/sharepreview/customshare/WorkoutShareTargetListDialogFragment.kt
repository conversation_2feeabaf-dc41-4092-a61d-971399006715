package com.stt.android.workouts.sharepreview.customshare

import android.content.SharedPreferences
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.text.TextUtils
import android.view.View
import android.widget.Toast
import androidx.core.content.edit
import androidx.core.net.toUri
import androidx.core.os.BundleCompat
import androidx.core.os.bundleOf
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import com.airbnb.epoxy.OnModelBuildFinishedListener
import com.stt.android.BuildConfig
import com.stt.android.R
import com.stt.android.analytics.EmarsysAnalytics
import com.stt.android.common.ui.observeNotNull
import com.stt.android.common.viewstate.ViewStateBottomSheetListFragment
import com.stt.android.databinding.FragmentWorkoutShareLinkTargetListBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.multimedia.sportie.SportieShareType
import com.stt.android.utils.STTConstants
import com.stt.android.utils.STTConstants.CacheFileSharedPreferences
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutShareTargetListDialogFragment : ViewStateBottomSheetListFragment<
    WorkoutShareLinkTargets,
    WorkoutShareTargetListViewModel,
    >() {

    override val layoutId: Int = R.layout.fragment_workout_share_link_target_list
    override val viewModel: WorkoutShareTargetListViewModel by viewModels()

    private val binding: FragmentWorkoutShareLinkTargetListBinding get() = requireBinding()

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    lateinit var emarsysAnalytics: EmarsysAnalytics

    @Inject
    lateinit var cacheFileSharedPreferences: SharedPreferences

    private lateinit var useCase: UseCase
    private lateinit var workoutHeader: WorkoutHeader
    private lateinit var source: SportieShareSource
    private lateinit var imageUri: Uri
    private lateinit var videoUri: Uri
    private lateinit var sportieSelection: SportieSelection
    private var numPhotosAdded: Int = 0

    private val buildFinishedListener = OnModelBuildFinishedListener {
        // For some reason the bottom sheet dialog fragment does not update the layout correctly
        // when epoxy is done adding items to the recycler view. Enforcing layout update.
        binding.list.requestLayout()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        controller.addModelBuildListener(buildFinishedListener)
        initArgs()
        // delete cache share image file that is saved last share
        deleteCacheFileForShare()

        val shareLink = this::useCase.isInitialized &&
            useCase == UseCase.SHARE_LINK &&
            this::workoutHeader.isInitialized

        val shareImage = this::useCase.isInitialized &&
            useCase == UseCase.SHARE_IMAGE &&
            this::workoutHeader.isInitialized &&
            this::imageUri.isInitialized &&
            this::sportieSelection.isInitialized

        val shareVideo = this::useCase.isInitialized &&
            useCase == UseCase.SHARE_VIDEO &&
            this::videoUri.isInitialized

        when {
            shareLink -> {
                val targets = workoutShareHelper.getCustomShareLinkTargets(requireContext())
                viewModel.setupTargets(targets)
            }

            shareImage -> {
                // save cache share image uri
                // when finish share, will delete this cache file
                cacheFileSharedPreferences.edit {
                    putString(
                        CacheFileSharedPreferences.KEY_FILE_URI,
                        imageUri.toString()
                    )
                }
                val targets = workoutShareHelper.getCustomShareImageTargets(requireContext())
                if (targets.size == 1) {
                    handleShareImageToTarget(targets[0])
                    dismiss()
                } else {
                    viewModel.setupTargets(targets)
                }
            }

            shareVideo -> {
                val targets = workoutShareHelper.getCustomShareVideoTargets(requireContext())
                viewModel.setupTargets(targets)
            }

            else -> {
                if (BuildConfig.DEBUG) {
                    throw IllegalArgumentException("WorkoutShareLinkTargetListDialogFragment initialised with wrong parameters")
                } else {
                    Timber.w("WorkoutShareLinkTargetListDialogFragment initialised with wrong parameters")
                }
            }
        }
    }

    private fun deleteCacheFileForShare() {
        val cacheShareFileUri =
            cacheFileSharedPreferences.getString(CacheFileSharedPreferences.KEY_FILE_URI, "")
        // delete cache image if new image uri is the same as cache uri will make system can't find the image when you share it
        if (this::imageUri.isInitialized && !TextUtils.isEmpty(cacheShareFileUri) && imageUri.toString() != cacheShareFileUri) {
            viewModel.deleteCacheFile(requireContext(), cacheShareFileUri.orEmpty().toUri())
            cacheFileSharedPreferences.edit {
                putString(CacheFileSharedPreferences.KEY_FILE_URI, "")
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.targetSelectLiveEvent.observeNotNull(viewLifecycleOwner) {
            when (useCase) {
                UseCase.SHARE_LINK -> handleShareLinkToTarget(it)
                UseCase.SHARE_IMAGE -> handleShareImageToTarget(it)
                UseCase.SHARE_VIDEO -> handleShareVideoToTarget(it)
            }
            dismiss()
        }
        binding.viewModel = viewModel
        binding.list.layoutManager = GridLayoutManager(requireContext(), 5)
    }

    override fun onDestroy() {
        super.onDestroy()
        controller.removeModelBuildListener(buildFinishedListener)
    }

    private fun initArgs() = arguments?.let { args ->
        BundleCompat.getSerializable(args, EXTRA_USE_CASE, UseCase::class.java)
            ?.let { useCase = it }
        BundleCompat.getParcelable(
            args,
            STTConstants.ExtraKeys.WORKOUT_HEADER,
            WorkoutHeader::class.java,
        )?.let { workoutHeader = it }
        BundleCompat.getSerializable(
            args,
            STTConstants.ExtraKeys.SPORTIE_SHARE_SOURCE,
            SportieShareSource::class.java,
        )?.let { source = it }
        BundleCompat.getParcelable(args, EXTRA_IMAGE_URI, Uri::class.java)?.let { imageUri = it }
        BundleCompat.getParcelable(args, EXTRA_SPORTIE_SELECTION, SportieSelection::class.java)
            ?.let { sportieSelection = it }
        numPhotosAdded = args.getInt(EXTRA_NUM_PHOTOS_ADDED)
        BundleCompat.getParcelable(args, EXTRA_VIDEO_URI, Uri::class.java)?.let { videoUri = it }
    }

    private fun handleShareLinkToTarget(shareTarget: ShareTarget) {
        val activity = requireActivity()
        when (shareTarget) {
            is ShareTarget.SaveToMedia -> {
                // do nothing, not supported
                Timber.w("SaveToPictures not supported when sharing link")
            }

            is ShareTarget.DelegateToOS -> {
                workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                    activity,
                    workoutHeader,
                    source
                )
            }

            is ShareTarget.CustomTarget -> {
                if (workoutShareHelper.hasSharePlatformApp(activity, shareTarget.appId).not()) {
                    Toast.makeText(
                        activity.applicationContext,
                        R.string.share_fail,
                        Toast.LENGTH_SHORT
                    )
                        .show()
                } else {
                    // sharing link to custom target takes some time, let's show a toast to the user
                    Toast.makeText(
                        activity.applicationContext,
                        R.string.loading_content,
                        Toast.LENGTH_SHORT
                    )
                        .show()
                    viewModel.shareLinkToCustomTarget(activity, workoutHeader, source, shareTarget)
                }
            }
        }
    }

    private fun handleShareImageToTarget(shareTarget: ShareTarget) {
        val activity = requireActivity()
        when (shareTarget) {
            is ShareTarget.SaveToMedia -> {
                // we handle analytics here because no actual intent is sent
                workoutShareHelper.sendImageShareAnalytics(
                    workoutHeader,
                    sportieSelection,
                    TARGET_SAVE_TO_PICTURES
                )
                // saved image to media
                viewModel.saveToPicture(requireContext(), imageUri)
                // show a message with the location
                val destFolder = File(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES),
                    BuildConfig.DIRECTORY_APPLICATION
                )
                Toast.makeText(
                    activity.applicationContext,
                    getString(R.string.image_saved_at, destFolder),
                    Toast.LENGTH_LONG
                ).show()
            }

            is ShareTarget.DelegateToOS -> {
                viewModel.shareToOS(
                    activity,
                    workoutHeader,
                    imageUri,
                    sportieSelection,
                    numPhotosAdded
                )
            }

            is ShareTarget.CustomTarget -> {
                if (workoutShareHelper.hasSharePlatformApp(activity, shareTarget.appId).not()) {
                    Toast.makeText(
                        activity.applicationContext,
                        R.string.share_fail,
                        Toast.LENGTH_SHORT
                    )
                        .show()
                } else {
                    viewModel.shareImageToCustomTarget(
                        activity,
                        workoutHeader,
                        imageUri,
                        sportieSelection,
                        shareTarget
                    )
                }
            }
        }
    }

    private fun handleShareVideoToTarget(shareTarget: ShareTarget) {
        val activity = requireActivity()
        when (shareTarget) {
            ShareTarget.SaveToMedia -> Unit
            ShareTarget.DelegateToOS -> {
                viewModel.shareVideoToOS(activity, videoUri, SportieShareType.VIDEO_3D)
            }

            is ShareTarget.CustomTarget -> {
                if (workoutShareHelper.hasSharePlatformApp(activity, shareTarget.appId).not()) {
                    Toast.makeText(
                        activity.applicationContext,
                        R.string.share_fail,
                        Toast.LENGTH_SHORT
                    ).show()
                } else {
                    viewModel.shareVideoToCustomTarget(
                        activity,
                        videoUri,
                        shareTarget,
                        SportieShareType.VIDEO_3D,
                    )
                }
            }
        }
    }

    private enum class UseCase {
        SHARE_LINK,
        SHARE_IMAGE,
        SHARE_VIDEO,
    }

    companion object {
        const val TAG = "WorkoutShareTargetListDialogFragment"
        private const val EXTRA_USE_CASE = "EXTRA_USE_CASE"
        private const val EXTRA_IMAGE_URI = "EXTRA_IMAGE_URI"
        private const val EXTRA_VIDEO_URI = "EXTRA_VIDEO_URI"
        private const val EXTRA_SPORTIE_SELECTION = "EXTRA_SPORTIE_SELECTION"
        private const val EXTRA_NUM_PHOTOS_ADDED = "EXTRA_NUM_PHOTOS_ADDED"

        private const val TARGET_SAVE_TO_PICTURES = "SaveToPictures"

        @JvmStatic
        fun newInstanceForLinkSharing(
            workoutHeader: WorkoutHeader,
            sportieShareSource: SportieShareSource,
            numPhotosAdded: Int = 0
        ): WorkoutShareTargetListDialogFragment {
            return WorkoutShareTargetListDialogFragment()
                .apply {
                    arguments = bundleOf(
                        EXTRA_USE_CASE to UseCase.SHARE_LINK,
                        STTConstants.ExtraKeys.WORKOUT_HEADER to
                            workoutHeader.toBuilder().polyline(null).build(),
                        STTConstants.ExtraKeys.SPORTIE_SHARE_SOURCE to sportieShareSource,
                        EXTRA_NUM_PHOTOS_ADDED to numPhotosAdded
                    )
                }
        }

        @JvmStatic
        fun newInstanceForImageSharing(
            workoutHeader: WorkoutHeader,
            uri: Uri,
            sportieSelection: SportieSelection,
            numPhotosAdded: Int
        ): WorkoutShareTargetListDialogFragment {
            return WorkoutShareTargetListDialogFragment()
                .apply {
                    arguments = bundleOf(
                        EXTRA_USE_CASE to UseCase.SHARE_IMAGE,
                        STTConstants.ExtraKeys.WORKOUT_HEADER to
                            workoutHeader.toBuilder().polyline(null).build(),
                        EXTRA_IMAGE_URI to uri,
                        EXTRA_SPORTIE_SELECTION to sportieSelection,
                        EXTRA_NUM_PHOTOS_ADDED to numPhotosAdded
                    )
                }
        }

        @JvmStatic
        fun newInstanceForVideoSharing(uri: Uri): WorkoutShareTargetListDialogFragment {
            return WorkoutShareTargetListDialogFragment()
                .apply {
                    arguments = bundleOf(
                        EXTRA_USE_CASE to UseCase.SHARE_VIDEO,
                        EXTRA_VIDEO_URI to uri,
                    )
                }
        }
    }
}
