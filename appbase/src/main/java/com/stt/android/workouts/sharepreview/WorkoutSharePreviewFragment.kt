package com.stt.android.workouts.sharepreview

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Size
import android.util.SparseArray
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.collection.SparseArrayCompat
import androidx.core.os.BundleCompat
import androidx.core.view.MenuProvider
import androidx.core.view.isNotEmpty
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager.widget.ViewPager
import com.google.android.material.snackbar.Snackbar
import com.stt.android.R
import com.stt.android.colorfultrack.HeartRateWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PaceWorkoutColorfulTrackLoader
import com.stt.android.colorfultrack.PowerWorkoutColorfulTrackLoader
import com.stt.android.common.ui.SimpleProgressDialogFragment
import com.stt.android.common.ui.observeK
import com.stt.android.common.ui.observeNotNull
import com.stt.android.controllers.UserSettingsController
import com.stt.android.databinding.FragmentWorkoutSharePreviewBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.extensions.openAppSettings
import com.stt.android.infomodel.SummaryItem
import com.stt.android.maps.MapSnapshotter
import com.stt.android.multimedia.sportie.SportieAddPhoto
import com.stt.android.multimedia.sportie.SportieAspectRatio
import com.stt.android.multimedia.sportie.SportieDiveTrack
import com.stt.android.multimedia.sportie.SportieImage
import com.stt.android.multimedia.sportie.SportieInfo
import com.stt.android.multimedia.sportie.SportieItem
import com.stt.android.multimedia.sportie.SportieMap
import com.stt.android.multimedia.sportie.SportieOverlayViewBase
import com.stt.android.multimedia.sportie.SportieSelection
import com.stt.android.multimedia.sportie.SportieShareSource
import com.stt.android.ui.extensions.forcePortraitOrientationOnCompactDisplaySizeClasses
import com.stt.android.ui.extensions.shortToast
import com.stt.android.ui.utils.PagerBulletStripUtility
import com.stt.android.ui.utils.setOnClickListenerThrottled
import com.stt.android.utils.AddImagePermissionsUtil
import com.stt.android.utils.FlavorUtils
import com.stt.android.utils.ImagePicker
import com.stt.android.utils.PermissionUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.map
import com.stt.android.utils.toCompat
import com.stt.android.utils.toNonCompat
import com.stt.android.workouts.details.values.WorkoutValue
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareTargetListDialogFragment
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import pub.devrel.easypermissions.EasyPermissions
import timber.log.Timber
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class WorkoutSharePreviewFragment :
    Fragment(),
    SportieOverlayViewClickListener,
    ViewPager.OnPageChangeListener,
    EasyPermissions.PermissionCallbacks,
    ImagePicker.Listener {
    @Inject
    lateinit var userSettingsController: UserSettingsController

    @Inject
    lateinit var workoutShareHelper: WorkoutShareHelper

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    internal lateinit var localBroadcastManager: LocalBroadcastManager

    private var sportieShareSource: SportieShareSource? = SportieShareSource.UNKNOWN

    private lateinit var workoutHeader: WorkoutHeader

    private var adapter: WorkoutSharePreviewAdapter? = null

    private var sportieInfos: SparseArrayCompat<SportieInfo> = SparseArrayCompat()

    private val viewModel: WorkoutSharePreviewViewModel by viewModels()

    private val shareLinkViewModel: ShareLinkViewModel by activityViewModels()

    private var isEditModeEnabled = false

    private lateinit var binding: FragmentWorkoutSharePreviewBinding

    @Inject
    lateinit var heartRateWorkoutColorfulTrackLoader: HeartRateWorkoutColorfulTrackLoader

    @Inject
    lateinit var paceWorkoutColorfulTrackLoader: PaceWorkoutColorfulTrackLoader

    @Inject
    lateinit var powerWorkoutColorfulTrackLoader: PowerWorkoutColorfulTrackLoader

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentWorkoutSharePreviewBinding.inflate(inflater)
        return binding.root
    }

    @SuppressLint("SourceLockedOrientationActivity")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        requireActivity().apply {
            // it's quite weird to request orientation here, but since this is the only fragment
            // used in both suunto apps and sport tracker app, that's might be the reason.
            // since video fragment's layout is too narrow in suunto china app, so we force portrait here.
            if (FlavorUtils.isSuuntoAppChina) {
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_PORTRAIT
            } else {
                binding.rootContainer.forcePortraitOrientationOnCompactDisplaySizeClasses(this)
            }
        }
        val workoutHeaderId = arguments?.getInt(STTConstants.ExtraKeys.WORKOUT_ID) ?: 0
        viewModel.loadWorkoutHeader(workoutHeaderId)
        var currentItemIndex = 0
        viewModel.initWorkoutHeaderEvent.observe(viewLifecycleOwner) { header ->
            if (header == null) {
                Timber.w("WorkoutSharePreviewActivity finished because workout header was null")
                requireActivity().finish()
            }
            // set a default header
            workoutHeader = header ?: WorkoutHeader.builder().build()

            setMenu()
            val defaultInitialItemIndex =
                if (workoutHeader.isPolylineEmpty) 0 else 1
            currentItemIndex =
                savedInstanceState?.getInt(CURRENT_ITEM_INDEX, defaultInitialItemIndex)
                    ?: arguments?.getInt(CURRENT_ITEM_INDEX, defaultInitialItemIndex) ?: 0
            viewModel.sendScreenAnalytics(workoutHeader)

            // workoutheader is null ,can't updateLoadingState
            viewModel.loadingState.observeNotNull(this) {
                updateLoadingState(it)
            }
            // mapSnapshotSize == null, don't load SportieItems
            if (viewModel.mapSnapshotSize != null) {
                loadSportieItemsIfInternet(currentItemIndex)
            }
        }

        val tmpSparseArray =
            savedInstanceState?.let {
                BundleCompat.getSparseParcelableArray(
                    it,
                    CURRENT_SPORTIE_INFO,
                    SportieInfo::class.java
                )
            } ?: SparseArray()

        val defaultSummaryItemsNames =
            arguments?.getStringArrayList(CURRENT_SUMMARY_ITEMS) ?: arrayListOf()

        val defaultSummaryItems =
            SummaryItem.entries.filter { it.name in defaultSummaryItemsNames }

        sportieInfos = tmpSparseArray.toCompat()

        viewModel.sportieItemsLiveData.observeNotNull(this) { sportieItems ->
            if (::workoutHeader.isInitialized) {
                showMedia(sportieItems, viewModel.currentItemIndex, defaultSummaryItems)
                showShareLinks(sportieItems)
            }
        }

        viewModel.shareImageLiveData.observeNotNull(this) { (uri, sportieSelection) ->
            onShareImage(uri, sportieSelection)
        }

        viewModel.shareImageErrorEvent.observeNotNull(this) {
            showGenericErrorSnackBar()
        }

        viewModel.setEditModeLiveData.observeNotNull(this) { enabled ->
            adapter?.setEditMode(enabled)
            binding.imagePreviewViewPager.scrollingEnabled = !enabled
            isEditModeEnabled = enabled
            activity?.invalidateMenu()
        }

        viewModel.workoutHeaderLiveData.observeNotNull(this) {
            workoutHeader = it

            localBroadcastManager.sendBroadcast(
                Intent(STTConstants.BroadcastActions.WORKOUT_UPDATED)
                    .putExtra(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeader.id)
                    .putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, workoutHeader)
            )

            activity?.setResult(
                STTConstants.RequestResult.WORKOUT_EDITED,
                Intent().putExtra(
                    STTConstants.ExtraKeys.WORKOUT_HEADER,
                    workoutHeader
                )
            )
        }

        viewModel.shareLinkErrorEvent.observeNotNull(this) {
            showGenericErrorSnackBar()
        }

        viewModel.shareLinkReady.observeNotNull(this) { isSummary ->
            shareLink(isSummary)
        }

        viewModel.shareLinkLoading.observeNotNull(this) { isLoading ->
            if (isLoading) {
                val dialog =
                    SimpleProgressDialogFragment.newInstance(getString(R.string.creating_share_link))
                dialog.show(childFragmentManager, SimpleProgressDialogFragment.FRAGMENT_TAG)
            } else {
                val fm = childFragmentManager
                val fragment =
                    fm.findFragmentByTag(SimpleProgressDialogFragment.FRAGMENT_TAG) as? DialogFragment
                fragment?.dismiss()
            }
        }

        viewModel.onAddPhotoClicked.observeK(this) { openImagePicker() }

        binding.shareImageBtn.setOnClickListenerThrottled {
            // On API 29 and above we don't need any extra permissions for basic sportie sharing
            if (Build.VERSION.SDK_INT >= 29 || EasyPermissions.hasPermissions(
                    requireContext(),
                    *PermissionUtils.STORAGE_PERMISSIONS
                )
            ) {
                handleShareImage()
            } else {
                requestStoragePermission(
                    PermissionUtils.STORAGE_PERMISSIONS,
                    SHARE_IMAGE_PERMISSION_REQUEST_CODE,
                    R.string.storage_permission_rationale
                )
            }
        }

        viewModel.noNetworkErrorEvent.observeK(this) {
            showNoConnectionSnackBar(currentItemIndex)
        }

        binding.imagePreviewViewPager.addOnLayoutChangeListener { view, _, _, _, _, _, _, _, _ ->
            val hasChanged = viewModel.mapSnapshotSize?.let {
                it.width != view.width || it.height != view.height
            } ?: true
            viewModel.mapSnapshotSize = Size(view.width, view.height)
            if (this::workoutHeader.isInitialized && hasChanged) {
                loadSportieItemsIfInternet(currentItemIndex)
            }
        }

        if (arguments?.getBoolean(EXTRA_RUN_MAP_SNAPSHOTTER, true) == true) {
            lifecycleScope.launch {
                viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                    mapSnapshotter.runSnapshotterEngine(requireContext().applicationContext)
                }
            }
        }

        lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.reviewImageFailedEvent.collect {
                    if (it) {
                        showReviewFailedSnackBar()
                    }
                }
            }
        }

        observeAspectRatioChanges()
        // deal click back
        onBackPressed()
    }

    override fun onResume() {
        super.onResume()
        if (adapter?.getSelectedItem() !is SportieAddPhoto) {
            adapter?.showEditIcons()
        }
    }

    private fun showShareLinks(sportieItems: List<SportieItem>) {
        val enable3dVideoLinkSharing = arguments?.getBoolean(EXTRA_3D_VIDEO_LINK_SHARING) == true
        val support3DVideo = workoutShareHelper.support3DVideo()
        val show3D =
            enable3dVideoLinkSharing && support3DVideo && sportieItems.filterIsInstance<SportieMap>()
                .any { it.geoPoints.size > 1 }
        binding.shareImageBtn.visibility = View.VISIBLE
        binding.shareSummaryLink.visibility = View.VISIBLE
        if (show3D) binding.share3DLink.isVisible = true

        if (workoutHeader.activityType.isDiving) {
            binding.shareSummaryLink.isVisible = false
            binding.share3DLink.isVisible = false
        }

        binding.shareSummaryLink.setOnClickListenerThrottled {
            viewModel.setSharingLinkIfPrivate(workoutHeader, isSummary = true)
        }
        binding.share3DLink.setOnClickListenerThrottled {
            viewModel.setSharingLinkIfPrivate(workoutHeader, isSummary = false)
        }
        shareLinkViewModel.updateConfig {
            copy(
                showSummaryLink = binding.shareSummaryLink.isVisible,
                show3DLink = binding.share3DLink.isVisible
            )
        }
    }

    private fun showReviewFailedSnackBar() {
        val snackbar = Snackbar.make(
            binding.rootContainer,
            R.string.review_failed,
            Snackbar.LENGTH_SHORT
        )
        snackbar.show()
    }

    private fun setMenu() {
        requireActivity().addMenuProvider(
            object : MenuProvider {

                override fun onPrepareMenu(menu: Menu) {
                    super.onPrepareMenu(menu)
                    if (showMenu()) {
                        adapter?.let {
                            menu.findItem(R.id.toggle_aspect_ratio).isVisible =
                                !isEditModeEnabled && it.getSelectedItem() is SportieImage
                            menu.findItem(R.id.enable_edit_mode).isVisible =
                                !isEditModeEnabled && it.getSelectedItem() !is SportieAddPhoto
                            menu.findItem(R.id.add_photo).isVisible = !isEditModeEnabled
                            menu.findItem(R.id.save_edittext).isVisible = isEditModeEnabled
                        }
                    } else {
                        menu.setGroupVisible(R.id.group_menu, false)
                        // when longScreenshotFragment is selected, disableEdit
                        if (isEditModeEnabled) {
                            disableEditMode()
                        }
                    }
                }

                override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                    menuInflater.inflate(R.menu.menu_share_preview, menu)
                }

                override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                    when (menuItem.itemId) {
                        R.id.toggle_aspect_ratio -> {
                            viewModel.changeSportieAspectRatio()
                        }

                        R.id.enable_edit_mode -> {
                            viewModel.setEditMode(true)
                        }

                        R.id.save_edittext -> {
                            viewModel.setEditMode(false)
                        }

                        R.id.add_photo -> {
                            viewModel.onAddPhotoClicked()
                        }

                        android.R.id.home -> {
                        }
                    }
                    return true
                }
            },
            viewLifecycleOwner,
            Lifecycle.State.STARTED
        )
    }

    /***
     * only WorkoutSharePreviewFragment should show menu
     */
    private fun showMenu(): Boolean {
        val fragments = requireActivity().supportFragmentManager.fragments
        for (fragment in fragments) {
            if (fragment.isAdded && fragment.isResumed && fragment is WorkoutSharePreviewFragment) {
                return true
            }
        }
        return false
    }

    private fun showNoConnectionSnackBar(currentItemIndex: Int) {
        setShareButtonsEnabled(false)
        val snackbar = Snackbar.make(
            binding.rootContainer,
            R.string.no_network_error,
            Snackbar.LENGTH_INDEFINITE
        )
        snackbar.setAction(R.string.retry_action) {
            snackbar.dismiss()
            loadSportieItemsIfInternet(currentItemIndex)
        }
        snackbar.show()
    }

    private fun showGenericErrorSnackBar() {
        val snackbar = Snackbar.make(
            binding.rootContainer,
            R.string.error_generic_try_again,
            Snackbar.LENGTH_INDEFINITE
        )
        snackbar.setAction(R.string.ok) {
            snackbar.dismiss()
        }
        snackbar.show()
    }

    private fun loadSportieItemsIfInternet(currentItemIndex: Int) {
        viewModel.checkWorkoutSyncedAndLoadSportieItems(
            workoutHeader = workoutHeader,
            sendAddPhotoAnalytics = false,
            selectPosition = currentItemIndex
        )
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)

        adapter?.let {
            outState.putInt(CURRENT_ITEM_INDEX, it.selectedPosition)
            outState.putSparseParcelableArray(
                CURRENT_SPORTIE_INFO,
                it.getCurrentSportieInfo().toNonCompat()
            )
        }
    }

    private fun openImagePicker() {
        // because app declared camera permission in manifest, need to request it dynamically
        if (EasyPermissions.hasPermissions(
                requireContext(),
                *AddImagePermissionsUtil.chooseImagePermission
            ) && this::workoutHeader.isInitialized
        ) {
            ImagePicker.pickImage(requireActivity())
        } else {
            requestStoragePermission(
                AddImagePermissionsUtil.chooseImagePermission,
                ADD_IMAGE_PERMISSION_REQUEST_CODE,
                AddImagePermissionsUtil.requestImagePermissionRationalResId
            )
        }
    }

    private fun getSportieAspectRatio(): SportieAspectRatio =
        viewModel.sportieAspectRatio.value ?: SportieAspectRatio.UNKNOWN

    private fun shareLink(isSummary: Boolean = false) {
        if (sportieShareSource == null) {
            Timber.w("share fail,because sportieShareSource is null")
        }
        sportieShareSource =
            if (isSummary) SportieShareSource.WORKOUT_SUMMARY else SportieShareSource.WORKOUT_DETAILS
        sportieShareSource?.let {
            // china flavor should show different platform items, other jump to the system share
            if (workoutShareHelper.hasCustomIntentHandling()) {
                WorkoutShareTargetListDialogFragment.newInstanceForLinkSharing(
                    workoutHeader,
                    it,
                    viewModel.numPhotosAdded
                ).show(childFragmentManager, "WorkoutShareTargetListDialogFragment")
            } else {
                workoutShareHelper.sendImplicitWorkoutLinkShareIntent(
                    requireActivity(),
                    workoutHeader,
                    it,
                    viewModel.numPhotosAdded
                )
            }
        }
    }

    private fun addPageIndicatorIfNeeded(size: Int) {
        if (size == 0) {
            binding.imagesIndicator.visibility = View.INVISIBLE
        } else if (size > 1) {
            binding.imagesIndicator.visibility = View.VISIBLE
            if (binding.imagesIndicator.isNotEmpty()) {
                binding.imagesIndicator.removeAllViews()
            }
            val bullets = PagerBulletStripUtility.updateBulletStrip(
                size,
                binding.imagesIndicator,
                binding.imagePreviewViewPager
            )
            binding.imagePreviewViewPager.addOnPageChangeListener(
                PagerBulletStripUtility.BulletPageChangeListener(bullets)
            )
        }
    }

    private fun showMedia(
        items: List<SportieItem>,
        imageIndex: Int,
        initialSummaryItems: List<SummaryItem>,
    ) {
        val defaultSportieInfo = adapter?.getCurrentSportieInfo() ?: sportieInfos
        adapter = WorkoutSharePreviewAdapter(
            items,
            userSettingsController.settings.measurementUnit,
            this,
            getSportieAspectRatio(),
            imageIndex,
            defaultSportieInfo,
            initialSummaryItems,
            heartRateWorkoutColorfulTrackLoader,
            paceWorkoutColorfulTrackLoader,
            powerWorkoutColorfulTrackLoader,
        ).apply {
            binding.imagePreviewViewPager.adapter = this
            addPageIndicatorIfNeeded(count)
        }

        setShareButtonsEnabled(true)
        binding.imagePreviewViewPager.addOnPageChangeListener(this)
        binding.imagePreviewViewPager.setCurrentItem(imageIndex, true)
    }

    private fun observeAspectRatioChanges() {
        viewModel.sportieAspectRatio.observeNotNull(this) { aspectRatio ->
            adapter?.refreshAspectRatio(aspectRatio)
            activity?.invalidateMenu()
        }
    }

    private fun handleShareImage() {
        val adapter = adapter ?: return

        val itemPosition = binding.imagePreviewViewPager.currentItem
        val sportieInfo = adapter.getCurrentSportieInfo()[itemPosition] ?: return

        val sportieItem = when (val sportieItem = adapter.items[itemPosition]) {
            is SportieMap -> adapter.getColorfulPolylines(itemPosition)
                ?.let { sportieItem.copy(mapSnapshotSpec = it) }
                ?: sportieItem

            is SportieDiveTrack -> adapter.getDiveTrack(itemPosition)
                ?.let { sportieItem.copy(snapshot = it) }
                ?: sportieItem

            is SportieImage,
            is SportieAddPhoto -> sportieItem
        }
        viewModel.shareImage(requireContext(), sportieItem, sportieInfo, getSportieAspectRatio())
    }

    private fun onShareImage(uri: Uri, sportieSelection: SportieSelection) {
        // china flavor should show different platform items, other jump to the system share
        if (workoutShareHelper.hasCustomIntentHandling()) {
            WorkoutShareTargetListDialogFragment.newInstanceForImageSharing(
                workoutHeader,
                uri,
                sportieSelection,
                viewModel.numPhotosAdded
            ).show(childFragmentManager, "WorkoutShareTargetListDialogFragment")
        } else {
            workoutShareHelper.sendImplicitImageShareIntent(
                requireActivity(),
                workoutHeader,
                uri,
                sportieSelection,
                viewModel.numPhotosAdded
            )
        }
    }

    private fun requestStoragePermission(
        permissions: Array<String>,
        requestCode: Int,
        rationalResId: Int
    ) {
        val requestPermissionsStarted = PermissionUtils.requestPermissionsIfNeeded(
            this,
            permissions,
            getString(rationalResId),
            requestCode
        )
        if (!requestPermissionsStarted) {
            onPermissionsGranted(requestCode, listOf(*permissions))
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        // Forward results to EasyPermissions
        EasyPermissions.onRequestPermissionsResult(requestCode, permissions, grantResults, this)
    }

    override fun onPermissionsGranted(requestCode: Int, perms: List<String>) {
        when (requestCode) {
            SHARE_IMAGE_PERMISSION_REQUEST_CODE -> handleShareImage()
            ADD_IMAGE_PERMISSION_REQUEST_CODE -> {
                // camera and storage permission have to both grant
                if (AddImagePermissionsUtil.imagePermissionsGranted(perms))
                    openImagePicker()
            }

            else -> Timber.w("Unknown request code [%d] onPermissionGranted %s", requestCode, perms)
        }
    }

    override fun onPermissionsDenied(requestCode: Int, perms: MutableList<String>) {
        showPermissionErrorMessage(requestCode, perms)
    }

    private fun showPermissionErrorMessage(requestCode: Int, perms: List<String>) {
        val rationalRes =
            when (requestCode) {
                SHARE_IMAGE_PERMISSION_REQUEST_CODE -> R.string.storage_permission_rationale_picker
                ADD_IMAGE_PERMISSION_REQUEST_CODE -> AddImagePermissionsUtil.getRationalPurposeResIdAfterRefused(
                    perms
                )

                else -> R.string.storage_permission_rationale_picker
            }
        binding.root.apply {
            val snackBar = Snackbar.make(
                this,
                rationalRes,
                Snackbar.LENGTH_LONG
            )
            snackBar.setAction(R.string.settings) {
                requireContext().openAppSettings()
            }
            snackBar.show()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            WorkoutValuesPickingActivity.INTENT_WORKOUT_VALUE_REQUEST_CODE -> {
                data?.apply {
                    val selectedTextViewIndex = getIntExtra(
                        WorkoutValuesPickingActivity.INTENT_EXTRA_SELECTED_TEXT_VIEW_INDEX,
                        0
                    )

                    val selectedWorkoutValueIndex = getIntExtra(
                        WorkoutValuesPickingActivity.INTENT_EXTRA_WORKOUT_VALUE_FINAL_INDEX_KEY,
                        0
                    )

                    adapter?.onWorkoutValueChange(selectedTextViewIndex, selectedWorkoutValueIndex)
                        ?: run {
                            // adapter is not initialized yet, but we might have a saved selected value to update
                            sportieInfos = sportieInfos.map { _, sportieInfo ->
                                when (selectedTextViewIndex) {
                                    0 -> sportieInfo.copy(firstDataIndex = selectedWorkoutValueIndex)
                                    1 -> sportieInfo.copy(secondDataIndex = selectedWorkoutValueIndex)
                                    2 -> sportieInfo.copy(thirdDataIndex = selectedWorkoutValueIndex)
                                    else -> sportieInfo
                                }
                            }
                        }
                }
            }

            WorkoutGraphPickingActivity.INTENT_WORKOUT_GRAPH_REQUEST_CODE -> {
                data?.apply {
                    val selectedGraphIndex = getIntExtra(
                        WorkoutGraphPickingActivity.INTENT_EXTRA_WORKOUT_GRAPH_FINAL_INDEX_KEY,
                        SportieOverlayViewBase.DEFAULT_GRAPH_INDEX
                    )
                    adapter?.onWorkoutGraphChange(selectedGraphIndex) ?: run {
                        // adapter is not initialized yet, but we might have a saved selected value to update
                        sportieInfos = sportieInfos.map { _, sportieInfo ->
                            sportieInfo.copy(graphIndex = selectedGraphIndex)
                        }
                    }
                }
            }

            EditActivityTypeAndStartTimeDisplayActivity.REQUEST_CODE -> {
                data?.apply {
                    val checkNothing = getBooleanExtra(
                        EditActivityTypeAndStartTimeDisplayActivity.CHECK_NOTHING,
                        false
                    )
                    val checkActivityType =
                        getBooleanExtra(
                            EditActivityTypeAndStartTimeDisplayActivity.CHECK_ACTIVITY_TYPE,
                            false
                        )
                    val checkActivityStartTime = getBooleanExtra(
                        EditActivityTypeAndStartTimeDisplayActivity.CHECK_ACTIVITY_START_TIME,
                        false
                    )
                    adapter?.onShowActivityTypeAndStartTimeStateChange(
                        checkNothing,
                        checkActivityType,
                        checkActivityStartTime
                    )
                }
            }

            STTConstants.RequestCodes.PICK_WORKOUT_PICTURE -> {
                ImagePicker.onActivityResult(requireActivity(), requestCode, resultCode, data, this)
            }
        }
    }

    private fun onBackPressed() {
        activity?.onBackPressedDispatcher?.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    if (isEditModeEnabled) {
                        disableEditMode()
                    } else {
                        activity?.finish()
                    }
                }
            }
        )
    }

    private fun disableEditMode() {
        adapter?.setEditMode(false)
        binding.imagePreviewViewPager.scrollingEnabled = true
        isEditModeEnabled = false
        activity?.invalidateMenu()
    }

    override fun onWorkoutValueClick(
        activityWorkoutValues: List<WorkoutValue>,
        initialIndex: Int,
        selectedTextViewIndex: Int
    ) {
        // when click workoutValue is activityType and startTime
        if (selectedTextViewIndex == SportieOverlayViewBase.FOURTH_TEXT_VIEW_INDEX && initialIndex == activityWorkoutValues.size - 1) {
            val intent =
                Intent(requireContext(), EditActivityTypeAndStartTimeDisplayActivity::class.java)
            activityWorkoutValues.getOrNull(initialIndex).let {
                intent.putExtra(EditActivityTypeAndStartTimeDisplayActivity.WORK_VALUE, it)
                startActivityForResult(
                    intent,
                    EditActivityTypeAndStartTimeDisplayActivity.REQUEST_CODE
                )
            }
        } else {
            // Edit other workoutValue remove activity type and startTime
            val newActivityWorkoutValues =
                activityWorkoutValues.subList(0, activityWorkoutValues.size - 1)
            startActivityForResult(
                WorkoutValuesPickingActivity.newStartIntent(
                    requireContext(),
                    newActivityWorkoutValues,
                    initialIndex,
                    selectedTextViewIndex
                ),
                WorkoutValuesPickingActivity.INTENT_WORKOUT_VALUE_REQUEST_CODE
            )
        }
    }

    override fun onErrorSettingWorkoutValueText() =
        requireContext().shortToast(R.string.error_generic)

    override fun onGraphClick(graphOptions: List<WorkoutShareGraphOption>, initialIndex: Int) {
        startActivityForResult(
            WorkoutGraphPickingActivity.newStartIntent(
                requireContext(),
                graphOptions,
                initialIndex
            ),
            WorkoutGraphPickingActivity.INTENT_WORKOUT_GRAPH_REQUEST_CODE
        )
    }

    override fun onAddPhotoClicked() {
        viewModel.onAddPhotoClicked()
    }

    override fun onPageScrollStateChanged(p0: Int) {
        // do nothing
    }

    override fun onPageScrolled(p0: Int, p1: Float, p2: Int) {
        // do nothing
    }

    override fun onPageSelected(position: Int) {
        adapter?.selectedPosition = position
        activity?.invalidateMenu()
        if (adapter?.getSelectedItem() !is SportieAddPhoto) {
            adapter?.showEditIcons()
        }
        binding.addPhotosHint.visibility =
            if (adapter?.getSelectedItem() is SportieAddPhoto) View.INVISIBLE else View.VISIBLE
        binding.shareImageBtn.isEnabled = adapter?.getSelectedItem() !is SportieAddPhoto
    }

    private fun updateLoadingState(state: BaseWorkoutSharePreviewViewModel.LoadingState) {
        // Hide/show loading spinner
        binding.loadingSpinner.visibility =
            if (state != BaseWorkoutSharePreviewViewModel.LoadingState.NOT_LOADING) {
                View.VISIBLE
            } else {
                View.GONE
            }

        // Show overlay when waiting for sharing dialog to appear
        binding.loadingOverlay.visibility = when (state) {
            BaseWorkoutSharePreviewViewModel.LoadingState.LOADING_SHARING_IMAGES -> View.VISIBLE
            else -> View.GONE
        }

        // Disable share buttons while loading
        setShareButtonsEnabled(state == BaseWorkoutSharePreviewViewModel.LoadingState.NOT_LOADING)

        // Also menu items' visibility depend on loading state
        activity?.invalidateMenu()
    }

    private fun setShareButtonsEnabled(isEnabled: Boolean) {
        binding.shareImageBtn.isEnabled = isEnabled
        if (this::workoutHeader.isInitialized) {
            val shareLinkEnabled = workoutHeader.isSynced && isEnabled
            binding.share3DLink.isEnabled = shareLinkEnabled
            binding.shareSummaryLink.isEnabled = shareLinkEnabled
        }
    }

    override fun onImagePicked(uri: Uri) {
        if (this::workoutHeader.isInitialized) {
            viewModel.addWorkoutPhotoFromUri(uri, workoutHeader)
        }
    }

    override fun onImagePicked(file: File) {
        if (this::workoutHeader.isInitialized) {
            viewModel.addWorkoutPhoto(file, workoutHeader)
        }
    }

    companion object {
        private const val SHARE_IMAGE_PERMISSION_REQUEST_CODE = 100
        private const val ADD_IMAGE_PERMISSION_REQUEST_CODE = 101
        private const val CURRENT_SUMMARY_ITEMS = "com.stt.android.CURRENT_SUMMARY_ITEMS"
        private const val CURRENT_ITEM_INDEX = "com.stt.android.CURRENT_ITEM_INDEX"
        private const val CURRENT_SPORTIE_INFO = "com.stt.android.CURRENT_SPORTIE_INFO"
        private const val EXTRA_SHARE_SOURCE = "EXTRA_SHARE_SOURCE"
        private const val EXTRA_RUN_MAP_SNAPSHOTTER = "EXTRA_RUN_MAP_SNAPSHOTTER"
        private const val EXTRA_3D_VIDEO_LINK_SHARING = "EXTRA_3D_VIDEO_LINK_SHARING"

        fun newInstance(
            workoutHeaderId: Int?,
            itemIndex: Int,
            workoutDetails: SportieShareSource?,
            defaultSummaryItems: ArrayList<String>,
            runMapSnapshotter: Boolean = true,
            enable3dVideoLinkSharing: Boolean = true,
        ): WorkoutSharePreviewFragment {
            val args = Bundle().apply {
                putInt(STTConstants.ExtraKeys.WORKOUT_ID, workoutHeaderId ?: 0)
                putInt(CURRENT_ITEM_INDEX, itemIndex)
                putParcelable(EXTRA_SHARE_SOURCE, workoutDetails)
                putStringArrayList(CURRENT_SUMMARY_ITEMS, defaultSummaryItems)
                putBoolean(EXTRA_RUN_MAP_SNAPSHOTTER, runMapSnapshotter)
                putBoolean(EXTRA_3D_VIDEO_LINK_SHARING, enable3dVideoLinkSharing)
            }

            return WorkoutSharePreviewFragment().apply {
                arguments = args
            }
        }
    }
}
