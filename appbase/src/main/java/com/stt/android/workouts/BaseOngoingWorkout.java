package com.stt.android.workouts;

import android.location.Location;
import android.os.SystemClock;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import androidx.annotation.WorkerThread;
import com.google.android.gms.maps.model.LatLng;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.google.maps.android.PolyUtil;
import com.stt.android.domain.Point;
import com.stt.android.domain.advancedlaps.Statistics;
import com.stt.android.domain.user.CadenceDataSource;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.MeasurementUnit;
import static com.stt.android.domain.user.MeasurementUnitKt.KILOMETERS_TO_METERS;
import static com.stt.android.domain.user.MeasurementUnitKt.MILES_TO_METERS;
import com.stt.android.domain.user.workoutextension.SlopeSkiSummary;
import com.stt.android.domain.weather.WeatherConditions;
import com.stt.android.domain.workout.ActivityType;
import com.stt.android.domain.workout.SharingOption;
import com.stt.android.domain.workout.WorkoutCadenceEvent;
import com.stt.android.domain.workout.WorkoutGeoPoint;
import com.stt.android.domain.workout.WorkoutHrEvent;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.domain.workouts.extensions.WeatherExtension;
import com.stt.android.domain.workouts.extensions.WorkoutExtension;
import com.stt.android.domain.workouts.tss.TSS;
import com.stt.android.hr.HeartRateEvent;
import com.stt.android.laps.AutomaticLaps;
import com.stt.android.laps.CompleteLap;
import com.stt.android.laps.Laps;
import com.stt.android.laps.ManualLaps;
import com.stt.android.laps.ParcelableCompleteLap;
import com.stt.android.ski.SlopeSki;
import com.stt.android.ski.SlopeSkiCalculator;
import com.stt.android.tracker.event.Event;
import com.stt.android.utils.CenterpointCalculator;
import com.stt.android.workouts.hardware.steps.StepRateCalculator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class BaseOngoingWorkout {
    /**
     * Amount of times this ongoing workout has been auto recovered.
     */
    private int autoRecovered = 0;

    @SerializedName("state")
    private TrackingState state = TrackingState.NOT_STARTED;

    @SerializedName("activityTypeId")
    private final int activityTypeId;

    /**
     * Duration stored internally as milliseconds. With <code>int</code> we get
     * over 596 hours of workout.
     */
    @SerializedName("duration")
    private int duration;

    @SerializedName("distance")
    private double distance;

    /**
     * Derivated value calculated by {@link #updateAverageSpeed()}
     */
    @SerializedName("averageSpeed")
    private double averageSpeed;

    @SerializedName("energyConsumed")
    private double energyConsumed;

    /**
     * System time of the workout start in milliseconds.
     */
    @SerializedName("startTime")
    private long startTime;

    /**
     * System time of the workout end in milliseconds.
     */
    @SerializedName("endTime")
    private long endTime;

    /**
     * Contains the last available timestamp. Used in {@link BaseOngoingWorkout#updateDuration()}
     * to calculate the proper duration time.
     */
    @SerializedName("lastTimestamp")
    private long lastTimestamp;

    @SerializedName("altitudeOffset")
    private final float altitudeOffset;

    /**
     * We combine cadence event with location updates to calculate the distance and speed, if
     * possible.
     */
    @SerializedName("cadenceDataSource")
    private CadenceDataSource cadenceDataSource;
    @SerializedName("lastCadenceEvent")
    private WorkoutCadenceEvent lastCadenceEvent;
    @SerializedName("distanceSinceLastLocationEvent")
    private float distanceSinceLastLocationEvent;
    @SerializedName("timeSinceLastLocationEvent")
    private long timeSinceLastLocationEvent;
    @SerializedName("lastSpeed")
    private float lastSpeed;

    @SerializedName("lastLocation")
    private Location lastLocation;

    /**
     * Total steps counted while the workout is only in {@link TrackingState#RECORDING}
     */
    @SerializedName("stepCount")
    private int totalStepsInRecordingState;

    private final StepRateCalculator stepRateCalculator = new StepRateCalculator();

    /**
     * Convenient property to hold the location used as starting point. It might
     * be null if there was no location when {@link #start(Location)} was
     * called. <em>Note: Only used for recovering a workout</em>
     */
    @SerializedName("startLocation")
    private Location startLocation;

    /**
     * The index for the {@link WorkoutGeoPoint} that defines the start of the
     * last kilometer speed in the {@link #geoPoints} list.
     */
    @SerializedName("startLastKmIdx")
    private int startLastKmIdx = 0;
    @SerializedName("lastKmSpeed")
    private double lastKmSpeed = 0;
    /**
     * The index for the {@link WorkoutGeoPoint} that defines the start of the
     * last mile speed in the {@link #geoPoints} list.
     */
    @SerializedName("startLastMiIdx")
    private int startLastMiIdx = 0;
    @SerializedName("lastMiSpeed")
    private double lastMiSpeed = 0;

    @SerializedName("longitudeStatistics")
    private final Statistics longitudeStatistics = new Statistics();
    @SerializedName("latitudeStatistics")
    private final Statistics latitudeStatistics = new Statistics();
    @SerializedName("altitudeStatistics")
    private final Statistics altitudeStatistics = new Statistics();
    @SerializedName("speedStatistics")
    private final Statistics speedStatistics = new Statistics();
    @SerializedName("heartRateStatistics")
    private final Statistics heartRateStatistics = new Statistics();
    @SerializedName("cadenceStatistics")
    private final Statistics cadenceStatistics = new Statistics();

    @SerializedName("manualLaps")
    private ManualLaps manualLaps;

    @SerializedName("weather")
    private OngoingWorkoutWeatherConditions weather;

    /*
     * Any list or data structure that holds a list should not be serialized
     * automatically to JSON by the @{link AutosaveOngoingWorkoutController}.
     * Therefore the below elements don't have @JsonProperty annotation
     */
    @Expose(serialize = false, deserialize = false)
    final List<WorkoutGeoPoint> geoPoints = new ArrayList<>();
    @Expose(serialize = false, deserialize = false)
    protected final List<Event> events = Collections.synchronizedList(new ArrayList<Event>());
    @Expose(serialize = false, deserialize = false)
    private final List<WorkoutHrEvent> heartRateEvents =
        Collections.synchronizedList(new ArrayList<WorkoutHrEvent>());
    /**
     * List of pictures taken during this workout
     */
    @SerializedName("pictures")
    private final List<ImageInformation> pictures = new ArrayList<>();
    @Expose(serialize = false, deserialize = false)
    private AutomaticLaps metricAutomaticLaps;
    @Expose(serialize = false, deserialize = false)
    private AutomaticLaps imperialAutomaticLaps;

    @Expose(serialize = false, deserialize = false)
    private final SlopeSkiCalculator slopeSkiCalculator = new SlopeSkiCalculator();

    @Expose(serialize = false, deserialize = false)
    private final AltitudeChangeCalculator altitudeChangeCalculator =
        new AltitudeChangeCalculator();

    private static final boolean INTERNAL_DEBUG = false;

    // Used by GSON
    @SuppressWarnings("unused")
    BaseOngoingWorkout() {
        this.activityTypeId = 0;
        this.altitudeOffset = 0;
    }

    public BaseOngoingWorkout(ActivityType activityType, float altitudeOffset,
        CadenceDataSource cadenceDataSource) {
        this.activityTypeId = activityType.getId();
        this.altitudeOffset = altitudeOffset;
        this.cadenceDataSource = cadenceDataSource;
    }

    public void setCadenceDataSource(CadenceDataSource cadenceDataSource) {
        this.cadenceDataSource = cadenceDataSource;
    }

    public void start(@Nullable Location startLocation) {
        state = TrackingState.RECORDING;
        startTime = System.currentTimeMillis();
        lastTimestamp = startTime;
        if (startLocation != null) {
            startWithLocation(startLocation, startTime);
        } else {
            startWithoutLocation();
        }
        addEvent(Event.EventType.START);
    }

    private void startWithLocation(Location startLocation, long startTime) {
        WorkoutGeoPoint startGeoPoint =
            BaseOngoingWorkout.convertWorkoutGeoPoint(startLocation, startTime);
        addObservationToRunCalculator(startGeoPoint);
        metricAutomaticLaps = new AutomaticLaps(MeasurementUnit.METRIC, startGeoPoint);
        imperialAutomaticLaps = new AutomaticLaps(MeasurementUnit.IMPERIAL, startGeoPoint);

        manualLaps = new ManualLaps(startGeoPoint, Laps.Type.MANUAL, null);

        this.startLocation = startLocation;
        addStartLocation(startGeoPoint);
        this.lastLocation = startLocation;
    }

    protected void addObservationToRunCalculator(WorkoutGeoPoint geoPoint) {
        if (getActivityType().isSlopeSki()) {
            slopeSkiCalculator.addObservation(geoPoint.getMillisecondsInWorkout(),
                geoPoint.getTotalDistance(), geoPoint.getAltitude(),
                geoPoint.getSpeedMetersPerSecond());
        }
    }

    private static WorkoutGeoPoint convertWorkoutGeoPoint(Location startLocation, long startTime) {
        int latitudeE6 = (int) (startLocation.getLatitude() * 1E6);
        int longitudeE6 = (int) (startLocation.getLongitude() * 1E6);
        double altitude = startLocation.getAltitude();
        boolean hasAltitude = startLocation.hasAltitude();
        float course = startLocation.getBearing();
        float speedMetersPerSecond = 0;
        double distance = 0.0;
        int millisecondsInWorkout = 0;
        double totalDistance = 0.0;
        long timestamp = startTime;
        return new WorkoutGeoPoint(latitudeE6, longitudeE6, altitude, hasAltitude,
            speedMetersPerSecond, distance, millisecondsInWorkout, totalDistance, course,
            timestamp);
    }

    protected void startWithoutLocation() {
        // Initially both duration and distance are 0
        int duration = 0;
        double distance = 0.0;
        manualLaps = new ManualLaps(duration, distance, Laps.Type.MANUAL, null);
        metricAutomaticLaps = new AutomaticLaps(MeasurementUnit.METRIC, duration, distance);
        imperialAutomaticLaps = new AutomaticLaps(MeasurementUnit.IMPERIAL, duration, distance);
    }

    /**
     * Call this method to start a workout after it's been recovered from disk
     */
    public void recover() {
        // Add here any other task that needs to be done when this ongoing workout is used to
        // start again after a recover
        if (startLocation != null) {
            WorkoutGeoPoint startGeoPoint = convertWorkoutGeoPoint(startLocation, startTime);
            addObservationToRunCalculator(startGeoPoint);
            metricAutomaticLaps = new AutomaticLaps(MeasurementUnit.METRIC, startGeoPoint);
            imperialAutomaticLaps = new AutomaticLaps(MeasurementUnit.IMPERIAL, startGeoPoint);
        } else {
            metricAutomaticLaps = new AutomaticLaps(MeasurementUnit.METRIC, duration, distance);
            imperialAutomaticLaps = new AutomaticLaps(MeasurementUnit.IMPERIAL, duration, distance);
        }

        List<WorkoutGeoPoint> geoPoints = getRoutePoints();
        int count = geoPoints.size();
        for (int i = 0; i < count; ++i) {
            WorkoutGeoPoint workoutGeoPoint = geoPoints.get(i);
            addObservationToRunCalculator(workoutGeoPoint);
            if (workoutGeoPoint.hasAltitude()) {
                altitudeChangeCalculator.addAltitude(workoutGeoPoint.getAltitude());
            }
        }
        metricAutomaticLaps.updateWithLocations(geoPoints);
        imperialAutomaticLaps.updateWithLocations(geoPoints);
    }

    /**
     * Pauses the current workout (i.e. changes state to paused and sets the end time)
     */
    public void pause() {
        updateDurationsAndDependants();
        changeToPausedState();
    }

    public void changeToPausedState() {
        state = TrackingState.PAUSED;
        endTime = System.currentTimeMillis();
        addEvent(Event.EventType.PAUSE);
    }

    public void autoPause() {
        updateDurationsAndDependants();
        state = TrackingState.AUTO_PAUSED;
        addEvent(Event.EventType.AUTOPAUSE);
    }

    public void resume() {
        state = TrackingState.RECORDING;
        // Reset the last timestamp to now so duration continues from this point
        // in time
        lastTimestamp = System.currentTimeMillis();
        // Reset the last location
        lastLocation = null;
        addEvent(Event.EventType.CONTINUE);
    }

    protected void addEvent(Event.EventType eventType) {
        Event event = new Event(eventType, System.currentTimeMillis(), duration);
        events.add(event);
    }

    /**
     * Ends this workout (i.e. changes the state to not saved and completes laps)
     */
    public void end() {
        state = TrackingState.NOT_SAVED;
        manualLaps.complete(duration, endTime);
        // TODO: maybe complete the automatic laps as well?
    }

    public TrackingState getState() {
        return state;
    }

    public void markAsSaved() {
        state = TrackingState.SAVED;
    }

    public ActivityType getActivityType() {
        return ActivityType.valueOf(activityTypeId);
    }

    /**
     * @return current duration in seconds
     */
    public double getDurationInSeconds() {
        return (double) duration / 1000.0;
    }

    /**
     * @return current duration in milliseconds
     */
    public int getDuration() {
        return duration;
    }

    public List<WorkoutGeoPoint> getRoutePoints() {
        return geoPoints;
    }

    public float getLastSpeed() {
        if (!geoPoints.isEmpty()) {
            return geoPoints.get(geoPoints.size() - 1).getSpeedMetersPerSecond();
        }
        return 0.0F;
    }

    public List<Event> getEvents() {
        return events;
    }

    public List<WorkoutHrEvent> getHeartRateEvents() {
        return heartRateEvents;
    }

    public AutomaticLaps getAutomaticLaps(MeasurementUnit unit) {
        switch (unit) {
            case METRIC:
                return metricAutomaticLaps;
            case IMPERIAL:
                return imperialAutomaticLaps;
            default:
                throw new IllegalArgumentException("No automatic laps for " + unit);
        }
    }

    public ManualLaps getManualLaps() {
        return manualLaps;
    }

    public double getAverageSpeed() {
        return averageSpeed;
    }

    public double getDistance() {
        return distance;
    }

    public double getCurrentSpeed() {
        return speedStatistics.getLastValue();
    }

    public double getLastDistanceUnitSpeed(MeasurementUnit unit) {
        switch (unit) {
            case METRIC:
                return lastKmSpeed;
            case IMPERIAL:
                return lastMiSpeed;
            default:
                throw new IllegalArgumentException("No last distance unit speed for " + unit);
        }
    }

    public double getMaxAltitude() {
        return altitudeStatistics.getMax();
    }

    public double getMinAltitude() {
        return altitudeStatistics.getMin();
    }

    public double getMaxSpeed() {
        return speedStatistics.getMax();
    }

    public long getStartTime() {
        return startTime;
    }

    /**
     * @return The energy consumed during workout in KCal.
     */
    public double getEnergyConsumed() {
        return energyConsumed;
    }

    /**
     * @return the last known altitude in meters
     */
    public double getCurrentAltitude() {
        return altitudeStatistics.getLastValue();
    }

    public int getRecordedStepCount() {
        return totalStepsInRecordingState;
    }

    public long getEndTime() {
        return endTime;
    }

    public int getAverageHeartRate() {
        return (int) heartRateStatistics.getAvg();
    }

    public int getMaxHeartRate() {
        return (int) heartRateStatistics.getMax();
    }

    public ParcelableCompleteLap addManualLap() throws IllegalStateException {
        /*
         * Check we have correct state (laps can not be added to stopped
         * workout.
         */
        if (state == TrackingState.NOT_STARTED || state == TrackingState.NOT_SAVED) {
            throw new IllegalStateException(
                "Lap can't be added in workout which has not been started or is pending to be "
                    + "saved.");
        }
        // We don't need to update the manual laps, as the complete will do it.
        updateDuration();
        updateAutomaticLapsDuration();
        return manualLaps.complete(duration, System.currentTimeMillis());
    }

    public Statistics getLongitudeStatistics() {
        return longitudeStatistics;
    }

    public Statistics getLatitudeStatistics() {
        return latitudeStatistics;
    }

    public Statistics getAltitudeStatistics() {
        return altitudeStatistics;
    }

    public Statistics getSpeedStatistics() {
        return speedStatistics;
    }

    public Statistics getHeartRateStatistics() {
        return heartRateStatistics;
    }

    public Statistics getCadenceStatistics() {
        return cadenceStatistics;
    }

    /**
     * Adds the given energy in KCal to the total of energy consumed in this
     * workout
     *
     * @param additionalEnergyConsumed In KCal
     */
    public void updateEnergyConsumption(double additionalEnergyConsumed) {
        energyConsumed += additionalEnergyConsumed;
    }

    public void updateStepCount(int stepCount) {
        stepRateCalculator.addStepCount(stepCount, SystemClock.elapsedRealtime());
        if (state == TrackingState.RECORDING) {
            totalStepsInRecordingState += stepCount;
        }
    }

    public void updateDurationsAndDependants() {
        if (state == TrackingState.RECORDING) {
            updateDuration();
            manualLaps.updateWithTime(getDuration());
            updateAutomaticLapsDuration();
            updateAverageSpeed();
        }
    }

    private void updateAutomaticLapsDuration() {
        if (state == TrackingState.RECORDING) {
            metricAutomaticLaps.updateWithTime(getDuration());
            imperialAutomaticLaps.updateWithTime(getDuration());
        }
    }

    private void updateDuration() {
        if (state == TrackingState.RECORDING) {
            long currentTimestamp = System.currentTimeMillis();
            duration += (currentTimestamp - lastTimestamp);
            lastTimestamp = currentTimestamp;
        }
    }

    /**
     * @param hr New heart rate data.
     */
    public void updateHeartRate(HeartRateEvent hr) {
        if (state == TrackingState.RECORDING && hr.getHeartRate() != 0) {
            heartRateEvents.add(
                new WorkoutHrEvent(hr.getTimestamp(), hr.getHeartRate(), hr.getRawData(),
                    (long) duration));
            addHeartRateSample(hr.getHeartRate());
        }
    }

    protected void addHeartRateSample(int heartRateSample) {
        heartRateStatistics.addSample(heartRateSample);
    }

    public void addHeartRate(WorkoutHrEvent workoutHrEvent) {
        synchronized (heartRateEvents) {
            heartRateEvents.add(workoutHrEvent);
        }
    }

    public void updateCadence(WorkoutCadenceEvent cadenceEvent) {
        if (state == TrackingState.RECORDING) {
            cadenceStatistics.addSample(cadenceEvent.crankRPM);
            lastCadenceEvent = cadenceEvent;

            // update duration, speed and distance, if the user choose to use cadence as data source
            if (cadenceDataSource == CadenceDataSource.CADENCE) {
                distance += cadenceEvent.distanceInMeters;
                distanceSinceLastLocationEvent += cadenceEvent.distanceInMeters;
                timeSinceLastLocationEvent += cadenceEvent.timeDelta;
                updateDuration();
                speedStatistics.addSample(cadenceEvent.speedInMetersPerSecond);
                updateAverageSpeed();
            }
        }
    }

    /**
     * @param location New {@link Location} data.
     * @return the list of all new complete laps or null if not recording
     */
    public List<CompleteLap> updateLocation(Location location) {
        // DO NOT CONTINUE UNLESS WE ARE IN RECORDING STATE
        if (state == TrackingState.RECORDING) {
            // NOTE: The order of below calls matters so do not change it!
            /*
             * only update workout duration, the laps will be updated on the
             * updateWithLocation calls.
             */
            updateDuration();
            WorkoutGeoPoint geoPoint = updateLocationInternal(location);
            lastLocation = location;
            updateAverageSpeed();
            return updateStatsLapsLastDistance(geoPoint);
        }
        return null;
    }

    @VisibleForTesting
    void addStartLocation(WorkoutGeoPoint geoPoint) {
        synchronized (geoPoints) {
            geoPoints.add(0, geoPoint);
        }
        updateStatsLapsLastDistance(geoPoint);
    }

    protected List<CompleteLap> updateStatsLapsLastDistance(WorkoutGeoPoint geoPoint) {
        updateLocationStatistics(geoPoint);
        manualLaps.updateWithLocation(geoPoint);
        List<CompleteLap> completeLaps = metricAutomaticLaps.updateWithLocation(geoPoint);
        completeLaps.addAll(imperialAutomaticLaps.updateWithLocation(geoPoint));
        updateLastDistanceUnitSpeed();
        return completeLaps;
    }

    private void updateLastDistanceUnitSpeed() {
        int geoPointsSize = geoPoints.size();
        if (geoPointsSize < 1) {
            return;
        }
        int endGeoPointIdx = geoPointsSize - 1;
        WorkoutGeoPoint endGeoPoint = geoPoints.get(endGeoPointIdx);
        startLastKmIdx = getStartLastDistanceIndex(startLastKmIdx, endGeoPointIdx,
            KILOMETERS_TO_METERS);
        startLastMiIdx = getStartLastDistanceIndex(startLastMiIdx, endGeoPointIdx,
            MILES_TO_METERS);
        /*
         * After the loop, startLastKmIdx/startLastMiIdx are pointing to the
         * last index that had a full kilometer/mile (or more).
         */
        WorkoutGeoPoint startKmGeoPoint = geoPoints.get(startLastKmIdx);
        double distance = endGeoPoint.getTotalDistance() - startKmGeoPoint.getTotalDistance();
        int elapsedTime =
            endGeoPoint.getMillisecondsInWorkout() - startKmGeoPoint.getMillisecondsInWorkout();
        lastKmSpeed = elapsedTime == 0 ? 0 : distance / (elapsedTime / 1000.0);

        WorkoutGeoPoint startMiGeoPoint = geoPoints.get(startLastMiIdx);
        distance = endGeoPoint.getTotalDistance() - startMiGeoPoint.getTotalDistance();
        elapsedTime =
            endGeoPoint.getMillisecondsInWorkout() - startMiGeoPoint.getMillisecondsInWorkout();
        lastMiSpeed = elapsedTime == 0 ? 0 : distance / (elapsedTime / 1000.0);
    }

    /**
     * @param startLastDistanceIdx the {@link #geoPoints} index that will be
     * used as start point when going through the list to calculate
     * the new last distance unit index
     * @param endGeoPointIdx the {@link #geoPoints} index that will be used as
     * end point when going through the list to calculate the new
     * last distance unit index
     * @param unitDistanceInMeters the minimum distance that there has to be
     * between the start and end points
     * @return the index for the new start point where the distance between this
     * start and the given end is at least the given
     * unitDistanceInMeters
     */
    private int getStartLastDistanceIndex(int startLastDistanceIdx, int endGeoPointIdx,
        double unitDistanceInMeters) {
        int newStartIdx = startLastDistanceIdx;
        int geoPointsSize = geoPoints.size();
        if (newStartIdx >= geoPointsSize || endGeoPointIdx >= geoPointsSize) {
            throw new IllegalArgumentException("Invalid start or end index. Start index: "
                + newStartIdx
                + ", end index: "
                + endGeoPointIdx
                + ", geo points: "
                + geoPointsSize);
        }
        int nextGeoPointIdx = startLastDistanceIdx + 1;
        if (nextGeoPointIdx >= geoPointsSize) {
            // Avoid index out of bounds if the next point is further max size
            return newStartIdx;
        }
        WorkoutGeoPoint endGeoPoint = geoPoints.get(endGeoPointIdx);
        WorkoutGeoPoint nextGeoPoint = geoPoints.get(nextGeoPointIdx);

        // Calculate the distance between the last location and the next one to
        // the start one
        double distance = endGeoPoint.getTotalDistance() - nextGeoPoint.getTotalDistance();
        // Keep moving forward the start index as far as there's
        // unitDistanceInMeters between the end and the next point
        while (distance > unitDistanceInMeters && nextGeoPointIdx < endGeoPointIdx) {
            // Keep the previous index as it will be the one that contains
            // unitDistanceInMeters or more
            newStartIdx = nextGeoPointIdx;
            nextGeoPointIdx++;
            nextGeoPoint = geoPoints.get(nextGeoPointIdx);
            distance = endGeoPoint.getTotalDistance() - nextGeoPoint.getTotalDistance();
        }
        return newStartIdx;
    }

    /**
     * Updates the ongoing workout distance and adds the location to the list of
     * workout {@link #geoPoints}.
     * <em>Note: {@link #updateDuration()} has to be called before calling this method!</em>
     */
    private WorkoutGeoPoint updateLocationInternal(Location location) {
        float distanceSinceLastLocation = 0;
        float speedMetersPerSecond = 0;
        if (cadenceDataSource == CadenceDataSource.CADENCE && lastCadenceEvent != null) {
            // total distance is already updated in updateCadence()

            distanceSinceLastLocation = distanceSinceLastLocationEvent;
            if (distanceSinceLastLocation <= 0) {
                speedMetersPerSecond = lastSpeed;
            } else {
                speedMetersPerSecond =
                    lastSpeed = 1000 * distanceSinceLastLocation / timeSinceLastLocationEvent;
            }

            distanceSinceLastLocationEvent = 0;
            timeSinceLastLocationEvent = 0;
        } else if (lastLocation != null && !INTERNAL_DEBUG) {
            distanceSinceLastLocation = location.distanceTo(lastLocation);
            distance += distanceSinceLastLocation;
            speedMetersPerSecond = location.getSpeed();
        }

        int longitudeE6 = (int) (location.getLongitude() * 1E6);
        int latitudeE6 = (int) (location.getLatitude() * 1E6);
        double altitude = location.getAltitude() + altitudeOffset;
        float course = location.getBearing();
        boolean hasAltitude = location.hasAltitude();
        WorkoutGeoPoint geoPoint =
            new WorkoutGeoPoint(latitudeE6, longitudeE6, altitude, hasAltitude,
                speedMetersPerSecond, distanceSinceLastLocation, duration, distance, course,
                System.currentTimeMillis());

        // It might be that when the user pressed start we didn't have a start
        // location so add it now
        if (geoPoints.size() == 0) {
            WorkoutGeoPoint fakeStartGeoPoint =
                new WorkoutGeoPoint(latitudeE6, longitudeE6, altitude, hasAltitude, 0, 0, 0, 0,
                    course, startTime);
            // If this is the first location then add a fake one based on
            // this
            addStartLocation(fakeStartGeoPoint);
        }

        addWorkoutGeoPoint(geoPoint);
        addObservationToRunCalculator(geoPoint);
        return geoPoint;
    }

    public void addWorkoutGeoPoint(WorkoutGeoPoint geoPoint) {
        synchronized (geoPoints) {
            geoPoints.add(geoPoint);
        }
    }

    @VisibleForTesting
    void updateAverageSpeed() {
        if (duration == 0) {
            averageSpeed = 0;
        } else {
            averageSpeed = distance / (duration / 1000.0);
        }
    }

    private void updateLocationStatistics(WorkoutGeoPoint location) {
        int longitudeE6 = (int) (location.getLongitude() * 1E6);
        int latitudeE6 = (int) (location.getLatitude() * 1E6);
        double altitude = location.getAltitude();
        float speedMetersPerSecond = location.getSpeedMetersPerSecond();

        if (location.hasAltitude()) {
            altitudeStatistics.addSample(altitude);
            altitudeChangeCalculator.addAltitude(altitude);
        }
        speedStatistics.addSample(speedMetersPerSecond);
        latitudeStatistics.addSample(latitudeE6);
        longitudeStatistics.addSample(longitudeE6);
    }

    public void addWorkoutPictures(List<ImageInformation> pendingPictures) {
        pictures.addAll(pendingPictures);
    }

    public void addWorkoutPicture(ImageInformation imageInfo) {
        pictures.add(imageInfo);
    }

    public List<ImageInformation> getPictures() {
        return pictures;
    }

    public List<WorkoutExtension> getExtensions() {
        List<WorkoutExtension> extensions = new ArrayList<>();
        int workoutId = getWorkoutHeader("doesn't matter", 0, null, null, null).getId();
        if (getActivityType().isSlopeSki()) {
            extensions.add(new SlopeSkiSummary(workoutId, slopeSkiCalculator.calculateRuns()));
        }
        if (weather != null) {
            WeatherExtension weatherExtension = new WeatherExtension(
                workoutId,
                weather.toDomainWeatherConditions()
            );
            extensions.add(weatherExtension);
        }
        return extensions;
    }

    public int getRunCount() {
        return slopeSkiCalculator.getRunCount();
    }

    public SlopeSki calculateRuns() {
        return slopeSkiCalculator.calculateRuns();
    }

    public double getSkiDurationInSeconds() {
        return slopeSkiCalculator.getSkiDurationInSeconds();
    }

    public double getSkiDistanceInMeters() {
        return slopeSkiCalculator.getSkiDistanceInMeters();
    }

    public double getCurrentRunDurationInSeconds() {
        return slopeSkiCalculator.getCurrentRunDurationInSeconds();
    }

    public double getCurrentRunDistanceInMeters() {
        return slopeSkiCalculator.getCurrentRunDistanceInMeters();
    }

    public double getCurrentRunDescentInMeters() {
        return slopeSkiCalculator.getCurrentRunDescentInMeters();
    }

    public double getSkiDescentInMeters() {
        return slopeSkiCalculator.getSkiDescentInMeters();
    }

    public double getCurrentRunAverageSpeed() {
        return slopeSkiCalculator.getCurrentRunAverageSpeed();
    }

    public double getCurrentRunMaximumSpeed() {
        return slopeSkiCalculator.getCurrentRunMaximumSpeed();
    }

    public double getAverageSkiSpeed() {
        return slopeSkiCalculator.getAverageSpeed();
    }

    public double getMaximumSkiSpeed() {
        return slopeSkiCalculator.getMaximumSpeed();
    }

    public double getSkiAngleDegree() {
        return slopeSkiCalculator.getSkiAngleDegree();
    }

    void setWeatherConditions(WeatherConditions weatherConditions) {
        weather = new OngoingWorkoutWeatherConditions(weatherConditions);
    }

    /**
     * @return a {@link WorkoutHeader} representation of this {@link BaseOngoingWorkout} Note
     * that
     * Context can be null.
     */
    @WorkerThread
    public @NonNull WorkoutHeader getWorkoutHeader(
        @Nullable String username,
        int maxHeartRatePerUserSetting,
        @Nullable Integer sharingFlags,
        @Nullable TSS tss,
        @Nullable Long recoveryTime
    ) {
        List<WorkoutGeoPoint> routePoints = getRoutePoints();

        Point startPosition = null;
        Point stopPosition = null;
        Point centerPosition = null;

        if (!routePoints.isEmpty()) {
            startPosition =
                new Point(routePoints.get(0).getLongitude(), routePoints.get(0).getLatitude());
            stopPosition = new Point(routePoints.get(routePoints.size() - 1).getLongitude(),
                routePoints.get(routePoints.size() - 1).getLatitude());
            centerPosition =
                CenterpointCalculator.calculate(routePoints, startPosition, stopPosition);
        }
        double totalDistance = getDistance();
        double avgSpeed = getAverageSpeed();
        double maxSpeed = getMaxSpeed();
        double energyConsumption = getEnergyConsumed();

        Statistics heartRateStats = getHeartRateStatistics();
        double heartRateAvg = heartRateStats.getAvg();
        double heartRateUserSetMax = maxHeartRatePerUserSetting;
        double heartRateAvgPercentage = (heartRateAvg / heartRateUserSetMax) * 100.0;
        double heartRateMax = heartRateStats.getMax();
        double heartRateMaxPercentage = (heartRateMax / heartRateUserSetMax) * 100.0;

        Statistics cadenceStats = getCadenceStatistics();
        int avgCadence = (int) cadenceStats.getAvg();
        int maxCadence = (int) cadenceStats.getMax();

        int pictureCount = getPictures().size();
        int stepCount = getRecordedStepCount();

        int workoutSharingFlags = sharingFlags != null ? sharingFlags : SharingOption.NOT_SHARED.getBackendId();

        return WorkoutHeader.local(totalDistance, maxSpeed, getActivityType(), avgSpeed, null,
            startPosition, stopPosition, centerPosition, getStartTime(), getEndTime(),
            getDurationInSeconds(), energyConsumption, username, heartRateAvg,
            heartRateAvgPercentage, heartRateMax, heartRateMaxPercentage, heartRateUserSetMax,
            avgCadence, maxCadence, pictureCount, 0, 0, workoutSharingFlags, stepCount, false,
            generatePolyline(routePoints), 0, altitudeChangeCalculator.getTotalAscent(),
            altitudeChangeCalculator.getTotalDescent(), recoveryTime != null ? recoveryTime : 0,
            0.0, 0.0, tss, null, 0);
    }

    @Nullable
    private String generatePolyline(List<WorkoutGeoPoint> routePoints) {
        if (routePoints.isEmpty()) {
            return null;
        }
        List<LatLng> points = new ArrayList<>(routePoints.size());
        for (int i = 0; i < routePoints.size(); i++) {
            points.add(routePoints.get(i).getLatLng());
        }
        // TODO: Should we call PolyUtil.simplify(...) before?
        return PolyUtil.encode(points);
    }

    public void incrementAutoRecovered() {
        ++autoRecovered;
    }

    public int getAutoRecovered() {
        return autoRecovered;
    }

    public void resetAutoRecovered() {
        autoRecovered = 0;
    }

    /**
     * @return step rate in steps/minute
     */
    public int getStepRate() {
        return stepRateCalculator.calculateStepRate(SystemClock.elapsedRealtime());
    }
}
