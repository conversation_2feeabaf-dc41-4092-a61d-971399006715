package com.stt.android.workouts.headset

enum class SwimStyle {
    MEDLEY,
    BREASTSTROKE,
    FREESTYLE,
}

data class SwimmingData(
    val deviceMac: String,
    val deviceName: String,
    val id: String,
    val swimStyle: SwimStyle?,
    // unit: /min
    val breathingRate: Int,
    // unit: s
    val breaststrokeDuration: Int,
    val breaststrokePercentage: Int,
    // unit: s
    val breaststrokeGlideTime: Int,
    val breaststrokeMaxBreathAngle: Int,
    val breaststrokeAvgBreathAngle: Int,
    // unit: s
    val freestyleDuration: Int,
    val freestylePercentage: Int,
    val freestyleMaxBreathAngle: Int,
    val freestyleAvgBreathAngle: Int,
    val freestylePitchAngle: Int,
    // unit: s
    val otherDuration: Int,
    val otherPercentage: Int,
    // su07 phaseII
    val breaststrokeGlideAngle: Int,
    val tripsCount: Int?,
    val swimDistanceInMeter: Int?,
    val calorie: Int?,
    val speed: Float?,
    val swimType: SwimmingType = SwimmingType.OPEN_WATER,
    val intervalLaps: List<PoolSwimmingLap> = emptyList()
)

data class PoolSwimmingLap(
    val swimStyle: SwimStyle?,
    // unit: /min
    val breathingRate: Int,
    // unit: s
    val breaststrokeGlideTime: Int,
    val breaststrokeAvgBreathAngle: Int,
    val breaststrokeHeadAngle: Int,
    // unit: s
    val freestyleHeadAngle: Int,
    val freestyleAvgBreathAngle: Int,
    val swimDistanceInMeter: Int,
    // m/s
    val speed: Float,
    val duration: Int,
    val interval: IntervalType,
    // headphone don't have real time, so this timestamp start from 1970
    val startTimeInMillis: Long = 0L,
    val endTimeInMillis: Long = 0L,
    val cumulativeDistanceInMeter: Float = 0F,
)

enum class IntervalType {
    LAP,
    INTERVAL,
}

enum class SwimmingType {
    OPEN_WATER,
    POOL,
}

data class JumpRopeData(
    val deviceMac: String,
    val deviceName: String,
    val id: String,
    val skipsCount: Int,
    val tripCount: Int,
    val skipsPerMin: Int,
    val maxSkipsPerMin: Int,
    val avgSkipsPerRound: Int,
    val maxConsecutiveSkips: Int,
    val rounds: Int,
    val calorie: Int,
    val durationInSecond: Int,
    val jumpRoundsData: List<JumpRoundData> = emptyList()
)

data class JumpRoundData(
    val durationInMilliseconds: Int,
    val skipsPerRound: Int,
    val frequencyPerRound: Int,
    val startTime: Int,
    val endTime: Int?,
)
