package com.stt.android;

import android.annotation.SuppressLint;
import android.content.SharedPreferences;
import androidx.annotation.NonNull;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigInfo;
import com.google.firebase.remoteconfig.FirebaseRemoteConfigValue;
import com.stt.android.controllers.CurrentUserController;
import com.stt.android.di.FeatureTogglePreferences;
import com.stt.android.remote.remoteconfig.AmplitudeEventSampling;
import com.stt.android.remote.remoteconfig.CompanionLinkingParameters;
import com.stt.android.remote.remoteconfig.GraphhopperBaseUrl;
import com.stt.android.remote.remoteconfig.STFusedLocationParameters;
import com.stt.android.remoteconfig.AskoRemoteConfig;
import com.stt.android.utils.BrandFlavourConstants;
import com.stt.android.utils.RxUtils;
import com.stt.android.utils.STTConstants;
import dagger.Lazy;
import io.reactivex.Completable;
import io.reactivex.schedulers.Schedulers;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;
import javax.inject.Singleton;
import timber.log.Timber;

/**
 * Class which can be used to enable/disable features based on a remote configuration file.
 * <p>
 * We're currently using Firebase Remote Config (https://firebase.google.com/docs/remote-config/).
 * <p>
 * When you add a new flag you must:
 * <ul>
 * <li>Decide on a unique key string.</li>
 * <li>Add a new entry to xml file {@link R.xml#default_remote_config} for all flavours
 * (debug, release, ...)</li>
 * <li>Create a constant in this class which holds the key string</li>
 * <li>Add a new parameter with the key and value at the Firebase website
 * <a href="https://console.firebase.google.com/project/api-project-1014081224822/config">Firebase
 * Remote Config</a></li>
 * <li>Provide a method that exposes that flag value.
 * If you have multiple flags which are related to each other you might want to create an inner
 * class</li>
 * </ul>
 */
@Singleton
public class FeatureFlags implements com.stt.android.data.featureflags.FeatureFlags {
    private static final String GRAPH_HOPPER_KEY = "graph_hopper_key";

    private final Lazy<FirebaseRemoteConfig> firebaseRemoteConfig;
    private final AskoRemoteConfig askoRemoteConfig;
    private final Lazy<CurrentUserController> currentUserController;

    @Inject
    public FeatureFlags(
        Lazy<FirebaseRemoteConfig> firebaseRemoteConfig,
        AskoRemoteConfig askoRemoteConfig,
        @FeatureTogglePreferences SharedPreferences featureTogglePreferences,
        Lazy<CurrentUserController> currentUserController
    ) {
        this.firebaseRemoteConfig = firebaseRemoteConfig;
        this.askoRemoteConfig = askoRemoteConfig;
        this.currentUserController = currentUserController;
    }

    /**
     * Convenient method to print the source of a value for debug purposes
     */
    private static void debugPrintValueSource(
        FirebaseRemoteConfig firebaseRemoteConfig,
        String key
    ) {
        if (STTConstants.DEBUG) {
            if (firebaseRemoteConfig == null) {
                Timber.d("Remote config is null, key %s", key);
                return;
            }
            FirebaseRemoteConfigValue value = firebaseRemoteConfig.getValue(key);
            Timber.v(
                "Remote config %s value (%s) source is (Remote: %d, Default: %d, Static: %d): %s",
                key, value.asString(), FirebaseRemoteConfig.VALUE_SOURCE_REMOTE,
                FirebaseRemoteConfig.VALUE_SOURCE_DEFAULT, FirebaseRemoteConfig.VALUE_SOURCE_STATIC,
                value.getSource());
        }
    }

    /**
     * Refresh the remote config used to set the values for the different feature flags.
     */
    @SuppressWarnings("ResultOfMethodCallIgnored")
    @SuppressLint("CheckResult")
    public void refreshFeatureFlags() {
        askoRemoteConfig.fetchConfig()
            .andThen(fetchFirebaseRemoteConfig())
            .subscribeOn(Schedulers.io())
            .subscribe(RxUtils.actionNone(),
                e -> Timber.w(e, "Error during refresh feature flags"));
    }

    private Completable fetchFirebaseRemoteConfig() {
        // Getting an instance of FirebaseRemoteConfig means accessing disk, therefore we do it
        // in a background thread.
        return Completable.create(source -> {
            FirebaseRemoteConfigInfo configInfo = firebaseRemoteConfig.get().getInfo();

            Timber.v(
                "Firebase remote config info before refresh:\n\tstatus: %s\n\tWhen:%s",
                configInfo.getLastFetchStatus(), configInfo.getFetchTimeMillis());

            long cacheExpiration = TimeUnit.HOURS.toSeconds(6);
            if (configInfo.getConfigSettings().getMinimumFetchIntervalInSeconds() == 0) {
                cacheExpiration = 0;
            }
            Timber.d(
                "Scheduling a refresh for the firebase remote config with cache expiration %ds",
                cacheExpiration);

            firebaseRemoteConfig.get().fetch(cacheExpiration)
                .addOnCompleteListener(task -> {
                    if (task.isSuccessful()) {
                        FirebaseRemoteConfigInfo configInfo1 = firebaseRemoteConfig.get().getInfo();
                        Timber.v(
                            "Firebase Remote config fetch Succeeded\n\tstatus: %s\n\tWhen:%s",
                            configInfo1.getLastFetchStatus(),
                            configInfo1.getFetchTimeMillis());

                        // Once the config is successfully fetched it must be activated
                        // before newly fetched values are returned.
                        firebaseRemoteConfig.get().activate()
                            .addOnCompleteListener(activateTask -> source.onComplete())
                            .addOnFailureListener(e ->
                                source.onError(
                                    new RuntimeException("FirebaseRemote config activation failed",
                                        e))
                            );
                    } else {
                        Timber.d("Firebase Remote config fetch failed");
                    }
                })
                .addOnFailureListener((e) ->
                    source.onError(new RuntimeException("FirebaseRemote config fetch failed", e)));
        });
    }

    public String getGraphHopperKey() {
        debugPrintValueSource(firebaseRemoteConfig.get(), GRAPH_HOPPER_KEY);
        return firebaseRemoteConfig.get().getString(GRAPH_HOPPER_KEY);
    }

    public boolean isEmarsysEnabled() {
        return askoRemoteConfig.getEmarsysEnabled();
    }

    public boolean isPartnerConnectionsEnabled() {
        return askoRemoteConfig.getPartnerConnectionsEnabled();
    }

    @Override
    public boolean isPushSMLZipEnabled() {
        return askoRemoteConfig.getSmlToBackend();
    }

    public boolean isDeleteAccountEnabled() {
        return askoRemoteConfig.getDeleteAccount();
    }

    public STFusedLocationParameters getSTFusedLocationParameters() {
        return askoRemoteConfig.getStFusedLocationParameters();
    }

    public Boolean companionLinking() {
        return askoRemoteConfig.getCompanionLinking();
    }

    public CompanionLinkingParameters companionLinkingParameters() {
        return askoRemoteConfig.getCompanionLinkingParameters();
    }

    public List<AmplitudeEventSampling> getAnalyticsEventSamplingConfig() {
        return askoRemoteConfig.getAmplitudeEventSamplingConfig();
    }

    @NonNull
    public GraphhopperBaseUrl getGraphhopperBaseUrl() {
        return askoRemoteConfig.getGraphhopperBaseUrl();
    }

    public Boolean isCustomerSupportChatBotEnabled() {
        return askoRemoteConfig.isCustomerSupportChatBotEnabled();
    }

    public boolean isAiPlannerEnabled() {
        // ST does not support ai planner
        if (!BrandFlavourConstants.PROVIDE_AI_PLANNER_FEATURE) return false;

        CurrentUserController userController = currentUserController.get();
        boolean isFieldTester = userController != null && userController.isFieldTester();
        boolean isAiPlannerFlagEnabled = askoRemoteConfig.isAiPlannerEnabled();
        return isAiPlannerFlagEnabled || isFieldTester;
    }
}
