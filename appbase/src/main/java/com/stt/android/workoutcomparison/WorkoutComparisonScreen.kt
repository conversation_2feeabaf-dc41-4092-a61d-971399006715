package com.stt.android.workoutcomparison

import android.content.Context
import androidx.annotation.ColorRes
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.res.ResourcesCompat
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.header
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.domain.workouts.ActivityGroup
import com.stt.android.home.diary.diarycalendar.activitygroups.colorRes
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.utils.HorizontalDragTouchListener
import com.suunto.algorithms.data.Length
import kotlinx.coroutines.delay
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.roundToLong
import kotlin.time.Duration
import com.amersports.formatter.Unit as FormatterUnit

@Composable
fun WorkoutComparisonScreen(
    onBackClicked: () -> Unit,
    infoModelFormatter: InfoModelFormatter,
    unitConverter: JScienceUnitConverter,
    viewModel: WorkoutComparisonViewModel,
    modifier: Modifier = Modifier,
) {
    when (val uiState = viewModel.uiState.collectAsState().value) {
        is WorkoutComparisonUiState.Loading -> WorkoutComparisonLoadingScreen(modifier)
        is WorkoutComparisonUiState.Loaded -> WorkoutComparisonLoadedScreen(
            uiState = uiState,
            onBackClicked = onBackClicked,
            infoModelFormatter = infoModelFormatter,
            unitConverter = unitConverter,
            modifier = modifier,
        )

        is WorkoutComparisonUiState.Error -> WorkoutComparisonErrorScreen(
            onRetryClicked = viewModel::load,
            modifier = modifier,
        )
    }
}

@Composable
private fun WorkoutComparisonLoadingScreen(
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.fillMaxSize()) {
        CircularProgressIndicator(
            modifier = Modifier
                .align(Alignment.Center)
                .size(48.dp),
            color = MaterialTheme.colorScheme.primary,
        )
    }
}

@Composable
private fun WorkoutComparisonLoadedScreen(
    uiState: WorkoutComparisonUiState.Loaded,
    onBackClicked: () -> Unit,
    infoModelFormatter: InfoModelFormatter,
    unitConverter: JScienceUnitConverter,
    modifier: Modifier = Modifier,
) {
    WorkoutComparsionView(
        uiState = uiState,
        modifier = modifier,
        infoModelFormatter = infoModelFormatter,
        unitConverter = unitConverter,
        currentTitle = stringResource(R.string.compare_current_workout),
        otherTitle = stringResource(R.string.compare_other_workout),
        showPower = true,
        topBar = {
            WorkoutComparisonToolbar(
                activityIcon = uiState.activityIcon,
                activityIconBackgroundColor = uiState.activityGroup.colorRes,
                activityTime = uiState.activityTime,
                onBackClicked = onBackClicked,
            )
        }
    )
}

@Composable
fun WorkoutComparsionView(
    uiState: WorkoutComparisonUiState.Loaded,
    infoModelFormatter: InfoModelFormatter,
    unitConverter: JScienceUnitConverter,
    currentTitle: String,
    otherTitle: String,
    showPower: Boolean,
    modifier: Modifier = Modifier,
    topBar: @Composable () -> Unit = {},
    topView: @Composable () -> Unit = {},
    bottomView: @Composable () -> Unit = {},
) {
    val context = LocalContext.current

    Scaffold(
        topBar = topBar,
        modifier = modifier,
    ) { paddingValues ->
        val scrollState = rememberScrollState()
        var isDraggingHorizontally by remember { mutableStateOf(false) }
        val horizontalDragTouchListener = remember {
            HorizontalDragTouchListener(context) {
                isDraggingHorizontally = it
            }
        }
        Column(
            modifier = Modifier
                .background(MaterialTheme.colorScheme.background)
                .narrowContent()
                .padding(paddingValues)
                .verticalScroll(scrollState, enabled = !isDraggingHorizontally)
        ) {
            topView()

            if (uiState.comparisonEntries.isNotEmpty() && uiState.comparisonLines.isNotEmpty()) {
                var isPlaying by remember { mutableStateOf(false) }
                var selectedComparisonIndex by remember { mutableIntStateOf(0) }
                var selectedComparison by remember { mutableStateOf(uiState.comparisonEntries.first()) }

                LaunchedEffect(isPlaying) {
                    if (!isPlaying) {
                        return@LaunchedEffect
                    }

                    val playbackDelayInMills =
                        (2900.0 * (uiState.comparisonEntries.last().duration.inWholeMilliseconds / 60_000.0).pow(
                            0.66
                        ) / uiState.comparisonEntries.lastIndex).roundToLong()

                    if (selectedComparisonIndex == uiState.comparisonEntries.lastIndex) {
                        selectedComparisonIndex = 0
                    }
                    while (isPlaying) {
                        if (selectedComparisonIndex == uiState.comparisonEntries.lastIndex) {
                            isPlaying = false
                            return@LaunchedEffect
                        }

                        selectedComparisonIndex++
                        selectedComparison = uiState.comparisonEntries[selectedComparisonIndex]

                        delay(playbackDelayInMills)
                    }
                }

                uiState.workoutComparisonMap?.let {
                    WorkoutComparisonMap(
                        mapType = it.mapType,
                        currentWorkoutRoute = it.currentWorkoutRoute,
                        otherWorkoutRoute = it.otherWorkoutRoute,
                        selectedComparison = selectedComparison,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(300.dp),
                    )
                }

                WorkoutComparisonHeader(
                    infoModelFormatter = infoModelFormatter,
                    isPlaying = isPlaying,
                    onPlayToggled = { isPlaying = !isPlaying },
                    distance = selectedComparison.distance,
                    duration = selectedComparison.duration,
                    difference = selectedComparison.difference,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surface)
                        .padding(MaterialTheme.spacing.medium),
                )

                WorkoutComparisonChart(
                    distanceUnit = uiState.distanceUnit,
                    comparisonLines = uiState.comparisonLines,
                    comparisonEntries = uiState.comparisonEntries,
                    selectedComparisonEntry = selectedComparison,
                    horizontalDragTouchListener = horizontalDragTouchListener,
                    onComparisonEntrySelected = { index, entry ->
                        selectedComparisonIndex = index
                        selectedComparison = entry
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surface)
                        .height(160.dp)
                        .padding(MaterialTheme.spacing.medium)
                )

                HorizontalDivider()

                WorkoutComparisonTitle(
                    currentTitle = currentTitle,
                    otherTitle = otherTitle,
                    currentWorkoutDate = uiState.activityDate,
                    otherWorkoutDate = uiState.otherActivityDate,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surface)
                        .padding(MaterialTheme.spacing.medium),
                )

                HorizontalDivider()

                WorkoutHeartRateComparison(
                    heartRate = selectedComparison.heartRate,
                    infoModelFormatter = infoModelFormatter,
                    unitConverter = unitConverter,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surface)
                        .padding(MaterialTheme.spacing.medium),
                )

                HorizontalDivider()

                WorkoutSpeedComparison(
                    activityGroup = uiState.activityGroup,
                    infoModelFormatter = infoModelFormatter,
                    speed = selectedComparison.speed,
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(MaterialTheme.colorScheme.surface)
                        .padding(MaterialTheme.spacing.medium),
                )

                if (showPower) {
                    HorizontalDivider()

                    WorkoutPowerComparison(
                        infoModelFormatter = infoModelFormatter,
                        power = selectedComparison.power,
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(MaterialTheme.colorScheme.surface)
                            .padding(MaterialTheme.spacing.medium),
                    )
                }
            }

            bottomView()
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun WorkoutComparisonToolbar(
    @DrawableRes activityIcon: Int,
    @ColorRes activityIconBackgroundColor: Int,
    activityTime: String,
    onBackClicked: () -> Unit,
) {
    TopAppBar(
        title = {
            Column {
                Text(
                    text = stringResource(id = R.string.compare),
                    style = MaterialTheme.typography.header,
                    fontSize = 18.sp,
                )

                Text(
                    text = activityTime,
                    style = MaterialTheme.typography.body,
                    fontSize = 14.sp,
                )
            }
        },
        navigationIcon = {
            SuuntoIconButton(
                icon = SuuntoIcons.ActionBack,
                onClick = onBackClicked,
                contentDescription = stringResource(R.string.back),
            )
        },
        actions = {
            SuuntoActivityIcon(
                iconRes = activityIcon,
                tint = MaterialTheme.colorScheme.onPrimary,
                background = colorResource(activityIconBackgroundColor),
                modifier = Modifier
                    .padding(end = MaterialTheme.spacing.small),
            )
        },
    )
}

@Composable
private fun WorkoutComparisonHeader(
    infoModelFormatter: InfoModelFormatter,
    isPlaying: Boolean,
    onPlayToggled: () -> Unit,
    distance: Length,
    duration: Duration,
    difference: Duration,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        IconButton(
            onClick = onPlayToggled,
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(Color.Black),
        ) {
            Icon(
                painter = painterResource(if (isPlaying) R.drawable.ic_pause_fill else R.drawable.ic_play_fill),
                contentDescription = null,
                tint = Color.White,
            )
        }

        Column(
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.medium)
                .weight(1.0f),
        ) {
            Text(
                text = stringResource(R.string.distance),
                style = MaterialTheme.typography.header,
                fontSize = 16.sp,
            )

            Row(
                modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                verticalAlignment = Alignment.Bottom,
            ) {
                val (distanceText, distanceUnit) = infoModelFormatter
                    .formatValue(SummaryItem.DISTANCE, distance.inMeters)
                    .let { result ->
                        result.value.orEmpty() to (result.unit ?: infoModelFormatter.unit.speedUnit)
                    }

                Text(
                    text = distanceText,
                    style = MaterialTheme.typography.body,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                )

                Text(
                    text = stringResource(distanceUnit),
                    modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                    style = MaterialTheme.typography.body,
                    fontSize = 18.sp,
                )
            }
        }

        Column(
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.medium)
                .weight(1.0f),
        ) {
            Text(
                text = stringResource(R.string.duration),
                style = MaterialTheme.typography.header,
                fontSize = 16.sp,
            )

            Text(
                text = infoModelFormatter
                    .formatValue(SummaryItem.DURATION, duration.inWholeSeconds)
                    .value
                    .orEmpty(),
                modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                style = MaterialTheme.typography.body,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
            )
        }

        Column(
            modifier = Modifier
                .padding(start = MaterialTheme.spacing.medium)
                .weight(1.0f),
        ) {
            Text(
                text = stringResource(R.string.compare_difference),
                style = MaterialTheme.typography.header,
                fontSize = 16.sp,
            )

            Text(
                text = infoModelFormatter
                    .formatValue(SummaryItem.DURATION, abs(difference.inWholeSeconds))
                    .value
                    ?.let { value ->
                        if (!difference.isPositive()) {
                            "-$value"
                        } else {
                            "+$value"
                        }
                    }
                    .orEmpty(),
                modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                color = colorResource(if (difference.isPositive()) BEHIND_COLOR else AHEAD_COLOR),
                style = MaterialTheme.typography.body,
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
            )
        }
    }
}

@Composable
fun WorkoutComparisonTitle(
    currentTitle: String,
    otherTitle: String,
    currentWorkoutDate: String,
    otherWorkoutDate: String,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
    ) {
        Spacer(Modifier.weight(1f))

        WorkoutComparisonTitle(
            title = currentTitle,
            workoutDate = currentWorkoutDate,
            modifier = Modifier.weight(1.2f),
        )

        WorkoutComparisonTitle(
            title = otherTitle,
            workoutDate = otherWorkoutDate,
            modifier = Modifier.weight(1.2f),
        )
    }
}

@Composable
private fun WorkoutComparisonTitle(
    title: String,
    workoutDate: String,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Text(
            text = title,
            style = MaterialTheme.typography.header,
            fontSize = 18.sp,
            textAlign = TextAlign.End,
            modifier = Modifier.fillMaxWidth(),
        )

        Text(
            text = workoutDate,
            style = MaterialTheme.typography.body,
            fontSize = 14.sp,
            textAlign = TextAlign.End,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Composable
private fun WorkoutHeartRateComparison(
    heartRate: WorkoutComparisonEntry.Comparison,
    infoModelFormatter: InfoModelFormatter,
    unitConverter: JScienceUnitConverter,
    modifier: Modifier = Modifier,
) {
    val (currentHeartRateColor, otherHeartRateColor) =
        if (heartRate.currentWorkoutValue <= heartRate.otherWorkoutValue) {
            AHEAD_COLOR to BEHIND_COLOR
        } else {
            BEHIND_COLOR to AHEAD_COLOR
        }
    WorkoutComparisonData(
        title = R.string.heart_rate,
        currentWorkoutValueColor = currentHeartRateColor,
        currentWorkoutValue = infoModelFormatter.formatHeartRate(
            unitConverter,
            heartRate.currentWorkoutValue
        ),
        currentWorkoutUnit = stringResource(com.stt.android.core.R.string.bpm),
        currentWorkoutAverageValue = infoModelFormatter
            .formatHeartRate(unitConverter, heartRate.currentWorkoutAverageValue),
        otherWorkoutValueColor = otherHeartRateColor,
        otherWorkoutValue = infoModelFormatter.formatHeartRate(
            unitConverter,
            heartRate.otherWorkoutValue
        ),
        otherWorkoutUnit = stringResource(com.stt.android.core.R.string.bpm),
        otherWorkoutAverageValue = infoModelFormatter
            .formatHeartRate(unitConverter, heartRate.otherWorkoutAverageValue),
        modifier = modifier,
    )
}

private fun InfoModelFormatter.formatHeartRate(
    unitConverter: JScienceUnitConverter,
    bmp: Double,
): String {
    if (bmp <= 0.0) {
        return "--"
    }
    val hz = unitConverter.convert(bmp, FormatterUnit.RPM, FormatterUnit.HZ)
    return formatValue(SummaryItem.AVGHEARTRATE, hz)
        .value
        ?: "--"
}

@Composable
private fun WorkoutSpeedComparison(
    activityGroup: ActivityGroup,
    infoModelFormatter: InfoModelFormatter,
    speed: WorkoutComparisonEntry.Comparison,
    modifier: Modifier = Modifier,
) {
    val (currentSpeedColor, otherSpeedColor) =
        if (speed.currentWorkoutValue >= speed.otherWorkoutValue) {
            AHEAD_COLOR to BEHIND_COLOR
        } else {
            BEHIND_COLOR to AHEAD_COLOR
        }
    val isRunning = activityGroup == ActivityGroup.Running
    val context = LocalContext.current
    val (currentSpeed, currentSpeedUnit) = infoModelFormatter
        .formatSpeedOrPace(context, activityGroup, speed.currentWorkoutValue)
    val (otherSpeed, otherSpeedUnit) = infoModelFormatter
        .formatSpeedOrPace(context, activityGroup, speed.otherWorkoutValue)
    WorkoutComparisonData(
        title = if (isRunning) R.string.pace else com.stt.android.core.R.string.speed,
        currentWorkoutValueColor = currentSpeedColor,
        currentWorkoutValue = currentSpeed,
        currentWorkoutUnit = currentSpeedUnit,
        currentWorkoutAverageValue = infoModelFormatter
            .formatSpeedOrPace(context, activityGroup, speed.currentWorkoutAverageValue)
            .first,
        otherWorkoutValueColor = otherSpeedColor,
        otherWorkoutValue = otherSpeed,
        otherWorkoutUnit = otherSpeedUnit,
        otherWorkoutAverageValue = infoModelFormatter
            .formatSpeedOrPace(context, activityGroup, speed.otherWorkoutAverageValue)
            .first,
        modifier = modifier,
    )
}

private fun InfoModelFormatter.formatSpeedOrPace(
    context: Context,
    activityGroup: ActivityGroup,
    speed: Double,
): Pair<String, String> = if (activityGroup == ActivityGroup.Running) {
    formatPace(context, speed)
} else {
    formatSpeed(context, speed)
}

private fun InfoModelFormatter.formatSpeed(context: Context, speed: Double): Pair<String, String> =
    formatValue(SummaryItem.MAXSPEED, speed)
        .let { result ->
            val unit = result.unitString
                ?: result.unit?.takeIf { it != ResourcesCompat.ID_NULL }?.let(context::getString)
                ?: ""
            result.value.orEmpty() to unit
        }

private fun InfoModelFormatter.formatPace(context: Context, speed: Double): Pair<String, String> =
    if (speed <= 0.0) {
        "--" to ""
    } else {
        formatValue(SummaryItem.MAXPACE, speed)
            .let { result ->
                val unit = result.unitString
                    ?: result.unit?.takeIf { it != ResourcesCompat.ID_NULL }
                        ?.let(context::getString)
                    ?: ""
                result.value.orEmpty() to unit
            }
    }

@Composable
private fun WorkoutPowerComparison(
    infoModelFormatter: InfoModelFormatter,
    power: WorkoutComparisonEntry.Comparison,
    modifier: Modifier = Modifier,
) {
    val (currentSpeedColor, otherSpeedColor) =
        if (power.currentWorkoutValue >= power.otherWorkoutValue) {
            AHEAD_COLOR to BEHIND_COLOR
        } else {
            BEHIND_COLOR to AHEAD_COLOR
        }
    val context = LocalContext.current
    val (currentSpeed, currentSpeedUnit) = infoModelFormatter.formatPower(
        context,
        power.currentWorkoutValue
    )
    val (otherSpeed, otherSpeedUnit) = infoModelFormatter.formatPower(
        context,
        power.otherWorkoutValue
    )
    WorkoutComparisonData(
        title = com.stt.android.core.R.string.all_power,
        currentWorkoutValueColor = currentSpeedColor,
        currentWorkoutValue = currentSpeed,
        currentWorkoutUnit = currentSpeedUnit,
        currentWorkoutAverageValue = infoModelFormatter
            .formatPower(context, power.currentWorkoutAverageValue)
            .first,
        otherWorkoutValueColor = otherSpeedColor,
        otherWorkoutValue = otherSpeed,
        otherWorkoutUnit = otherSpeedUnit,
        otherWorkoutAverageValue = infoModelFormatter
            .formatPower(context, power.otherWorkoutAverageValue)
            .first,
        modifier = modifier,
    )
}

private fun InfoModelFormatter.formatPower(context: Context, power: Double): Pair<String, String> =
    if (power.isNaN()) {
        "--" to ""
    } else {
        formatValue(SummaryItem.AVGPOWER, power)
            .let { result ->
                val unit = result.unitString
                    ?: result.unit?.takeIf { it != ResourcesCompat.ID_NULL }
                        ?.let(context::getString)
                    ?: ""
                result.value.orEmpty() to unit
            }
    }

@Composable
private fun WorkoutComparisonData(
    @StringRes title: Int,
    @ColorRes currentWorkoutValueColor: Int,
    currentWorkoutValue: String,
    currentWorkoutUnit: String,
    currentWorkoutAverageValue: String,
    @ColorRes otherWorkoutValueColor: Int,
    otherWorkoutValue: String,
    otherWorkoutUnit: String,
    otherWorkoutAverageValue: String,
    modifier: Modifier = Modifier,
) {
    Row(modifier = modifier) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = stringResource(title),
                style = MaterialTheme.typography.header,
                fontSize = 18.sp,
            )

            Text(
                text = stringResource(com.stt.android.core.R.string.average),
                style = MaterialTheme.typography.body,
                fontSize = 14.sp,
            )
        }

        WorkoutComparisonData(
            valueColor = currentWorkoutValueColor,
            value = currentWorkoutValue,
            valueUnit = currentWorkoutUnit,
            averageValue = currentWorkoutAverageValue,
            modifier = Modifier.weight(1.2f),
        )

        WorkoutComparisonData(
            valueColor = otherWorkoutValueColor,
            value = otherWorkoutValue,
            valueUnit = otherWorkoutUnit,
            averageValue = otherWorkoutAverageValue,
            modifier = Modifier.weight(1.2f),
        )
    }
}

@Composable
private fun WorkoutComparisonData(
    @ColorRes valueColor: Int,
    value: String,
    valueUnit: String,
    averageValue: String,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.Bottom,
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.body,
                color = colorResource(valueColor),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
            )

            Text(
                text = valueUnit,
                modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall),
                style = MaterialTheme.typography.body,
                color = colorResource(valueColor),
                fontSize = 18.sp,
            )
        }

        Text(
            text = averageValue,
            style = MaterialTheme.typography.body,
            fontSize = 14.sp,
            textAlign = TextAlign.End,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Composable
private fun WorkoutComparisonErrorScreen(
    onRetryClicked: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = stringResource(R.string.error_0),
            style = MaterialTheme.typography.body,
            fontSize = 18.sp,
        )

        Spacer(Modifier.height(MaterialTheme.spacing.small))

        PrimaryButton(
            text = stringResource(R.string.retry),
            onClick = onRetryClicked,
        )
    }
}

@ColorRes
private val AHEAD_COLOR: Int = R.color.ghost_target_ahead

@ColorRes
private val BEHIND_COLOR: Int = R.color.ghost_target_behind_or_no_match

@Preview
@Composable
private fun WorkoutComparisonLoadingScreenPreview() {
    WorkoutComparisonLoadingScreen()
}

@Preview
@Composable
private fun WorkoutComparisonErrorScreenPreview() {
    WorkoutComparisonErrorScreen({})
}
