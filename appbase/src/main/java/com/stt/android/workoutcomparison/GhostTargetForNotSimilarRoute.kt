package com.stt.android.workoutcomparison

import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.workout.WorkoutData
import com.stt.android.domain.workout.WorkoutGeoPoint
import com.stt.android.exceptions.GhostMatchNotFoundException
import timber.log.Timber

class GhostTargetForNotSimilarRoute(
    private val otherWorkoutData: WorkoutData,
    private val currentWorkoutSml: Sml,
    private val otherWorkoutSml: Sml
) : GhostTarget {

    // Stores precomputed distances for quick lookup
    private val otherWorkoutDistances: List<Double> = computeDistances(otherWorkoutData.routePoints)

    private var matchedGeoPoint: WorkoutGeoPoint? = null

    override fun getLastMatchedPosition(): WorkoutGeoPoint? {
        return matchedGeoPoint
    }

    private fun Sml.findMatchedCumulativeDistance(workoutGeoPoint: WorkoutGeoPoint): Double? {
        val timestamp = workoutGeoPoint.timestamp
        return streamData.speed
            .firstOrNull { point ->
                point.timestamp >= timestamp
            }
            ?.cumulativeDistance
            ?.toDouble()
    }

    /**
     * Computes distances from the start for all points in the workout.
     */
    private fun computeDistances(points: List<WorkoutGeoPoint>): List<Double> {
        if (points.isEmpty()) return emptyList()

        return points.mapNotNull { point ->
            otherWorkoutSml.findMatchedCumulativeDistance(point)
        }
    }

    override fun processNewLocation(currentWorkoutGeoPoint: WorkoutGeoPoint) {
        val currentDistanceFromStart = currentWorkoutSml.findMatchedCumulativeDistance(currentWorkoutGeoPoint)
            ?: return

        // Find the closest matching point in the other workout
        val closestIndex = otherWorkoutDistances.indexOfFirst { distance ->
            distance >= currentDistanceFromStart
        }

        // Return the matched point or null if no match is found
        matchedGeoPoint = if (closestIndex != -1) otherWorkoutData.routePoints[closestIndex] else null
    }

    override fun calculateCurrentMatchTimeDifferenceInMilliseconds(referenceTimeInMilliseconds: Long): Long {
        if (matchedGeoPoint == null) {
            throw GhostMatchNotFoundException("There's no match available")
        }
        val ghostTime = matchedGeoPoint!!.millisecondsInWorkout
        return referenceTimeInMilliseconds - ghostTime
    }

    override fun findByDuration(millisecondsInWorkout: Int): WorkoutGeoPoint {
        return WorkoutGeoPoint(LatLng(0.0, 0.0), 0.0, false, 0f, 0.0, 0.0, 0.0, 0f, 0)
    }
}
