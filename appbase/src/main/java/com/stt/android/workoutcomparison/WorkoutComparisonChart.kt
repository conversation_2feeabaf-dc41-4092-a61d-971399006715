package com.stt.android.workoutcomparison

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Typeface
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.LimitLine
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.components.YAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.github.mikephil.charting.renderer.YAxisRenderer
import com.github.mikephil.charting.utils.Transformer
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler
import com.stt.android.R
import com.stt.android.ThemeColors
import com.stt.android.ui.utils.HorizontalDragTouchListener
import com.stt.android.utils.FontUtils
import java.util.Locale
import kotlin.math.roundToInt

@SuppressLint("ClickableViewAccessibility")
@Composable
internal fun WorkoutComparisonChart(
    distanceUnit: String,
    comparisonLines: List<WorkoutComparisonLine>,
    comparisonEntries: List<WorkoutComparisonEntry>,
    selectedComparisonEntry: WorkoutComparisonEntry,
    horizontalDragTouchListener: HorizontalDragTouchListener,
    onComparisonEntrySelected: (Int, WorkoutComparisonEntry) -> Unit,
    modifier: Modifier = Modifier,
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            WorkoutComparisonChart(context).apply {
                layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT,
                )

                initialize(
                    distanceUnit = distanceUnit,
                    comparisonLines = comparisonLines,
                    comparisonEntries = comparisonEntries,
                    onComparisonEntrySelected = onComparisonEntrySelected,
                )
                highlight(selectedComparisonEntry)
            }
        },
        update = { comparisonChart ->
            comparisonChart.highlight(selectedComparisonEntry)
            comparisonChart.setOnTouchListener(horizontalDragTouchListener)
        }
    )
}

private class WorkoutComparisonChart(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = 0,
) : LineChart(context, attrs, defStyle) {
    @ColorInt
    private val labelColor: Int = ThemeColors.primaryTextColor(context)
    private val chartTypeface: Typeface = FontUtils.getChartLabelsTypeface(context)

    private var lastHighlightedX = -1.0F
    private var lastHighlightedDataSetIndex = -1

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean = when (event.action) {
        MotionEvent.ACTION_DOWN -> {
            startNestedScroll(ViewCompat.SCROLL_AXIS_HORIZONTAL)
            true
        }

        else -> super.onTouchEvent(event)
    }

    fun initialize(
        distanceUnit: String,
        comparisonLines: List<WorkoutComparisonLine>,
        comparisonEntries: List<WorkoutComparisonEntry>,
        onComparisonEntrySelected: (Int, WorkoutComparisonEntry) -> Unit,
    ) {
        description.isEnabled = false
        legend.isEnabled = false
        setScaleEnabled(false)

        initializeXAsis(distanceUnit)
        initializeLeftYAxis(comparisonLines)
        axisRight.isEnabled = false

        setOnChartValueSelectedListener(object : OnChartValueSelectedListener {
            override fun onValueSelected(e: Entry, h: Highlight) {
                val x = e.x.roundToInt()
                comparisonEntries.forEachIndexed { index, entry ->
                    val distanceInMeters = entry.distance.inMeters
                    if (distanceInMeters >= x) {
                        lastHighlightedX = distanceInMeters.toFloat()
                        onComparisonEntrySelected(index, entry)
                        return
                    }
                }
            }

            override fun onNothingSelected() {
                if (lastHighlightedX >= 0.0F && lastHighlightedDataSetIndex >= 0) {
                    highlightValue(lastHighlightedX, lastHighlightedDataSetIndex, false)
                }
            }
        })
    }

    private fun initializeXAsis(distanceUnit: String) = with(xAxis) {
        position = XAxis.XAxisPosition.BOTTOM

        setDrawAxisLine(false)
        setDrawGridLines(false)

        setDrawLabels(true)
        textColor = labelColor
        textSize = 12.0F
        typeface = chartTypeface

        valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String = when {
                value < 0.0F -> ""
                value == 0.0F -> distanceUnit
                else -> String.format(Locale.US, "%.2f", value / 1000.0)
            }
        }
    }

    private fun initializeLeftYAxis(comparisonLines: List<WorkoutComparisonLine>) = with(axisLeft) {
        isEnabled = true

        setDrawAxisLine(true)
        setDrawGridLines(true)
        gridLineWidth = 0.5F
        setDrawZeroLine(false)

        addLimitLine(
            LimitLine(0.0F, "").apply {
                lineColor = gridColor
                enableDashedLine(
                    Utils.convertDpToPixel(1.0F),
                    Utils.convertDpToPixel(3.0F),
                    0.0F
                )
            }
        )

        setDrawTopYLabelEntry(true)
        setDrawLabels(true)
        textColor = labelColor
        textSize = 12.0F
        typeface = chartTypeface

        data = comparisonLines.map { comparisonLine ->
            LineDataSet(comparisonLine.entries, "").apply {
                color =
                    ResourcesCompat.getColor(context.resources, comparisonLine.color, context.theme)
                setDrawValues(false)
                setDrawCircles(false)
                mode = LineDataSet.Mode.LINEAR

                setDrawHighlightIndicators(true)
                setDrawHorizontalHighlightIndicator(false)
                highLightColor = Color.BLACK
                highlightLineWidth = 1.0F
                lineWidth = 2.0F
                axisDependency = YAxis.AxisDependency.LEFT
            }
        }.let(::LineData)

        calculate(lineData.yMin, lineData.yMax)
        axisMinimum = if (axisMinimum > 0.0F) {
            axisMinimum - (axisMinimum % 10000.0F)
        } else {
            axisMinimum - (10000.0F + axisMinimum % 10000.0F)
        }
        axisMaximum = if (axisMaximum > 0.0F) {
            axisMaximum + (10000.0F - axisMaximum % 10000.0F)
        } else {
            axisMaximum - (10000.0F + axisMaximum % 10000.0F)
        }
        setLabelCount(calculateLabelCount(axisMinimum, axisMaximum), true)

        valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                val rounded = (value / 1000.0F).roundToInt()
                return when {
                    rounded > 0 -> "-$rounded"
                    rounded < 0 -> "+${-rounded}"
                    else -> "0"
                }
            }
        }

        rendererLeftYAxis = DurationAxisRenderer(
            aheadColor = ResourcesCompat.getColor(context.resources, AHEAD_COLOR, context.theme),
            behindColor = ResourcesCompat.getColor(context.resources, BEHIND_COLOR, context.theme),
            viewPortHandler = viewPortHandler,
            yAxis = this,
            transformer = mLeftAxisTransformer,
        )
    }

    fun highlight(selectedComparisonEntry: WorkoutComparisonEntry) {
        val distanceInMeters = selectedComparisonEntry.distance.inMeters.toFloat()
        if (lastHighlightedX == distanceInMeters) {
            return
        }
        data.dataSets
            .forEachIndexed { index, dataSet ->
                if (dataSet.xMax >= distanceInMeters) {
                    lastHighlightedX = distanceInMeters
                    lastHighlightedDataSetIndex = index
                    highlightValue(distanceInMeters, index, false)
                    return
                }
            }
    }

    private companion object {
        @ColorRes
        val AHEAD_COLOR: Int = R.color.ghost_target_ahead

        @ColorRes
        val BEHIND_COLOR: Int = R.color.ghost_target_behind_or_no_match

        fun calculateLabelCount(min: Float, max: Float): Int {
            val diff = (max - min).toInt()
            if (diff < 20.0) {
                return 3
            }
            if (diff % 30 == 0) {
                return 4
            }
            if (diff % 40 == 0) {
                return 5
            }
            if (diff % 20 == 0) {
                return 3
            }

            return 5
        }
    }
}

private class DurationAxisRenderer(
    @ColorInt private val aheadColor: Int,
    @ColorInt private val behindColor: Int,
    viewPortHandler: ViewPortHandler,
    yAxis: YAxis,
    transformer: Transformer,
) : YAxisRenderer(viewPortHandler, yAxis, transformer) {
    override fun drawYLabels(
        c: Canvas,
        fixedPosition: Float,
        positions: FloatArray,
        offset: Float
    ) {
        val from = if (mYAxis.isDrawBottomYLabelEntryEnabled) 0 else 1
        val to =
            if (mYAxis.isDrawTopYLabelEntryEnabled) mYAxis.mEntryCount else (mYAxis.mEntryCount - 1)
        for (i in from..<to) {
            mAxisLabelPaint.setColor(if (mYAxis.mEntries[i] >= 0.0F) aheadColor else behindColor)
            c.drawText(
                mYAxis.getFormattedLabel(i),
                fixedPosition,
                positions[i * 2 + 1] + offset,
                mAxisLabelPaint
            )
        }
    }
}
