package com.stt.android.tooltips

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.app.Activity
import android.graphics.Color
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewPropertyAnimator
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import androidx.annotation.StringRes
import androidx.core.view.doOnDetach
import androidx.core.view.doOnLayout
import androidx.core.view.doOnPreDraw
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import com.stt.android.R
import com.stt.android.databinding.TooltipModalViewBinding
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume
import kotlin.math.roundToInt

object TooltipModal {
    private const val SHADOW_SIZE = 20

    suspend fun show(
        activity: Activity,
        anchor: View,
        @StringRes messageRes: Int,
        gravity: Int,
        dimAmount: Float = 0.2f,
    ) = suspendCancellableCoroutine { continuation ->
        show(
            activity,
            anchor,
            messageRes,
            gravity,
            dimAmount,
            onDismiss = { continuation.resume(Unit) },
            onCancel = { continuation.cancel() },
        )
    }

    fun show(
        activity: Activity,
        anchor: View,
        @StringRes messageRes: Int,
        gravity: Int,
        dimAmount: Float = 0.2f,
        onDismiss: () -> Unit,
        onCancel: () -> Unit,
    ) = (activity.window.decorView as? FrameLayout)?.let { parent ->
        val message = activity.getString(messageRes)
        show(parent, anchor, message, gravity, dimAmount, onDismiss, onCancel)
    }

    fun show(
        parent: FrameLayout,
        anchor: View,
        message: String,
        gravity: Int,
        dimAmount: Float = 0.2f,
        onDismiss: () -> Unit,
        onCancel: () -> Unit,
    ) = anchor.doOnLayout {
        val context = parent.context
        val density = context.resources.displayMetrics.density
        val inflater = LayoutInflater.from(context)
        TooltipModalViewBinding.inflate(inflater, parent, true).apply {
            root.setBackgroundColor(Color.argb(dimAmount, 0f, 0f, 0f))
            tooltip.text = message
            when (gravity) {
                Gravity.START or Gravity.BOTTOM -> {
                    tooltip.setBackgroundResource(R.drawable.bg_tooltip_bottom_start)
                }

                else -> Unit
            }
            tooltip.measureSize(parent.width)
            val tooltipWidth = tooltip.measuredWidth
            val tooltipHeight = tooltip.measuredHeight
            tooltip.updateLayoutParams<FrameLayout.LayoutParams> {
                width = tooltipWidth
                height = tooltipHeight
            }
            var animator: ViewPropertyAnimator? = tooltip.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(300)
            tooltip.doOnPreDraw {
                tooltip.alpha = 0f
                tooltip.translationY = tooltipHeight / 4f
                animator?.setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        animator = null
                    }
                })?.start()
            }
            tooltip.doOnDetach {
                animator?.cancel()
                animator = null
            }

            val shadow = (SHADOW_SIZE * density).roundToInt()
            val globalLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
                tooltip.isVisible = true

                val parentLoc = IntArray(2)
                parent.getLocationInWindow(parentLoc)
                val anchorLoc = IntArray(2)
                anchor.getLocationInWindow(anchorLoc)
                val anchorY = anchorLoc[1] - parentLoc[1]
                val anchorHeight = anchor.height
                when (gravity) {
                    Gravity.START or Gravity.BOTTOM -> {
                        tooltip.updateLayoutParams<FrameLayout.LayoutParams> {
                            leftMargin = (parent.width - tooltipWidth) / 2
                            topMargin = anchorY + anchorHeight / 2 - tooltipHeight + shadow
                        }
                    }

                    else -> Unit
                }
            }
            anchor.viewTreeObserver.addOnGlobalLayoutListener(globalLayoutListener)
            val attachStateListener = object : View.OnAttachStateChangeListener {
                override fun onViewAttachedToWindow(v: View) {
                }

                override fun onViewDetachedFromWindow(v: View) {
                    anchor.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
                    anchor.removeOnAttachStateChangeListener(this)
                    parent.removeView(root)
                    onCancel()
                }
            }
            anchor.addOnAttachStateChangeListener(attachStateListener)
            root.setOnClickListener {
                anchor.viewTreeObserver.removeOnGlobalLayoutListener(globalLayoutListener)
                anchor.removeOnAttachStateChangeListener(attachStateListener)
                parent.removeView(root)
                onDismiss()
            }
        }
    }

    private fun View.measureSize(parentWidth: Int) {
        val spec = if (parentWidth > 0) {
            View.MeasureSpec.makeMeasureSpec(
                (parentWidth * 0.8f).roundToInt(),
                View.MeasureSpec.AT_MOST
            )
        } else {
            View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
        }
        measure(spec, spec)
    }
}
