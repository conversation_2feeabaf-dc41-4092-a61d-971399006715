package com.stt.android.viewmodel

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.achievements.GetAchievementUseCase
import com.stt.android.domain.tags.UserTag
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workout.SharingOption
import com.stt.android.domain.workouts.GetWorkoutHeaderByIdUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.attributes.FetchUnconfirmedWorkoutAttributesUpdateUseCase
import com.stt.android.domain.workouts.attributes.workoutLocation
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.domain.workouts.tss.TSS
import com.stt.android.extensions.getFormattedDistanceForType
import com.stt.android.extensions.roundToIntOrZero
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.ui.activities.WorkoutEditDetailsActivity.Companion.EXTRA_NAVIGATION_SOURCE
import com.stt.android.ui.activities.WorkoutEditDetailsActivity.Companion.EXTRA_SHOW_EDIT_LOCATION
import com.stt.android.ui.activities.WorkoutEditDetailsActivity.Companion.EXTRA_WORKOUT_START_TIME
import com.stt.android.ui.extensions.toWorkoutLocationOrNull
import com.stt.android.ui.fragments.WorkoutDetailsEditorFragment
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.DateUtils
import com.stt.android.utils.WorkoutShareUtils
import com.stt.android.utils.getBooleanExtra
import com.stt.android.workouts.EnergyConsumptionCalculator
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.max
import kotlin.math.roundToLong

@HiltViewModel
class WorkoutDetailsEditorViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val currentUserController: CurrentUserController,
    private val getWorkoutHeaderByIdUseCase: GetWorkoutHeaderByIdUseCase,
    private val userSettingsController: UserSettingsController,
    private val workoutShareUtils: WorkoutShareUtils,
    private val infoModelFormatter: InfoModelFormatter,
    private val getAchievementUseCase: GetAchievementUseCase,
    private val fetchUnconfirmedUseCase: FetchUnconfirmedWorkoutAttributesUpdateUseCase,
    val isSubscribedToPremiumUseCase: IsSubscribedToPremiumUseCase,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker
) : ViewModel() {
    private val _viewState =
        MutableStateFlow<WorkoutDetailsEditorViewState>(WorkoutDetailsEditorViewState.Loading)
    val viewState: StateFlow<WorkoutDetailsEditorViewState> = _viewState

    private lateinit var workoutDetailsEditorContainer: WorkoutDetailsEditorContainer

    val workoutHeader: WorkoutHeader
        get() = workoutDetailsEditorContainer.workoutHeader

    val isContainerInitialized: Boolean
        get() = ::workoutDetailsEditorContainer.isInitialized

    private val _uiEvents = Channel<UiEvents>()
    val uiEvents = _uiEvents.receiveAsFlow()

    var isWorkoutHeaderUpdated = false
    var isDescriptionAdded = false
    internal val changedFields: MutableSet<WorkoutDetailsEditorFragment.EditDetailField> = HashSet()
    var originalTss: TSS? = null
    var originalSuuntoTags: List<SuuntoTag> = emptyList()
    private var shouldAutoUpdateEnergy = false
    private var energyUpdatedByAutoUpdate = false
    val measurementUnit: MeasurementUnit = userSettingsController.settings.measurementUnit

    val isNewWorkout: Boolean
        get() = !savedStateHandle.contains(WorkoutDetailsEditorFragment.KEY_WORKOUT_ID)

    val isTssUpdated: Boolean
        get() = originalTss != workoutHeader.tss

    val isTssCalculationMethodChanged: Boolean
        get() {
            val originalMethod = originalTss?.calculationMethod
            val currentMethod = workoutHeader.tss?.calculationMethod
            return originalMethod != currentMethod
        }

    val startPosition: LatLng?
        get() {
            val startPosition = workoutHeader.startPosition
            return startPosition.toWorkoutLocationOrNull()
        }

    val isWorkoutLocationUpdated: Boolean
        get() {
            val unconfirmedArgument = savedStateHandle.get<LatLng?>(WorkoutDetailsEditorFragment.KEY_UNCONFIRMED_LOCATION)
            val current = unconfirmedLocation ?: startPosition
            return (unconfirmedArgument != unconfirmedLocation) ||
                (workoutLocationUpdated && editedWorkoutLocation != current)
        }

    var unconfirmedLocation: LatLng? = null

    // This flag is needed, otherwise we couldn't distinguish between "location not edited" and
    // "location deleted".
    private var workoutLocationUpdated = false
    var editedWorkoutLocation: LatLng? = null
        set(value) {
            field = value
            workoutLocationUpdated = true
        }

    val hasWorkoutChanged: Boolean
        get() = isWorkoutHeaderUpdated || isWorkoutLocationUpdated

    var isAutoOpenEditLocation: Boolean = savedStateHandle.getBooleanExtra(EXTRA_SHOW_EDIT_LOCATION, false)

    val isSubscribedToPremium: Boolean
        get() = workoutDetailsEditorContainer.isSubscribedToPremium

    init {
        viewModelScope.launch {
            loadWorkoutHeader()
        }
    }

    private suspend fun loadWorkoutHeader() {
        val header = getWorkoutHeader()
        if (header == null) {
            Timber.w("Loading workout header in editor failed")
            _viewState.value = WorkoutDetailsEditorViewState.Error(
                Throwable("Loading workout header in editor failed")
            )
            return
        }

        originalTss = header.tss
        originalSuuntoTags = header.suuntoTags
        shouldAutoUpdateEnergy = isNewWorkout || header.energyConsumption <= 0.0
        fetchUnconfirmedUseCase(
            fetchUnconfirmedUseCase.getParams(
                workoutId = header.id,
                username = header.username
            )
        )?.workoutLocation?.let {
            unconfirmedLocation = LatLng(it.latitude, it.longitude)
        }
        val isSubscribedToPremium = isSubscribedToPremiumUseCase().first()
        // There's a chance that [updateWorkoutHeader] was called already and it created
        // the container with more up-to-date header
        if (!isContainerInitialized) {
            workoutDetailsEditorContainer = WorkoutDetailsEditorContainer(header, isSubscribedToPremium)
        }

        workoutDetailsEditorContainer = workoutDetailsEditorContainer.copy(isSubscribedToPremium = isSubscribedToPremium)

        savedStateHandle.get<String>(EXTRA_NAVIGATION_SOURCE)?.let {
            trackEditWorkoutScreen(header, it)
        }

        savedStateHandle.get<Long>(EXTRA_WORKOUT_START_TIME)?.let {
            updateStartTime(it, false)
        }

        _viewState.value = WorkoutDetailsEditorViewState.Success(
            data = workoutDetailsEditorContainer
        )
    }

    private suspend fun getWorkoutHeader(): WorkoutHeader? {
        val workoutHeader: WorkoutHeader? = if (isNewWorkout) {
            createNewWorkoutHeader()
        } else {
            val workoutId =
                savedStateHandle.get<Int>(WorkoutDetailsEditorFragment.KEY_WORKOUT_ID) ?: 0
            if (workoutId != 0) {
                getWorkoutHeaderByIdUseCase(workoutId)
            } else {
                null
            }
        }

        return workoutHeader
    }

    fun updateActivityType(activityType: ActivityType, localizedName: String) {
        Timber.d("New activity type to set: $localizedName")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.WORKOUT_TYPE)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .activityId(activityType.id)
            .locallyChanged(true)
            .build()
        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnActivityTypeUpdated(activityType))
        }

        updateEnergyIfWeShould()
    }

    fun updateEnergy(energy: Int) {
        Timber.d("New energy to set: $energy")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.ENERGY)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .energyConsumption(energy.toDouble())
            .locallyChanged(true)
            .build()
        if (energyUpdatedByAutoUpdate) {
            energyUpdatedByAutoUpdate = false
        } else {
            shouldAutoUpdateEnergy = false
        }

        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnEnergyUpdated(energy.toString()))
        }
    }

    fun updateSteps(steps: Int) {
        Timber.d("New steps to set: $steps")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.STEP_COUNT)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .stepCount(steps)
            .locallyChanged(true)
            .build()

        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnStepsUpdated(steps.toString()))
        }
    }

    fun updateAvgHr(avgHr: Int) {
        Timber.d("New avg HR to set: $avgHr")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.AVG_HR)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .heartRateAvg(avgHr.toDouble())
            .locallyChanged(true)
            .build()
        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnAvgHrUpdated(avgHr.toString()))
        }
        updateEnergyIfWeShould()
    }

    fun updateDistance(distance: Double) {
        Timber.d("New distance to set: $distance")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.DISTANCE)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .totalDistance(distance)
            .locallyChanged(true)
            .build()

        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnDistanceUpdated(formattedDistance()))
        }

        updateEnergyIfWeShould()
    }

    fun updateTss(tss: TSS) {
        Timber.d("New TSS to set: ${tss.trainingStressScore}")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.TSS)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .tss(tss)
            .locallyChanged(true)
            .build()

        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnTssUpdated(tss))
        }
    }

    fun onSpeedEntered(speedInMetersPerSecond: Double) {
        try {
            Timber.d("New max speed to set: $speedInMetersPerSecond (m/s)")
            changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.MAX_SPEED)
            isWorkoutHeaderUpdated = true
            workoutDetailsEditorContainer.workoutHeader =
                workoutDetailsEditorContainer.workoutHeader
                    .toBuilder()
                    .maxSpeed(speedInMetersPerSecond)
                    .locallyChanged(true)
                    .build()
            viewModelScope.launch {
                _uiEvents.send(UiEvents.OnMaxSpeedUpdated(formattedMaxSpeed()))
            }
        } catch (e: NumberFormatException) {
            // Ignore setting non-double value for the input
        }
    }

    fun updateAscent(ascent: Double) {
        Timber.d("New ascent to set: $ascent")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.ASCENT)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .totalAscent(ascent)
            .locallyChanged(true)
            .build()
        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnAscentUpdated(formattedAscentValue()))
        }
    }

    fun updateDescent(descent: Double) {
        Timber.d("New descent to set: $descent")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.DESCENT)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .totalDescent(descent)
            .locallyChanged(true)
            .build()
        viewModelScope.launch {
            _uiEvents.send(UiEvents.OnDescentUpdated(formattedDescentValue()))
        }
    }

    fun updateMaxHr(maxHr: Int) {
        Timber.d("New max HR to set: $maxHr")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.MAX_HR)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .heartRateMax(maxHr.toDouble())
            .locallyChanged(true)
            .build()
        viewModelScope.launch {
            _uiEvents.send(
                UiEvents.OnMaxHrUpdated(maxHr.toString())
            )
        }
    }

    fun updateStartTime(timeInMillis: Long, manuallyUpdated: Boolean) {
        Timber.d("New start time to set: $timeInMillis")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.START_TIME)
        isWorkoutHeaderUpdated = manuallyUpdated
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .startTime(timeInMillis, true)
            .locallyChanged(true)
            .build()
    }

    fun updateDuration(totalTime: Double) {
        Timber.d("New duration to set: $totalTime")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.DURATION)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .totalTime(totalTime, updateStopTime = isNewWorkout)
            .locallyChanged(true)
            .build()

        updateEnergyIfWeShould()
    }

    fun updateDescription(description: String?) {
        Timber.d("New description to set: $description")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.DESCRIPTION)
        isWorkoutHeaderUpdated = true
        if (description != null && description.isNotEmpty()) {
            isDescriptionAdded = true
        }
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .description(description)
            .locallyChanged(true)
            .build()
    }

    fun updateSuuntoTags(suuntoTags: List<SuuntoTag>) {
        Timber.d("New suuntoTags to set: $suuntoTags")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.SUUNTO_TAGS)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .suuntoTags(suuntoTags)
            .locallyChanged(true)
            .build()

        viewModelScope.launch {
            _uiEvents.send(
                UiEvents.OnTagsUpdated(
                    userTags = workoutHeader.userTags,
                    suuntoTags = workoutHeader.suuntoTags
                )
            )
        }
    }

    fun onUserTagRemoved(userTags: UserTag) {
        updateUserTags(workoutHeader.userTags.filterNot { it == userTags })
    }

    fun onSuuntoTagRemoved(suuntoTag: SuuntoTag) {
        updateSuuntoTags(workoutHeader.suuntoTags.filterNot { it == suuntoTag })
    }

    fun updateUserTags(userTags: List<UserTag>) {
        Timber.d("New userTags to set: $userTags")
        changedFields.add(WorkoutDetailsEditorFragment.EditDetailField.USER_TAGS)
        isWorkoutHeaderUpdated = true
        workoutDetailsEditorContainer.workoutHeader = workoutDetailsEditorContainer.workoutHeader
            .toBuilder()
            .userTags(userTags.toList())
            .locallyChanged(true)
            .build()

        viewModelScope.launch {
            _uiEvents.send(
                UiEvents.OnTagsUpdated(
                    userTags = workoutHeader.userTags,
                    suuntoTags = workoutHeader.suuntoTags
                )
            )
        }
    }

    fun updateWorkoutHeader(updatedWorkoutHeader: WorkoutHeader, isSubscribedToPremium: Boolean) {
        if (isContainerInitialized) {
            val originalWorkoutHeader = workoutHeader
            workoutDetailsEditorContainer.workoutHeader = updatedWorkoutHeader.toBuilder()
                .description(originalWorkoutHeader.description)
                .startTime(originalWorkoutHeader.startTime, true)
                .totalTime(originalWorkoutHeader.totalTime, false)
                .activityId(originalWorkoutHeader.activityType.id)
                .totalDistance(originalWorkoutHeader.totalDistance)
                .energyConsumption(originalWorkoutHeader.energyConsumption)
                .heartRateMax(originalWorkoutHeader.heartRateMax)
                .heartRateAvg(originalWorkoutHeader.heartRateAverage)
                .maxCadence(originalWorkoutHeader.maxCadence)
                .averageCadence(originalWorkoutHeader.averageCadence)
                .stepCount(originalWorkoutHeader.stepCount)
                .tss(originalWorkoutHeader.tss)
                .totalAscent(originalWorkoutHeader.totalAscent)
                .totalDescent(originalWorkoutHeader.totalDescent)
                .locallyChanged(true)
                .build()
        } else {
            workoutDetailsEditorContainer = WorkoutDetailsEditorContainer(updatedWorkoutHeader, isSubscribedToPremium)
        }
        isWorkoutHeaderUpdated = true
    }

    private fun updateEnergyIfWeShould() {
        if (!shouldAutoUpdateEnergy) {
            return
        }
        var energy: Int
        val avgHr = workoutHeader.heartRateAverage.roundToIntOrZero()
        energy = if (avgHr > 0) {
            val duration = workoutHeader.totalTime
            val sex = userSettingsController.settings.gender
            val weight = (userSettingsController.settings.weight / 1000.0).roundToIntOrZero()
            val age = DateUtils.calculateAge(
                userSettingsController.settings.birthDate
            )
            EnergyConsumptionCalculator.updateBasedOnHeartRate(
                avgHr,
                (duration * 1000.0).roundToLong(),
                sex,
                weight,
                age
            ).roundToIntOrZero()
        } else {
            val activityType = workoutHeader.activityType
            val weight = (userSettingsController.settings.weight / 1000.0).roundToIntOrZero()
            val duration = workoutHeader.totalTime
            val distance = workoutHeader.totalDistance
            EnergyConsumptionCalculator.updateBasedOnSpeed(
                activityType,
                weight,
                distance / duration,
                (duration * 1000.0).roundToLong()
            ).roundToIntOrZero()
        }
        energy = max(energy, 0)

        energyUpdatedByAutoUpdate = true
        updateEnergy(energy)
    }

    suspend fun getAchievementsCount(): Int = withContext(Dispatchers.IO) {
        val workoutKey = workoutHeader.key
        if (workoutKey.isNullOrBlank()) return@withContext 0

        val achievement = getAchievementUseCase(workoutKey)
        return@withContext achievement?.count ?: 0
    }

    private fun createNewWorkoutHeader(): WorkoutHeader {
        val sharingFlags = workoutShareUtils.getDefaultWorkoutBackendShareFlag()
        val now = System.currentTimeMillis()
        val username = currentUserController.username
        val heartRateUserSetMax = userSettingsController.settings.hrMaximum.toDouble()
        return WorkoutHeader.manual(
            totalDistance = 0.0,
            maxSpeed = 0.0,
            activityType = ActivityType.DEFAULT,
            avgSpeed = 0.0,
            description = "",
            startPosition = null,
            stopPosition = null,
            centerPosition = null,
            startTime = now,
            stopTime = now,
            totalTime = 0.0,
            energyConsumption = 0.0,
            username = username,
            heartRateAvg = 0.0,
            heartRateAvgPercentage = 0.0,
            heartRateMax = 0.0,
            heartRateMaxPercentage = 0.0,
            heartRateUserSetMax = heartRateUserSetMax,
            averageCadence = 0,
            maxCadence = 0,
            pictureCount = 0,
            viewCount = 0,
            commentCount = 0,
            sharingFlags = sharingFlags,
            stepCount = 0,
            reactionCount = 0,
            totalAscent = 0.0,
            totalDescent = 0.0,
            recoveryTime = 0,
            maxAltitude = 0.0,
            minAltitude = 0.0,
            tss = null,
            tssList = null,
            estimatedFloorsClimbed = 0
        )
    }

    internal fun formattedDistance(): String =
        workoutHeader.activityType.getFormattedDistanceForType(measurementUnit, workoutHeader.totalDistance)

    internal fun formattedMaxSpeed(): String =
        TextFormatter.formatSpeed(measurementUnit.toSpeedUnit(workoutHeader.maxSpeed))

    internal fun formattedDescentValue(): String = TextFormatter.formatDistanceRounded(
        measurementUnit.toShortDistanceUnit(
            workoutHeader.totalDescent
        )
    )

    internal fun formattedAscentValue(): String = TextFormatter.formatDistanceRounded(
        measurementUnit.toShortDistanceUnit(
            workoutHeader.totalAscent
        )
    )

    internal fun formatTssValueText(methodString: String): String {
        val displayedValue: Float = workoutHeader.tss?.trainingStressScore ?: 0f
        val valueString = infoModelFormatter.formatValueAsString(
            SummaryItem.TRAININGSTRESSSCORE,
            displayedValue
        )
        return "$methodString $valueString"
    }

    private suspend fun trackEditWorkoutScreen(workoutHeader: WorkoutHeader, source: String) {
        val achievementsCount = getAchievementsCount()
        val properties = AnalyticsProperties()
        val sharing = when {
            workoutHeader.sharingOptions.contains(SharingOption.EVERYONE) -> {
                "Public"
            }
            workoutHeader.sharingOptions.contains(SharingOption.FOLLOWERS) -> {
                "Followers"
            }
            else -> {
                "Private"
            }
        }
        val workoutLocation = workoutHeader.startPosition.toWorkoutLocationOrNull()
        properties
            .put(AnalyticsEventProperty.SOURCE, source)
            .put(AnalyticsEventProperty.TARGET_WORKOUT_VISIBILITY, sharing)
            .put(AnalyticsEventProperty.NUM_PHOTOS, workoutHeader.pictureCount)
            .put(AnalyticsEventProperty.NUM_LIKES, workoutHeader.reactionCount)
            .put(AnalyticsEventProperty.NUM_COMMENTS, workoutHeader.commentCount)
            .putYesNo(
                AnalyticsEventProperty.HAS_DESCRIPTION,
                !workoutHeader.description.isNullOrBlank()
            )
            .put(
                AnalyticsEventProperty.ACTIVITY_TYPE,
                workoutHeader.activityType.simpleName
            )
            .put(
                AnalyticsEventProperty.DURATION_IN_MINUTES,
                (workoutHeader.totalTime.toFloat() / 60.0f).roundToLong()
            )
            .put(AnalyticsEventProperty.DISTANCE_IN_METERS, workoutHeader.totalDistance)
            .put(AnalyticsEventProperty.NUMBER_OF_ACHIEVEMENTS, achievementsCount)
            .putYesNo(AnalyticsEventProperty.LOCATION_ADDED, workoutLocation != null)

        amplitudeAnalyticsTracker.trackEvent(
            AnalyticsEvent.EDIT_WORKOUT_SCREEN,
            properties
        )
    }
}

data class WorkoutDetailsEditorContainer(
    var workoutHeader: WorkoutHeader,
    val isSubscribedToPremium: Boolean
)

sealed class WorkoutDetailsEditorViewState {
    object Loading : WorkoutDetailsEditorViewState()
    data class Success(val data: WorkoutDetailsEditorContainer) : WorkoutDetailsEditorViewState()
    data class Error(val error: Throwable) : WorkoutDetailsEditorViewState()
}

sealed class UiEvents {
    data class OnMaxSpeedUpdated(val value: String) : UiEvents()
    data class OnEnergyUpdated(val value: String) : UiEvents()
    data class OnStepsUpdated(val value: String) : UiEvents()
    data class OnAvgHrUpdated(val value: String) : UiEvents()
    data class OnDistanceUpdated(val value: String) : UiEvents()
    data class OnAscentUpdated(val value: String) : UiEvents()
    data class OnDescentUpdated(val value: String) : UiEvents()
    data class OnMaxHrUpdated(val value: String) : UiEvents()
    data class OnTssUpdated(val tss: TSS) : UiEvents()
    data class OnActivityTypeUpdated(val activityType: ActivityType) : UiEvents()
    data class OnTagsUpdated(val userTags: List<UserTag>, val suuntoTags: List<SuuntoTag>) : UiEvents()
}
