package com.stt.android.ui.workout.widgets;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import android.view.ContextThemeWrapper;
import android.view.View;
import android.widget.TextView;

import com.stt.android.R;
import com.stt.android.ThemeColors;
import com.stt.android.controllers.UserSettingsController;
import com.stt.android.domain.user.MeasurementUnit;
import com.stt.android.domain.workout.GhostDistanceTimeState;
import com.stt.android.exceptions.GhostMatchNotFoundException;
import com.stt.android.ui.utils.TextFormatter;
import com.stt.android.utils.STTConstants;
import com.stt.android.workouts.RecordWorkoutService;
import com.stt.android.workouts.TrackingState;

import javax.inject.Inject;

public class GhostTimeDistanceWidget extends DualStateWorkoutWidget {
    private final UserSettingsController userSettingsController;
    private TextView distanceTimeValue;
    private TextView distanceUnit;
    private View container;
    private int aheadColor;
    private int behindColor;
    private int noMatchColor;

    @Inject
    public GhostTimeDistanceWidget(LocalBroadcastManager localBM,
        UserSettingsController userSettingsController) {
        super(localBM);
        this.userSettingsController = userSettingsController;
        setDefaultColor();
    }

    private BroadcastReceiver switchGhostDistanceTimeReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            GhostDistanceTimeState currentGhostTimeDistanceState =
                (GhostDistanceTimeState) intent.getSerializableExtra(
                    STTConstants.ExtraKeys.GHOST_TIME_DISTANCE_STATE);
            setIsPrimaryAndUpdate(currentGhostTimeDistanceState == GhostDistanceTimeState.DEFAULT);
        }
    };

    @Override
    protected int getLayoutId() {
        return R.layout.ghost_time_distance_widget;
    }

    @Override
    protected void onViewInflated() {
        super.onViewInflated();
        distanceTimeValue = (TextView) view.findViewById(R.id.ghostDistanceTimeValue);
        distanceUnit = (TextView) view.findViewById(R.id.ghostDistanceTimeValueUnit);
        container = view.findViewById(R.id.ghostDistanceTimeContainer);
        aheadColor = context.getResources().getColor(R.color.ghost_target_ahead);
        behindColor = context.getResources().getColor(R.color.ghost_target_behind_or_no_match);
        noMatchColor = context.getResources().getColor(R.color.ghost_target_behind_or_no_match);
        onUpdate();
    }

    private void setDefaultColor() {
        defaultColor = com.stt.android.core.R.color.near_black;
    }

    private void setDistanceValue() {
        // Reset UI to default values
        int textColor = getTextColor();
        distanceTimeValue.setTextColor(textColor);
        distanceUnit.setTextColor(textColor);
        MeasurementUnit unit = userSettingsController.getSettings().getMeasurementUnit();
        distanceUnit.setText(unit.getDistanceUnit());

        try {
            RecordWorkoutService rws = serviceConnection.getRecordWorkoutService();
            if (rws != null) {
                if (rws.getCurrentState() == TrackingState.NOT_STARTED) {
                    return;
                }

                double distanceDifference = rws.getGhostDistanceDifference();
                double absoluteDistanceDifference = Math.abs(distanceDifference);
                long roundDiffInCentimeters = Math.round(distanceDifference / 10.0);
                String distanceText =
                    TextFormatter.formatDistance(unit.toDistanceUnit(absoluteDistanceDifference));
                setGhostState(roundDiffInCentimeters, distanceText);
            } else {
                String distanceText = TextFormatter.formatDistance(unit.toDistanceUnit(0.0));
                setGhostState(0, distanceText);
            }
        } catch (GhostMatchNotFoundException ghostMatchNotFoundException) {
            setOffRouteGhostState();
        }
    }

    @Override
    protected int getPrimaryColor(){
        Context themeContext = new ContextThemeWrapper(context, R.style.WhiteTheme);
        return ThemeColors.resolveColor(themeContext, android.R.attr.textColorPrimaryInverse);
    }

    /**
     * Fetches the time difference with the ghost and presents it to the user
     */
    private void setTimeValue() {
        // Reset the color
        distanceTimeValue.setTextColor(getTextColor());
        try {
            long timeDifferenceInSeconds;
            RecordWorkoutService rws = serviceConnection.getRecordWorkoutService();
            if (rws != null) {
                if (rws.getCurrentState() == TrackingState.NOT_STARTED) {
                    return;
                }
                timeDifferenceInSeconds = (long) rws.getGhostTimeDifference();
                long absoluteTimeDifferenceInSeconds = Math.abs(timeDifferenceInSeconds);
                setGhostState(timeDifferenceInSeconds,
                    TextFormatter.formatElapsedTime(absoluteTimeDifferenceInSeconds, false));
            } else {
                setGhostState(0, TextFormatter.formatElapsedTime(0, false));
            }
        } catch (GhostMatchNotFoundException ghostMatchNotFoundException) {
            setOffRouteGhostState();
        }
    }

    private void setOffRouteGhostState() {
        container.setBackgroundColor(noMatchColor);
        distanceTimeValue.setText("--");
    }

    private void setGhostState(long aheadBehindValue, CharSequence text) {
        distanceTimeValue.setText(text);
        if (aheadBehindValue < 0) {
            container.setBackgroundColor(aheadColor);
        } else if (aheadBehindValue > 0) {
            container.setBackgroundColor(behindColor);
        }
    }

    @Override
    public void onInit() {
        onUpdate();
    }

    @Override
    public void onStart() {
        super.onStart();

        localBM.registerReceiver(switchGhostDistanceTimeReceiver,
            new IntentFilter(STTConstants.BroadcastActions.GHOST_STATE_CHANGED));
    }

    @Override
    public void onStarted() {
        onUpdate();
    }

    @Override
    public void onStop() {
        localBM.unregisterReceiver(switchGhostDistanceTimeReceiver);
        super.onStop();
    }

    @Override
    public void onStopped() {
        // update once more the text to make sure we show the right value
        onUpdate();
    }

    @Override
    protected void switchState() {
        super.switchState();
        Intent intent = new Intent(STTConstants.BroadcastActions.GHOST_STATE_CHANGED).putExtra(
            STTConstants.ExtraKeys.GHOST_TIME_DISTANCE_STATE,
            isPrimary() ? GhostDistanceTimeState.TIME : GhostDistanceTimeState.DISTANCE);
        localBM.sendBroadcast(intent);
    }

    @Override
    protected void setIsPrimaryAndUpdate(boolean isPrimary) {
        super.setIsPrimaryAndUpdate(isPrimary);
        if (isPrimary()) {
            distanceUnit.setVisibility(View.GONE);
        } else {
            distanceUnit.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected int getWidgetLabelId() {
        return R.id.ghostDistanceTimeLabel;
    }

    @Override
    protected void onUpdatePrimary() {
        setTimeValue();
    }

    @Override
    protected void onUpdateSecondary() {
        setDistanceValue();
    }

    @Override
    public void onRecordWorkoutServiceBound() {
        super.onRecordWorkoutServiceBound();
        GhostDistanceTimeState ghostState =
            serviceConnection.getRecordWorkoutService().getCurrentGhostTimeDistanceState();
        setIsPrimaryAndUpdate(ghostState == GhostDistanceTimeState.DEFAULT);
    }
}
