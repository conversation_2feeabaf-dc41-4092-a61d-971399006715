package com.stt.android.notifications

import android.content.Context
import android.content.Intent
import android.text.TextUtils
import androidx.core.app.JobIntentService
import androidx.core.os.bundleOf
import com.google.firebase.messaging.RemoteMessage
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.ReactionModel
import com.stt.android.coroutines.AppCoroutineScopeProvider
import com.stt.android.coroutines.await
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.domain.user.ReactionSummary
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.HomeActivityNavigator
import com.stt.android.home.people.PeopleController
import com.stt.android.services.JOB_ID_PUSH_NOTIFICATION_HANDLER_SERVICE
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class PushNotificationHandler : JobIntentService() {
    @Inject
    lateinit var gson: Gson

    @Inject
    lateinit var currentUserController: CurrentUserController

    @Inject
    lateinit var homeActivityNavigator: HomeActivityNavigator

    @Inject
    lateinit var reactionModel: ReactionModel

    @Inject
    lateinit var dispatchers: CoroutinesDispatchers

    @Inject
    lateinit var peopleController: PeopleController

    @Inject
    lateinit var eventTracker: EventTracker

    @Inject
    lateinit var appCoroutineScopeProvider: AppCoroutineScopeProvider

    override fun onHandleWork(intent: Intent) {
        if (!currentUserController.isLoggedIn) {
            // no notification for anonymous users is supported as of now
            Timber.w("onHandleWork: User not logged in")
            return
        }
        // TODO there's a chance the logged-in user receives notification for other users
        // it can happen e.g. when the user did an offline logout, or simply clears the data, etc.
        try {
            val extras = intent.getBundleExtra(KEY_EXTRAS)
            if (extras == null) {
                Timber.w("onHandleWork: missing KEY_EXTRAS from intent")
                return
            }
            val message: RemoteMessage? = extras.getParcelable(KEY_REMOTE_MESSAGE)
            if (message == null) {
                Timber.w("onHandleWork: missing KEY_REMOTE_MESSAGE from extras")
                return
            }
            val messageType = message.data[KEY_EXTRAS_MESSAGE_TYPE]
            val attrs = message.data[KEY_EXTRAS_ATTRS]
            if (TextUtils.isEmpty(messageType) || TextUtils.isEmpty(attrs)) {
                Timber.w("Invalid messageType (%s) or attrs (%s)", messageType, attrs)
                return
            }
            val wrapperType = object : TypeToken<PushAttr?>() {}.type
            val pushAttr = gson.fromJson<PushAttr>(attrs, wrapperType)

            if (messageType == NOTIFICATION_TYPE_SYNC) {
                STTNotificationNoUI.getNotification(
                    pushAttr
                ).apply {
                    handleCustomNotificationWithoutUI()
                }
            } else {
                STTNotificationUI.getNotification(
                    this,
                    messageType,
                    pushAttr,
                    extras,
                    homeActivityNavigator
                )?.apply {
                    handleNotification(this@PushNotificationHandler, intent)

                    // Refactor the code to allow STTNotificationUI sub classes to handle extra actions
                    if (this is MyWorkoutReactionNotification) {
                        appCoroutineScopeProvider.appCoroutineScope.launch(dispatchers.io) {
                            runSuspendCatching {
                                reactionModel.loadReactionsFromRemote(
                                    workoutKey = pushAttr.workoutKey,
                                    reactionType = ReactionSummary.REACTION_LIKE,
                                )
                            }
                        }
                    } else if (this is FollowRequestReceivedNotification) {
                        appCoroutineScopeProvider.appCoroutineScope.launch(dispatchers.io) {
                            runSuspendCatching {
                                peopleController.loadFollowListsFromBackendAndCacheLocaly().await()
                            }
                        }
                    }
                }

                STTNotificationUI.getAnalyticsPushMessageType(messageType)?.let { type ->
                    eventTracker.trackEvent(
                        AnalyticsEvent.PUSH_DELIVERED,
                        mapOf(AnalyticsEventProperty.PUSH_MESSAGE_TYPE to type),
                    )
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to handle push notification")
        }
    }

    companion object {
        const val KEY_EXTRAS = "com.stt.android.KEY_EXTRAS"
        const val LIKE_ACTION = "likeAction"
        const val KEY_REMOTE_MESSAGE = "com.stt.android.KEY_REMOTE_MESSAGE"
        const val KEY_EXTRAS_MESSAGE_TYPE = "type"
        const val KEY_EXTRAS_ATTRS = "attrs"
        const val NOTIFICATION_TYPE_SYNC = "REQUEST_SYNC"
        fun enqueueWork(context: Context, message: RemoteMessage) {
            context.apply {
                val intent = Intent(this, PushNotificationHandler::class.java)
                    .putExtra(KEY_EXTRAS, bundleOf(KEY_REMOTE_MESSAGE to message))
                enqueueWork(
                    this,
                    PushNotificationHandler::class.java,
                    JOB_ID_PUSH_NOTIFICATION_HANDLER_SERVICE,
                    intent
                )
            }
        }

        fun enqueueWork(context: Context, intent: Intent) {
            if (intent.extras == null) {
                Timber.w("enqueueWork: missing extras from intent")
                return
            }
            context.apply {
                val newIntent = Intent(this, PushNotificationHandler::class.java)
                    .setAction(intent.action)
                    .putExtras(intent.extras!!)
                enqueueWork(
                    this,
                    PushNotificationHandler::class.java,
                    JOB_ID_PUSH_NOTIFICATION_HANDLER_SERVICE,
                    newIntent
                )
            }
        }
    }
}
