package com.stt.android.notifications;

import android.app.PendingIntent;
import android.content.Context;
import androidx.core.app.NotificationCompat;
import com.stt.android.R;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.social.userprofileV2.BaseUserProfileActivity;

class FacebookFriendNotification extends STTNotificationUI {

    FacebookFriendNotification(Context context, PushAttr pushAttr) {
        super(context, pushAttr, NotificationChannelIds.CHANNEL_ID_FACEBOOK_FRIEND_JOINED,
            NotificationGroup.GROUP_ID_FACEBOOK_FRIEND_JOINED);
    }

    @Override
    protected boolean isEnabled() {
        return getNotificationSettings().facebookFriendJoinPushEnabled();
    }

    @Override
    protected NotificationCompat.Builder getBuilder() throws InternalDataException {
        NotificationCompat.Builder builder = super.getBuilder();
        if (builder != null) {
            String text =
                context.getString(R.string.facebook_friend_joined, pushAttr.getRealName());
            return builder.setContentText(text)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(text));
        } else {
            return null;
        }
    }

    @Override
    protected int getNotificationId() {
        return createUniqueNotificationId(R.string.facebook_friend_joined, pushAttr.getUsername());
    }

    @Override
    protected PendingIntent getContentIntent() {
        return PendingIntent.getActivity(context, getNotificationId(),
            BaseUserProfileActivity.newStartIntent(context, pushAttr.getUsername(), true),
            getFlagsWithImmutable(PendingIntent.FLAG_UPDATE_CURRENT));
    }
}
