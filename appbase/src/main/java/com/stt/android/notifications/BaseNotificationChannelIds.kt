@file:Jvm<PERSON><PERSON>("NotificationChannelIds")
@file:JvmMultifileClass

package com.stt.android.notifications

import androidx.annotation.StringDef

/**
 * Notification channel and group ids common to all build variants.
 *
 * Android sorts groups and channels on the notification settings page alphabetically based on id.
 * Numbers are added to the ids to keep the desired order.
 */

/* BEGIN Channel group Ids */
const val CHANNEL_GROUP_ID_MY_ACTIVITIES = "channel_group_id_100_my_activities"
const val CHANNEL_GROUP_ID_SOCIAL = "channel_group_id_200_social"
const val CHANNEL_GROUP_ID_BRAND = "channel_group_id_300_brand"
/* END Channel group Ids */

/* BEGIN Channel ids */
// group CHANNEL_GROUP_ID_MY_ACTIVITIES
const val CHANNEL_ID_ACTIVITY_RECORDING = "channel_id_110_activity_recording"
const val CHANNEL_ID_MY_ACTIVITY_LIKES = "channel_id_120_my_activity_likes"
const val CHANNEL_ID_MY_ACTIVITY_COMMENTS = "channel_id_130_my_activity_comments"
const val CHANNEL_ID_PERSONAL_ACHIEVEMENTS = "channel_id_140_personal_achievements"

// group CHANNEL_GROUP_ID_SOCIAL
const val CHANNEL_ID_NEW_FOLLOWERS = "channel_id_210_new_followers"
const val CHANNEL_ID_FACEBOOK_FRIEND_JOINED = "channel_id_220_facebook_friend_joined"
const val CHANNEL_ID_FRIEND_ACTIVITY_SHARED = "channel_id_230_friend_activity_shared"
const val CHANNEL_ID_FRIEND_ACTIVITY_COMMENT = "channel_id_240_friend_activity_comment"

// group CHANNEL_GROUP_ID_BRAND
const val CHANNEL_ID_CRITICAL_INFORMATION = "channel_id_310_critical_information"
const val CHANNEL_ID_APP_UPDATES = "channel_id_320_app_updates"
const val CHANNEL_ID_EVENTS_AND_CHALLENGES = "channel_id_330_events_and_challenges"
const val CHANNEL_ID_UPDATES_FROM_COMMUNITY = "channel_id_340_updates_from_community"
const val CHANNEL_ID_TRAINING_PLAN_UPDATES = "channel_id_350_training_plan_updates"
const val CHANNEL_ID_UPCOMING_PERIOD = "channel_id_360_upcoming_period"
const val CHANNEL_ID_LOG_PERIOD_REMINDER = "channel_id_370_log_period_reminder"

// No group / other group
const val CHANNEL_ID_FOREGROUND_SYNC = "channel_id_foreground_feed_sync"
/* END Channel ids */

/* BEGIN Notification Groups */
// the fact that we have 1 groupId per channelId is a chosen convention. Notification Groups and channels are completely separate.
enum class NotificationGroup(val id: Int, val groupName: String) {
    GROUP_ID_ACTIVITY_RECORDING(1001, "GROUP_ID_ACTIVITY_RECORDING"),
    GROUP_ID_MY_ACTIVITY_LIKES(1002, "GROUP_ID_MY_ACTIVITY_LIKES"),
    GROUP_ID_MY_ACTIVITY_COMMENTS(1003, "GROUP_ID_MY_ACTIVITY_COMMENTS"),
    GROUP_ID_PERSONAL_ACHIEVEMENTS(1004, "GROUP_ID_PERSONAL_ACHIEVEMENTS"),
    GROUP_ID_NEW_FOLLOWERS(2001, "GROUP_ID_NEW_FOLLOWERS"),
    GROUP_ID_FACEBOOK_FRIEND_JOINED(2002, "GROUP_ID_FACEBOOK_FRIEND_JOINED"),
    GROUP_ID_FRIEND_ACTIVITY_SHARED(2003, "GROUP_ID_FRIEND_ACTIVITY_SHARED"),
    GROUP_ID_FRIEND_ACTIVITY_COMMENT(2004, "GROUP_ID_FRIEND_ACTIVITY_COMMENT"),
    GROUP_ID_CRITICAL_INFORMATION(3001, "GROUP_ID_CRITICAL_INFORMATION"),
    GROUP_ID_APP_UPDATES(3002, "GROUP_ID_APP_UPDATES"),
    GROUP_ID_EVENTS_AND_CHALLENGES(3003, "GROUP_ID_EVENTS_AND_CHALLENGES"),
    GROUP_ID_UPDATES_FROM_COMMUNITY(3004, "GROUP_ID_UPDATES_FROM_COMMUNITY"),
    GROUP_ID_TRAINING_PLAN_UPDATES(3005, "GROUP_ID_TRAINING_PLAN_UPDATES"),
    GROUP_ID_UPCOMING_PERIOD(3006, "GROUP_ID_UPCOMING_PERIOD"),
    GROUP_ID_LOG_PERIOD_REMINDER(3007, "GROUP_ID_LOG_PERIOD_REMINDER"),
}
/* END Notification Groups */

@StringDef(
    CHANNEL_ID_ACTIVITY_RECORDING,
    CHANNEL_ID_MY_ACTIVITY_LIKES,
    CHANNEL_ID_MY_ACTIVITY_COMMENTS,
    CHANNEL_ID_PERSONAL_ACHIEVEMENTS,
    CHANNEL_ID_NEW_FOLLOWERS,
    CHANNEL_ID_FACEBOOK_FRIEND_JOINED,
    CHANNEL_ID_FRIEND_ACTIVITY_SHARED,
    CHANNEL_ID_FRIEND_ACTIVITY_COMMENT,
    CHANNEL_ID_CRITICAL_INFORMATION,
    CHANNEL_ID_APP_UPDATES,
    CHANNEL_ID_EVENTS_AND_CHALLENGES,
    CHANNEL_ID_UPDATES_FROM_COMMUNITY,
    CHANNEL_ID_TRAINING_PLAN_UPDATES,
    CHANNEL_ID_UPCOMING_PERIOD,
    CHANNEL_ID_LOG_PERIOD_REMINDER,
)
annotation class ChannelId
