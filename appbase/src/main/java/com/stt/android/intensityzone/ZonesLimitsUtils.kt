package com.stt.android.intensityzone

import com.soy.algorithms.intensity.IntensityZonesData
import com.stt.android.controllers.UserSettingsController
import com.stt.android.domain.sml.SmlExtensionStreamPoint
import com.stt.android.domain.user.workoutextension.IntensityExtension
import com.stt.android.domain.workouts.WorkoutHeader
import kotlin.math.roundToInt

object ZonesLimitsUtils {
    fun getHrZoneLimits(
        workoutHeader: WorkoutHeader,
        intensityExtension: IntensityExtension?,
        userSettingsController: UserSettingsController,
        maxHeartRateInWorkout: Float
    ): IntensityZoneLimits {
        val heartRateMax = workoutHeader.heartRateUserSetMax.let {
            when {
                it > 0 -> it.roundToInt()
                else -> userSettingsController.settings.hrMaximum
            }
        }
        val intensityZoneLimits = IntensityZoneLimits.createHrIntensityZoneLimits(
            heartRateMax,
            intensityExtension?.intensityZones?.hr,
        )
        val last = intensityZoneLimits.zoneLimits.last()
        return if (maxHeartRateInWorkout > last.end) {
            val zoneLimits = intensityZoneLimits.zoneLimits.dropLast(1)
            intensityZoneLimits.copy(zoneLimits = mutableListOf<ZoneRange>().apply {
                addAll(zoneLimits)
                add(last.copy(end = maxHeartRateInWorkout))
            })
        } else {
            intensityZoneLimits
        }
    }

    fun getPaceZoneLimits(
        paces: List<Pair<Long, Float>>,
        zones: IntensityZonesData,
    ): IntensityZoneLimits {
        var minValue = paces.first().second
        var maxValue = minValue
        paces.forEach {
            minValue = it.second.coerceAtMost(minValue)
            maxValue = it.second.coerceAtLeast(maxValue)
        }
        val limits = listOf(
            minValue,
            zones.zone5LowerLimit,
            zones.zone4LowerLimit,
            zones.zone3LowerLimit,
            zones.zone2LowerLimit,
            maxValue.coerceAtLeast(zones.zone5LowerLimit)
        )
        return IntensityZoneLimits(
            listOf(
                ZoneRange(IntensityZone.WARMUP, limits[4], limits[5]),
                ZoneRange(IntensityZone.ENDURANCE, limits[3], limits[4]),
                ZoneRange(IntensityZone.AEROBIC, limits[2], limits[3]),
                ZoneRange(IntensityZone.ANAEROBIC, limits[1], limits[2]),
                ZoneRange(IntensityZone.PEAK, limits.first(), limits[1])
            )
        )
    }

    fun getPowerZoneLimits(
        powers: List<SmlExtensionStreamPoint>,
        zones: IntensityZonesData,
    ): IntensityZoneLimits {
        var minValue = powers.first().value
        var maxValue = minValue
        powers.forEach {
            minValue = it.value.coerceAtMost(minValue)
            maxValue = it.value.coerceAtLeast(maxValue)
        }
        val limits = listOf(
            minValue,
            zones.zone2LowerLimit,
            zones.zone3LowerLimit,
            zones.zone4LowerLimit,
            zones.zone5LowerLimit,
            maxValue.coerceAtLeast(zones.zone5LowerLimit)
        )
        return IntensityZoneLimits(
            listOf(
                ZoneRange(IntensityZone.WARMUP, limits.first(), limits[1]),
                ZoneRange(IntensityZone.ENDURANCE, limits[1], limits[2]),
                ZoneRange(IntensityZone.AEROBIC, limits[2], limits[3]),
                ZoneRange(IntensityZone.ANAEROBIC, limits[3], limits[4]),
                ZoneRange(IntensityZone.PEAK, limits[4], limits[5])
            )
        )
    }
}
