package com.stt.android.session

import android.content.SharedPreferences
import androidx.annotation.WorkerThread
import androidx.core.content.edit
import androidx.work.WorkManager
import com.stt.android.analytics.AmplitudeAnalyticsTracker
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsProperties
import com.stt.android.analytics.notificationAnalytics.NotificationsAnalyticsJob
import com.stt.android.analytics.userDetailsAnalytics.UserDetailsAnalyticsJob
import com.stt.android.billing.StartupSubscriptionSync
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.core.utils.TimeProvider
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.activitydata.logout.ActivityDataHelper
import com.stt.android.data.usersettings.UserSettingsSynchronizer
import com.stt.android.domain.UserSession
import com.stt.android.domain.refreshable.Refreshable
import com.stt.android.domain.user.User
import com.stt.android.home.people.PeopleController
import com.stt.android.menstrualcycle.MenstrualCycleRemoteSyncJob
import com.stt.android.offlinemaps.domain.DownloadOfflineMapWorker
import com.stt.android.utils.STTConstants.DefaultPreferences.KEY_LAST_STARTUP_SYNC_EPOCH_MS
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.util.concurrent.TimeUnit
import javax.inject.Inject

/**
 * Trigger server sync for data that should be synced only when process is created
 */
class StartupSync
@Inject constructor(
    private val activityDataHelper: ActivityDataHelper,
    private val workManager: dagger.Lazy<WorkManager>,
    private val currentUserController: CurrentUserController,
    private val backendController: BackendController,
    private val peopleController: PeopleController,
    private val startupSubscriptionSync: StartupSubscriptionSync,
    private val sharedPreferences: SharedPreferences,
    private val userSettingsSynchronizer: UserSettingsSynchronizer,
    private val timeProvider: TimeProvider,
    private val dispatchers: CoroutinesDispatchers,
    private val amplitudeAnalyticsTracker: AmplitudeAnalyticsTracker,
) : Refreshable {

    override suspend fun refresh() {
        sync()
    }

    @WorkerThread
    fun syncBlocking(session: UserSession) = runBlocking {
        sync(session)
    }

    private suspend fun sync(
        session: UserSession = currentUserController.session ?: throw NotLoggedInException()
    ) {
        val start = timeProvider.elapsedRealtime()

        Timber.d("Syncing current session started")
        runSuspendCatching {
            fetchAndStoreCurrentUser(session)
            migrateToFollowModelIfNeeded(session, currentUserController.currentUser)
            startupSubscriptionSync(session, isInitialSync())
            fetchAndStoreUserFollowStatuses(session)

            UserDetailsAnalyticsJob.schedule(workManager.get())
            NotificationsAnalyticsJob.schedule(workManager.get())
            userSettingsSynchronizer.fetchAndStoreUserSettings()
            activityDataHelper.start247DataSync()
            MenstrualCycleRemoteSyncJob.schedule(workManager.get())
            DownloadOfflineMapWorker.schedule(workManager.get())
        }.onFailure { e ->
            Timber.w(e, "Error occurs when pushing changes to backend")
            val syncErrorDuration =
                TimeUnit.MILLISECONDS.toSeconds(timeProvider.elapsedRealtime() - start)
            amplitudeAnalyticsTracker.trackEvent(
                AnalyticsEvent.BACKEND_SYNC_ERROR,
                AnalyticsProperties().put(
                    AnalyticsEventProperty.SYNC_DURATION,
                    syncErrorDuration
                )
            )
        }

        saveLastSyncTimestamp()

        Timber.d("Syncing current session finished")
    }

    /**
     * This method populates [CurrentUserController.currentUser] which in return
     * populates [UserSession] which is needed for follow up requests in the app
     *
     * If this method isn't called right after login, the user is considered logged out
     */
    private suspend fun fetchAndStoreCurrentUser(session: UserSession) = withContext(dispatchers.io) {
        val user = backendController.fetchSessionUser(session).user
        Timber.d("Fetched current user: ${user.session?.sessionKey}")
        currentUserController.store(user)
    }

    private suspend fun fetchAndStoreUserFollowStatuses(
        session: UserSession
    ) = withContext(dispatchers.io) {
        val followLists = backendController.getFollowersAndFollowings(session).toFollowLists()
        peopleController.updateUserFollowStatuses(followLists)
    }

    private fun saveLastSyncTimestamp() {
        sharedPreferences.edit {
            putLong(KEY_LAST_STARTUP_SYNC_EPOCH_MS, timeProvider.currentTimeMillis())
        }
    }

    private fun isInitialSync() = sharedPreferences.getLong(KEY_LAST_STARTUP_SYNC_EPOCH_MS, 0L) == 0L

    private suspend fun migrateToFollowModelIfNeeded(
        session: UserSession,
        currentUser: User
    ) = withContext(dispatchers.io) {
        val isFollowModel = currentUser.followModel
        if (isFollowModel == null || !isFollowModel) {
            val migratedUser = backendController.migrateUserToFollowModel(session, currentUser).user
            val isMigratedSuccessfully = migratedUser.followModel
            if (isMigratedSuccessfully != null && isMigratedSuccessfully) {
                Timber.d("User migrated successfully to follow model")
            } else {
                throw FollowModelMigrationException()
            }
        }
    }
}

class NotLoggedInException : IllegalStateException("User session is null. Cannot execute start up sync")
class FollowModelMigrationException : IllegalStateException("Unable to migrate to follow model")
