package com.stt.android.workoutdetail.trend

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.content.IntentCompat
import androidx.fragment.app.DialogFragment
import androidx.viewpager.widget.ViewPager
import com.amersports.formatter.unit.jscience.JScienceUnitConverter
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.ui.SimpleDialogFragment
import com.stt.android.common.ui.SimpleDialogFragment.Companion.newInstance
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.RecentWorkoutTrendActivityBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.user.workout.RecentWorkoutTrend
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.premium.PremiumRequiredToAccessHandler
import com.stt.android.ui.adapters.BigRecentWorkoutPagerAdapter.OnSelectedWorkoutChangedListener
import com.stt.android.ui.adapters.BigRecentWorkoutTrendPagerAdapter
import com.stt.android.ui.components.workout.WorkoutCard
import com.stt.android.ui.components.workout.WorkoutCardViewModel
import com.stt.android.utils.MPAndroidChartUtils.clearViewPool
import com.stt.android.utils.STTConstants
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * This class looks quite similar to RecentWorkoutTrendFragment, but really not easy to merge them.
 */
@AndroidEntryPoint
class RecentWorkoutTrendActivity : AppCompatActivity(), RecentWorkoutTrendView,
    ViewPager.OnPageChangeListener, OnSelectedWorkoutChangedListener,
    SimpleDialogFragment.Callback {
    @Inject
    lateinit var recentWorkoutTrendPresenter: RecentWorkoutTrendPresenter

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var premiumRequiredToAccessHandler: PremiumRequiredToAccessHandler

    @Inject
    lateinit var infoModelFormatter: InfoModelFormatter

    @Inject
    lateinit var unitConverter: JScienceUnitConverter

    private val workoutCardViewModel: WorkoutCardViewModel by viewModels()

    private lateinit var binding: RecentWorkoutTrendActivityBinding

    private var bigRecentWorkoutTrendPagerAdapter: BigRecentWorkoutTrendPagerAdapter? = null
    private var selectedWorkoutHeader: WorkoutHeader? = null
    private var lastKnownPage = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val referenceWorkout = intent?.let {
            IntentCompat.getParcelableExtra(
                it,
                STTConstants.ExtraKeys.WORKOUT_HEADER,
                WorkoutHeader::class.java
            )
        }

        recentWorkoutTrendPresenter.setReferenceWorkout(referenceWorkout)
        recentWorkoutTrendPresenter.setLimit(0)

        binding = RecentWorkoutTrendActivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

        lastKnownPage = savedInstanceState?.getInt(PAGER_STATE, 0) ?: 0

        @StringRes val titleRes =
            if (recentWorkoutTrendPresenter.routeSelection == RouteSelection.ON_THIS_ROUTE) {
                R.string.on_this_route_capital
            } else {
                R.string.previous_on_all_route_capital
            }
        binding.title.setText(titleRes)

        binding.recentWorkoutTrendToolbar.setTitle(titleRes)
        setSupportActionBar(binding.recentWorkoutTrendToolbar)
        supportActionBar?.let {
            it.setDisplayShowHomeEnabled(true)
            it.setDisplayHomeAsUpEnabled(true)
        }

        binding.backButton?.setOnClickListener { onBackPressedDispatcher.onBackPressed() }

        binding.trendViewPager.addOnPageChangeListener(this)

        premiumRequiredToAccessHandler.onCreate(this)
        premiumRequiredToAccessHandler.startCheckingForPremiumAccess(
            this,
            binding.root,
            getString(R.string.buy_premium_workout_recent_trends_description),
            AnalyticsPropertyValue.BuyPremiumPopupShownSource.RECENT_WORKOUT_TRENDS_SCREEN,
            null,
        )
    }

    private fun showWorkoutHeader(workoutHeader: WorkoutHeader) {
        selectedWorkoutHeader = workoutHeader
        binding.workoutSummaryView.setContentWithM3Theme {
            WorkoutCard(
                workoutHeader = workoutHeader,
                viewModel = workoutCardViewModel,
                onClick = {
                    recentWorkoutTrendPresenter.onWorkoutSummaryViewClicked(workoutHeader)
                },
            )
        }
    }

    public override fun onSaveInstanceState(outState: Bundle) {
        outState.putInt(PAGER_STATE, lastKnownPage)
        super.onSaveInstanceState(outState)
    }

    override fun onStart() {
        super.onStart()
        recentWorkoutTrendPresenter.takeView(this)
    }

    override fun onStop() {
        recentWorkoutTrendPresenter.dropView()
        super.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        clearViewPool()
    }

    override fun onSupportNavigateUp(): Boolean {
        onBackPressedDispatcher.onBackPressed()
        return true
    }

    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
        // do nothing
    }

    override fun onPageSelected(position: Int) {
        lastKnownPage = position

        val selectedWorkoutHeader = bigRecentWorkoutTrendPagerAdapter?.getSelectedWorkout(position)
            ?: recentWorkoutTrendPresenter.getReferenceWorkout()
        showWorkoutHeader(selectedWorkoutHeader)
    }

    override fun onPageScrollStateChanged(state: Int) {
        // do nothing
    }

    override fun onSelectedWorkoutChanged(workoutHeader: WorkoutHeader) {
        showWorkoutHeader(workoutHeader)
    }

    override fun onTrendLoaded(recentWorkoutTrend: RecentWorkoutTrend) {
        bigRecentWorkoutTrendPagerAdapter = BigRecentWorkoutTrendPagerAdapter(
            this,
            recentWorkoutTrend,
            this,
            ContextCompat.getColor(this, R.color.blue),
            ContextCompat.getColor(this, R.color.accent),
            infoModelFormatter,
            unitConverter
        )
        binding.trendViewPager.adapter = bigRecentWorkoutTrendPagerAdapter
        binding.slidingTabs.setupWithViewPager(binding.trendViewPager)
        binding.trendViewPager.currentItem = lastKnownPage
        onPageSelected(lastKnownPage)
        showWorkoutHeader(recentWorkoutTrend.currentWorkout)
        hideProgressBar()
    }

    override fun onNoTrendOnSameRouteAvailable() {
        binding.recentWorkoutTrendToolbar.setTitle(R.string.previous_on_all_route_capital)
        binding.title.setText(R.string.previous_on_all_route_capital)
        hideProgressBar()
    }

    override fun onNoTrendDataAvailable() {
        showErrorDialog()
    }

    override fun onTrendLoadFailed() {
        showErrorDialog()
    }

    override fun openDetailsForWorkout(
        username: String,
        workoutId: Int?,
        workoutKey: String?
    ) {
        rewriteNavigator.navigate(
            this,
            username,
            workoutId,
            workoutKey,
            null,
            false,
            false,
            false,
            false
        )
    }

    override fun onDialogButtonPressed(tag: String?, which: Int) {
        finish() // Finish activity when error dialog is closed
    }

    override fun onDialogDismissed(tag: String?) {
        finish() // Finish activity when error dialog is closed
    }

    private fun showErrorDialog() {
        val fragmentManager = supportFragmentManager
        if (fragmentManager.findFragmentByTag(ERROR_DIALOG_TAG) == null) {
            val dialog: DialogFragment =
                newInstance(getString(R.string.error_0), null, getString(R.string.ok))
            dialog.show(supportFragmentManager, ERROR_DIALOG_TAG)
        }
    }

    private fun hideProgressBar() {
        binding.progressBarContainer.visibility = View.GONE
    }

    companion object {
        private const val PAGER_STATE = "pager_state"
        private const val ERROR_DIALOG_TAG = "RecentWorkoutTrendErrorDialog"

        fun newStartIntent(context: Context?, currentWorkout: WorkoutHeader?): Intent {
            return Intent(
                context,
                RecentWorkoutTrendActivity::class.java
            ).putExtra(STTConstants.ExtraKeys.WORKOUT_HEADER, currentWorkout)
        }
    }
}
