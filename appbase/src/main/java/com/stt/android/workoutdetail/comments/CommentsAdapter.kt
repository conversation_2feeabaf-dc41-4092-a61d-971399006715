package com.stt.android.workoutdetail.comments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.stt.android.R

internal class CommentsAdapter(
    private val onDeleteCommentRequested: (commentKey: String?) -> Unit,
) : ListAdapter<CommentsDialogUiState.Loaded.Item, BaseCommentItemViewHolder<CommentsDialogUiState.Loaded.Item>>(
    CommentsDiffUtilItemCallback()
) {
    override fun getItemViewType(position: Int): Int = when (getItem(position)) {
        is CommentsDialogUiState.Loaded.Item.Description -> DESCRIPTION
        is CommentsDialogUiState.Loaded.Item.Comment -> COMMENT
    }

    @Suppress("UNCHECKED_CAST")
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): BaseCommentItemViewHolder<CommentsDialogUiState.Loaded.Item> = when (viewType) {
        DESCRIPTION -> DescriptionViewHolder(
            itemView = LayoutInflater.from(parent.context)
                .inflate(R.layout.comments_dialog_description, parent, false),
        )

        COMMENT -> CommentViewHolder(
            itemView = LayoutInflater.from(parent.context)
                .inflate(R.layout.popup_comment_item, parent, false),
            onDeleteCommentRequested = onDeleteCommentRequested,
        )

        else -> throw IllegalArgumentException("Unsupported view type: $viewType")
    } as BaseCommentItemViewHolder<CommentsDialogUiState.Loaded.Item>

    override fun onBindViewHolder(
        holder: BaseCommentItemViewHolder<CommentsDialogUiState.Loaded.Item>,
        position: Int,
    ) {
        holder.bind(getItem(position))
    }

    private companion object {
        const val DESCRIPTION = 0
        const val COMMENT = 1
    }
}

internal abstract class BaseCommentItemViewHolder<T : CommentsDialogUiState.Loaded.Item>(
    itemView: View,
) : RecyclerView.ViewHolder(itemView) {
    abstract fun bind(item: T)
}

private class DescriptionViewHolder(
    itemView: View,
) : BaseCommentItemViewHolder<CommentsDialogUiState.Loaded.Item.Description>(itemView) {
    private val descriptionTextView: TextView = itemView.findViewById(R.id.description)

    override fun bind(item: CommentsDialogUiState.Loaded.Item.Description) {
        descriptionTextView.text = item.description
    }
}

private class CommentViewHolder(
    itemView: View,
    onDeleteCommentRequested: (commentKey: String?) -> Unit,
) : BaseCommentItemViewHolder<CommentsDialogUiState.Loaded.Item.Comment>(itemView) {
    private val commentView = itemView as PopupWorkoutCommentView
    private var item: CommentsDialogUiState.Loaded.Item.Comment? = null

    init {
        val onLongClickDeleteComment = View.OnLongClickListener { _ ->
            item?.let { onDeleteCommentRequested(it.comment.key) }

            true
        }
        commentView.setOnLongClickListener(onLongClickDeleteComment)
    }

    override fun bind(item: CommentsDialogUiState.Loaded.Item.Comment) {
        this.item = item
        commentView.setWorkoutComment(item.comment)
        commentView.isLongClickable = item.deletable
    }
}

private class CommentsDiffUtilItemCallback :
    DiffUtil.ItemCallback<CommentsDialogUiState.Loaded.Item>() {
    override fun areItemsTheSame(
        oldItem: CommentsDialogUiState.Loaded.Item,
        newItem: CommentsDialogUiState.Loaded.Item,
    ): Boolean = when {
        oldItem is CommentsDialogUiState.Loaded.Item.Description &&
            newItem is CommentsDialogUiState.Loaded.Item.Description -> true

        oldItem is CommentsDialogUiState.Loaded.Item.Comment &&
            newItem is CommentsDialogUiState.Loaded.Item.Comment -> oldItem.comment.key == newItem.comment.key

        else -> false
    }

    override fun areContentsTheSame(
        oldItem: CommentsDialogUiState.Loaded.Item,
        newItem: CommentsDialogUiState.Loaded.Item,
    ): Boolean = oldItem == newItem
}
