package com.stt.android.workoutdetail.location

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.os.BundleCompat
import androidx.core.os.postDelayed
import androidx.fragment.app.viewModels
import com.google.android.gms.maps.model.LatLng
import com.stt.android.R
import com.stt.android.common.ui.ViewModelFragment2
import com.stt.android.databinding.FragmentWorkoutLocationBinding
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.maps.MarkerZPriority
import com.stt.android.maps.OnMapReadyCallback
import com.stt.android.maps.SuuntoBitmapDescriptorFactory
import com.stt.android.maps.SuuntoMap
import com.stt.android.maps.SuuntoMapOptions
import com.stt.android.maps.SuuntoMarker
import com.stt.android.maps.SuuntoMarkerOptions
import com.stt.android.maps.SuuntoSupportMapFragment
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class WorkoutLocationFragment : ViewModelFragment2() {
    override val viewModel: WorkoutLocationViewModel by viewModels()
    private val viewDataBinding: FragmentWorkoutLocationBinding get() = requireBinding()

    override fun getLayoutResId(): Int = R.layout.fragment_workout_location
    private var map: SuuntoMap? = null

    private var locationMarker: SuuntoMarker? = null

    private var onClickListener: WorkoutLocationClickListener? = null

    private val fadeInMapHandler = Handler(Looper.getMainLooper())

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        viewDataBinding.workoutLocationAddButton.setOnClickListener {
            onClickListener?.onWorkoutLocationClicked()
        }
        return view
    }

    private fun getCoordinateArgument() =
        BundleCompat.getParcelable(requireArguments(), ARGUMENT_COORDINATE, LatLng::class.java)

    private fun getRequiresUserConfirmationArgument() =
        requireArguments().getBoolean(ARGUMENT_REQUIRES_USER_CONFIRMATION, false)

    @Deprecated("Deprecated in Java")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        loadMap()
    }

    private fun loadMap() {
        map?.clear()
        val fm = childFragmentManager
        var mapFragment = fm.findFragmentByTag(MAP_FRAGMENT_TAG) as SuuntoSupportMapFragment?
        if (mapFragment == null) {
            val options = SuuntoMapOptions()
                .mapType(viewModel.selectedMapType.name)
                .compassEnabled(false)
                .rotateGesturesEnabled(false)
                .scrollGesturesEnabled(false)
                .tiltGesturesEnabled(false)
                .zoomControlsEnabled(false)
                .zoomGesturesEnabled(false)
                .mapToolbarEnabled(false)
                .logoEnabled(false)
                .attributionEnabled(false)
                .liteMode(true)
                .textureMode(true)
                .showMyLocationMarker(false)
            mapFragment = SuuntoSupportMapFragment.newInstance(options)
            fm.beginTransaction().add(R.id.mapContainer, mapFragment, MAP_FRAGMENT_TAG).commitNow()
        }
        mapFragment.getMapAsync(
            callback = OnMapReadyCallback { map ->
                <EMAIL> = map
                getCoordinateArgument()?.run {
                    if (!getRequiresUserConfirmationArgument()) setLocationMarker(this)
                }
                viewModel.onMapReady(map)
                // show the map delayed to smooth the ui
                showMapDelayed()
            }
        )
    }

    private fun showMapDelayed() {
        fadeInMapHandler.postDelayed(200) {
            if (isAdded) viewDataBinding.mapContainer.visibility = View.VISIBLE
        }
    }

    private fun setLocationMarker(latLng: LatLng) {
        locationMarker?.remove()
        context?.run {
            val startMarker = SuuntoMarkerOptions()
                .position(latLng)
                .icon(
                    SuuntoBitmapDescriptorFactory(this)
                        .fromResource(R.drawable.map_pin)
                )
                .anchor(0.5f, 1f)
                .zPriority(MarkerZPriority.START_POINT)

            locationMarker = map?.addMarker(startMarker)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        map?.clear()
        fadeInMapHandler.removeCallbacksAndMessages(null)
    }

    fun setWorkoutLocationClickListener(listener: WorkoutLocationClickListener) {
        onClickListener = listener
    }

    companion object {
        const val ARGUMENT_COORDINATE = "argument_coordinate"
        const val ARGUMENT_REQUIRES_USER_CONFIRMATION = "argument_requires_user_confirmation"
        const val ARGUMENT_WORKOUT_STOP_TIME = "argument_workout_stop_time"
        private const val MAP_FRAGMENT_TAG = "WorkoutLocationMapFragment"
        const val FRAGMENT_TAG = "WorkoutLocationFragment"

        @JvmStatic
        fun newInstance(
            latLng: LatLng?,
            requiresUserConfirmation: Boolean,
            workoutHeader: WorkoutHeader?
        ): WorkoutLocationFragment {
            return WorkoutLocationFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(ARGUMENT_COORDINATE, latLng)
                    putBoolean(
                        ARGUMENT_REQUIRES_USER_CONFIRMATION,
                        requiresUserConfirmation && latLng != null
                    )
                    if (workoutHeader != null) {
                        putLong(ARGUMENT_WORKOUT_STOP_TIME, workoutHeader.stopTime)
                    }
                }
            }
        }
    }
}
