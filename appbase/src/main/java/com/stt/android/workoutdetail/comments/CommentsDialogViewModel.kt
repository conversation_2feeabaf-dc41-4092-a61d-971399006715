package com.stt.android.workoutdetail.comments

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.core.content.IntentCompat
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.WorkoutCommentController
import com.stt.android.controllers.load
import com.stt.android.domain.comments.DeleteWorkoutCommentUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.STTConstants
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class CommentsDialogViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val currentUserController: CurrentUserController,
    private val workoutCommentController: WorkoutCommentController,
    private val deleteWorkoutCommentUseCase: DeleteWorkoutCommentUseCase,
    private val localBroadcastManager: LocalBroadcastManager,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {
    private val _commentsUiState: MutableStateFlow<CommentsDialogUiState> = MutableStateFlow(CommentsDialogUiState.Loading)
    val commentsUiState: StateFlow<CommentsDialogUiState> = _commentsUiState.asStateFlow()

    var workoutHeader: WorkoutHeader
        get() = requireNotNull(savedStateHandle[STTConstants.ExtraKeys.WORKOUT_HEADER])
        private set(value) {
            savedStateHandle[STTConstants.ExtraKeys.WORKOUT_HEADER] = value
        }

    private val workoutHeaderMonitor: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val workoutId = when (intent.action) {
                STTConstants.BroadcastActions.WORKOUT_UPDATED ->
                    intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_ID, 0)

                STTConstants.BroadcastActions.WORKOUT_SYNCED ->
                    intent.getIntExtra(STTConstants.ExtraKeys.WORKOUT_OLD_ID, 0)

                else -> return
            }

            if (workoutHeader.id == workoutId) {
                val updateWorkoutHeader = IntentCompat.getParcelableExtra(
                    intent,
                    STTConstants.ExtraKeys.WORKOUT_HEADER,
                    WorkoutHeader::class.java,
                ) ?: return
                workoutHeader = updateWorkoutHeader
                loadComments()
            }
        }
    }

    private fun loadComments() {
        _commentsUiState.value = CommentsDialogUiState.Loading

        val workoutHeader = workoutHeader
        workoutCommentController.load(workoutHeader.key)
            .onEach { workoutComments ->
                val items = buildList {
                    workoutHeader.description
                        ?.takeUnless(String::isEmpty)
                        ?.let { add(CommentsDialogUiState.Loaded.Item.Description(it)) }

                    val currentUsername = currentUserController.username
                    workoutComments.forEach { workoutComment ->
                        val comment = CommentsDialogUiState.Loaded.Item.Comment(
                            comment = workoutComment,
                            deletable = workoutComment.username == currentUsername ||
                                workoutHeader.username == currentUsername,
                        )
                        add(comment)
                    }
                }

                _commentsUiState.value = CommentsDialogUiState.Loaded(items)
            }
            .catch { e ->
                Timber.w(e, "Failed to load comments")
                _commentsUiState.value = CommentsDialogUiState.Error
            }
            .launchIn(viewModelScope)
    }

    init {
        val intentFilter = IntentFilter(STTConstants.BroadcastActions.WORKOUT_UPDATED)
            .apply { addAction(STTConstants.BroadcastActions.WORKOUT_SYNCED) }
        localBroadcastManager.registerReceiver(workoutHeaderMonitor, intentFilter)

        loadComments()
    }

    override fun onCleared() {
        localBroadcastManager.unregisterReceiver(workoutHeaderMonitor)

        super.onCleared()
    }

    suspend fun sendComment(comment: String) = withContext(coroutinesDispatchers.io) {
        val currentUser = currentUserController.currentUser
        val workoutComment = WorkoutComment(
            null,
            workoutHeader.key,
            comment,
            currentUser.username,
            currentUser.realNameOrUsername,
            currentUser.profileImageUrl,
        )
        workoutCommentController.sendComment(workoutComment)
    }

    suspend fun deleteComment(commentKey: String) {
        deleteWorkoutCommentUseCase(commentKey)
    }
}
