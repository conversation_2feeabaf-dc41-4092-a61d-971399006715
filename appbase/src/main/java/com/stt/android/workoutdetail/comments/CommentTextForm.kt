package com.stt.android.workoutdetail.comments

import android.content.Context
import android.util.AttributeSet
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.inputmethod.EditorInfo
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.TextView.OnEditorActionListener
import com.stt.android.databinding.CommentTextFormBinding
import com.stt.android.utils.KeyboardUtil

class CommentTextForm : LinearLayout, OnEditorActionListener {
    interface OnSubmitListener {
        fun onSubmit(text: String)
    }

    private val binding = CommentTextFormBinding.inflate(LayoutInflater.from(context), this)
        .apply {
            text.setOnEditorActionListener(this@CommentTextForm)
        }

    var onSubmitListener: OnSubmitListener? = null

    constructor(context: Context) : super(context)

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    init {
        orientation = HORIZONTAL
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        binding.text.isEnabled = enabled
    }

    override fun onEditorAction(v: TextView, actionId: Int, event: KeyEvent?): Boolean {
        if (v != binding.text || event == null) {
            return false
        }

        if (actionId == EditorInfo.IME_ACTION_SEND ||
            (event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_UP)
        ) {
            binding.text
                .text
                ?.toString()
                ?.trim()
                ?.takeUnless(String::isEmpty)
                ?.let { text ->
                    hideKeyboard()
                    onSubmitListener?.onSubmit(text)
                }

            return true
        } else if (event.keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) {
            // Required to receive ACTION_UP that will handle the action
            return true
        }

        return false
    }

    fun clearText() {
        binding.text.text = null
    }

    fun requestTextFocus(): Boolean = binding.text.requestFocus()

    fun hideKeyboard() {
        KeyboardUtil.hideKeyboardFrom(context, binding.text)
    }
}
