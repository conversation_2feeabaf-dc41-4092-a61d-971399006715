package com.stt.android.analytics

import android.app.Application
import com.stt.android.eventtracking.EventTracker
import com.stt.android.utils.FlavorUtils
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AmplitudeAnalyticsTrackerImpl @Inject constructor(
    private val eventTracker: EventTracker,
) : AmplitudeAnalyticsTracker {

    /**
     * Initializes Amplitude analytics. Must be called before tracking events.
     *
     * Amplitude API key must be defined in application resources with name "amplitude_key".
     *
     * @param app Application
     */
    override fun initialize(app: Application) {
        eventTracker.initialize(
            app,
            app.getString(R.string.amplitude_key),
            app.getString(com.stt.android.R.string.event_tracker_watch_key),
            debug = com.stt.android.BuildConfig.DEBUG,
            chinaVersion = FlavorUtils.isSuuntoAppChina,
        )
    }

    /**
     * Sets user id for analytics.
     *
     * @param analyticsUUID User id
     * @return `true` if UUID has changed
     */
    override fun setUUID(analyticsUUID: String?): Boolean {
        val previousUserId = eventTracker.userId
        if (previousUserId == null || previousUserId != analyticsUUID) {
            eventTracker.userId = analyticsUUID
            return true
        }
        return false
    }

    /**
     * Gets user id for analytics.
     */
    override fun getUUID(): String? {
        return eventTracker.userId
    }

    /**
     * Logs an event with event properties.
     *
     * @param event Event name
     * @param properties Properties
     */
    override fun trackEvent(
        @AnalyticsEvent.EventName event: String,
        properties: AnalyticsProperties
    ) {
        trackEvent(event, properties.map)
    }

    /**
     * Logs an event with a single event property.
     *
     * @param event Event name
     * @param propertyName Event property name
     * @param propertyValue Property value
     */
    override fun trackEvent(
        @AnalyticsEvent.EventName event: String,
        propertyName: String,
        propertyValue: Any
    ) {
        trackEvent(
            event,
            AnalyticsProperties().put(propertyName, propertyValue)
        )
    }

    /**
     * Logs and event with event properties.
     *
     * @param event Event name
     * @param properties Properties
     */
    override fun trackEvent(
        @AnalyticsEvent.EventName event: String,
        properties: Map<String, Any>
    ) {
        try {
            eventTracker.trackEvent(event, properties)
        } catch (e: Throwable) {
            Timber.w(e, "Failed to track event")
        }
    }

    /**
     * Logs an event without properties.
     *
     * @param event Event name
     */
    override fun trackEvent(@AnalyticsEvent.EventName event: String) {
        try {
            eventTracker.trackEvent(event)
        } catch (e: Throwable) {
            Timber.w(e, "Failed to track event")
        }
    }

    /**
     * Sets a single user property.
     *
     * @param name User property name
     * @param value Property value
     */
    override fun trackUserProperty(name: String, value: Any) {
        trackUserProperties(mapOf(name to value))
    }

    /**
     * Sets multiple user properties.
     *
     * @param properties Properties
     */
    override fun trackUserProperties(properties: AnalyticsProperties) {
        trackUserProperties(properties.map)
    }

    /**
     * Sets multiple user properties.
     *
     * @param properties Properties
     */
    override fun trackUserProperties(properties: Map<String, Any>) {
        try {
            eventTracker.trackUserProperties(properties)
        } catch (e: Throwable) {
            Timber.w(e, "EventTracker failed to track user property")
        }
    }

    /**
     * Logout pushes unsynced events, sets userId to null and clears user properties.
     */
    override fun logout() {
        eventTracker.userId = null
    }

    /**
     * Get the current device id. Can be null if deviceId hasn't been initialized yet.
     *
     * @return A unique identifier for tracking within the analytics system.
     */
    override fun getDeviceId(): String? = eventTracker.deviceId

    override fun analyticsInitialised(): Boolean = getDeviceId() != null
}
