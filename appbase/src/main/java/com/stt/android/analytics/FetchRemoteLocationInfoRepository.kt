package com.stt.android.analytics

import com.stt.android.data.locationinfo.FetchLocationInfoDataResource
import com.stt.android.remote.locationinfo.LocationInfo
import javax.inject.Inject

class FetchRemoteLocationInfoRepository @Inject constructor(private val fetchLocationInfoDataResource: FetchLocationInfoDataResource) {

    suspend fun fetchLocationInfo(): LocationInfo =
        fetchLocationInfoDataResource.fetchLocationInfo()
}
