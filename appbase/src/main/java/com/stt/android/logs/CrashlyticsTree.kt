package com.stt.android.logs

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import timber.log.Timber

class CrashlyticsTree : Timber.Tree() {
    override fun isLoggable(tag: String?, priority: Int) = priority >= Log.WARN

    override fun log(priority: Int, tag: String?, message: String, throwable: Throwable?) {
        // See https://firebase.google.com/docs/crashlytics/upgrade-sdk?platform=android
        val logString = when (priority) {
            Log.ERROR -> "E/"
            Log.DEBUG -> "D/"
            Log.INFO -> "I/"
            Log.VERBOSE -> "V/"
            Log.WARN -> "W/"
            Log.ASSERT -> "A/"
            else -> ""
        } + (tag?.let { "$it:" } ?: "") + message
        FirebaseCrashlytics.getInstance().log(logString)
        throwable?.let { FirebaseCrashlytics.getInstance().recordException(it) }
    }
}
