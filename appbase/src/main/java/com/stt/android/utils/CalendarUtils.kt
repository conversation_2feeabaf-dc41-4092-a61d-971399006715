package com.stt.android.utils

import android.content.Context
import android.text.format.DateUtils
import android.text.style.TextAppearanceSpan
import androidx.core.text.buildSpannedString
import androidx.core.text.inSpans
import com.stt.android.R
import com.stt.android.data.toEpochMilli
import com.stt.android.extensions.capitalize
import com.stt.android.home.diary.diarycalendar.DiaryCalendarListContainer
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.Year
import java.time.ZoneId
import java.time.temporal.TemporalField
import java.util.Date
import java.util.Formatter
import java.util.Locale

object CalendarUtils {

    @JvmStatic
    fun formatYearTitle(
        startDate: LocalDate,
    ): String {
        return startDate.year.toString()
    }

    @OptIn(ExperimentalStdlibApi::class)
    @JvmStatic
    fun formatMonthTitle(startOfMonth: LocalDate, context: Context): String {
        val flags = if (startOfMonth.year == Year.now().value) {
            DateUtils.FORMAT_NO_YEAR or DateUtils.FORMAT_NO_MONTH_DAY
        } else {
            DateUtils.FORMAT_NO_MONTH_DAY
        }

        val zoneId = ZoneId.systemDefault()
        val startOfMonthMillis = startOfMonth.atStartOfDay().atZone(zoneId).toEpochMilli()

        return DateUtils.formatDateRange(
            context,
            Formatter(StringBuilder(), Locale.getDefault()),
            startOfMonthMillis,
            startOfMonthMillis,
            flags,
            null // System default time zone
        )
            ?.toString()
            ?.capitalize(Locale.getDefault()) ?: ""
    }

    @JvmStatic
    fun formatWeekTitle(
        startOfWeek: LocalDate,
        endOfWeek: LocalDate,
        context: Context
    ): String {
        val flags = if (startOfWeek.year == Year.now().value) {
            DateUtils.FORMAT_NO_YEAR or DateUtils.FORMAT_NUMERIC_DATE
        } else {
            DateUtils.FORMAT_NUMERIC_DATE
        }

        // Add 1 to endMillis because [DateUtils.formatDateRange()] has well documented special
        // handling for when the end time is at midnight. Without it, DateUtils would consider it
        // the end of the previous day and it would show one day too little.
        val zoneId = ZoneId.systemDefault()
        val endMillis = endOfWeek.atStartOfDay().atZone(zoneId).toEpochMilli() + 1
        val startMillis = startOfWeek.atStartOfDay().atZone(zoneId).toEpochMilli()

        return DateUtils.formatDateRange(
            context,
            Formatter(StringBuilder(), Locale.getDefault()),
            startMillis,
            endMillis,
            flags,
            null // System default time zone
        )
            .toString()
    }

    fun formatDateRangeTitle(
        startDate: LocalDate,
        endDate: LocalDate,
        context: Context
    ): String {
        val flags = if (startDate.year == Year.now().value) {
            DateUtils.FORMAT_NO_YEAR or DateUtils.FORMAT_NUMERIC_DATE
        } else {
            DateUtils.FORMAT_NUMERIC_DATE
        }

        val zoneId = ZoneId.systemDefault()
        val startMillis = startDate.atStartOfDay().atZone(zoneId).toEpochMilli()
        val endMillis = endDate.atStartOfDay().atZone(zoneId).toEpochMilli() + 1

        return DateUtils.formatDateRange(
            context,
            Formatter(StringBuilder(), Locale.getDefault()),
            startMillis,
            endMillis,
            flags,
            null // System default time zone
        )
            .toString()
    }

    @JvmStatic
    fun getTitleBasedOnGranularity(
        startDate: LocalDate,
        endDate: LocalDate,
        granularity: DiaryCalendarListContainer.Granularity,
        context: Context
    ) = when (granularity) {
        DiaryCalendarListContainer.Granularity.WEEK -> formatWeekTitle(startDate, endDate, context)

        DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> if (LocalDate.now() in startDate..endDate) {
            context.getString(R.string.last_30_days)
        } else {
            formatDateRangeTitle(startDate, endDate, context)
        }

        DiaryCalendarListContainer.Granularity.MONTH -> formatMonthTitle(startDate, context)

        DiaryCalendarListContainer.Granularity.YEAR -> formatYearTitle(startDate)
    }

    @JvmStatic
    fun getTitleSpanBasedOnGranularity(
        startDate: LocalDate,
        endDate: LocalDate,
        granularity: DiaryCalendarListContainer.Granularity,
        context: Context
    ): CharSequence = when (granularity) {
        DiaryCalendarListContainer.Granularity.WEEK -> formatWeekTitle(startDate, endDate, context)

        DiaryCalendarListContainer.Granularity.LAST_30_DAYS -> if (LocalDate.now() in startDate..endDate) {
            buildSpannedString {
                append(context.getString(R.string.last_30_days))
                append("\n")
                inSpans(TextAppearanceSpan(context, R.style.Body_Small)) {
                    append(formatDateRangeTitle(startDate, endDate, context))
                }
            }
        } else {
            formatDateRangeTitle(startDate, endDate, context)
        }

        DiaryCalendarListContainer.Granularity.MONTH -> formatMonthTitle(startDate, context)

        DiaryCalendarListContainer.Granularity.YEAR -> formatYearTitle(startDate)
    }

    @JvmStatic
    fun buildDayOfWeekLabels(startDate: LocalDate, dayOfWeek: TemporalField): List<String> {
        val locale = Locale.getDefault()
        val startOfWeek = startDate.with(dayOfWeek, 1)

        // Day of week labels
        val weekDayLabelFormat = SimpleDateFormat("EEEEE", locale)
        val weekDayLabels = mutableListOf<String>()
        for (i in 0L until 7L) {
            val epochMillis = startOfWeek
                .plusDays(i)
                .atStartOfDay(ZoneId.systemDefault())
                .toEpochMilli()

            weekDayLabels.add(weekDayLabelFormat.format(Date(epochMillis)))
        }

        return weekDayLabels
    }

    @JvmStatic
    fun getTodayWeekLabelIndex(dayOfWeek: TemporalField): Int {
        val nowLocalDate = LocalDate.now()
        val startOfWeek = nowLocalDate.with(dayOfWeek, 1)
        val weekDays = mutableListOf<Long>()
        for (i in 0L until 7L) {
            val epochMillis = startOfWeek
                .plusDays(i)
                .atStartOfDay(ZoneId.systemDefault())
                .toEpochMilli()
            weekDays.add(epochMillis)
        }
        val today = nowLocalDate.atStartOfDay(ZoneId.systemDefault()).toEpochMilli()
        return weekDays.indexOf(today)
    }
}
