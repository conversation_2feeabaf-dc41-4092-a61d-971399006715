package com.stt.android.colorfultrack

import androidx.annotation.ColorRes
import com.google.android.gms.maps.model.LatLng
import com.google.android.gms.maps.model.LatLngBounds

data class WorkoutGeoPointsWithColor(
    val points: List<PointWithTimestamp>,
    @ColorRes val color: Int,
) {
    val startTime: Long get() = points.firstOrNull()?.timestamp ?: 0L
    val endTime: Long get() = points.lastOrNull()?.timestamp ?: 0L
}

data class PointWithTimestamp(val timestamp: Long, val latLng: LatLng)

/**
 * updateLapTrack for select lap. when value = ture, only draw selected lap track, and set colorful tracks translucent. otherwise, redraw all tracks
 */
data class WorkoutColorfulTrackMapData(
    // TODO Merge routeWithDashLinePoints with partitionedTracksPoints to be something like
    //  val activityRoutesWithColor: List<WorkoutGeoPointsWithColor>
    //  val nonActivityRoutes: List<List<LatLng>>
    val nonActivityRoutesWithColor: List<WorkoutGeoPointsWithColor>,
    val activityRoutesWithColor: List<WorkoutGeoPointsWithColor>,
    val nonActivityRoutes: List<List<LatLng>>,
    val activityRoutes: List<List<LatLng>>,
    val bounds: LatLngBounds,
    val lapTracks: List<WorkoutGeoPointsWithColor> = emptyList(),
    val updateLapTrack: Boolean = false,
) {
    val startPoint: LatLng? = activityRoutesWithColor
        .firstOrNull()
        ?.points
        ?.firstOrNull()
        ?.latLng

    val endPoint: LatLng? = activityRoutesWithColor
        .lastOrNull()
        ?.points
        ?.lastOrNull()
        ?.latLng
}
