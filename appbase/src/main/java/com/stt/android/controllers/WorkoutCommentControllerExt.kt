package com.stt.android.controllers

import com.stt.android.workoutdetail.comments.WorkoutComment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import timber.log.Timber

fun WorkoutCommentController.load(workoutKey: String?): Flow<List<WorkoutComment>> = flow {
    if (workoutKey.isNullOrEmpty()) {
        emit(emptyList())
        return@flow
    }

    emit(find(workoutKey))

    val userSession = currentUserController.session ?: return@flow
    val fetched = runCatching {
        backendController.fetchWorkoutComments(userSession, workoutKey)
    }.getOrElse { e ->
        Timber.w(e, "Failed to fetch workout comments")
        emptyList()
    }
    emit(fetched)

    val workoutOwnerUsername = workoutHeaderController.find(workoutKey)
        ?.username
        ?: return@flow
    val shouldStore = currentUserController.username == workoutOwnerUsername ||
        isFolloweeUseCase.isFollowee(workoutOwnerUsername)
    if (shouldStore) {
        removeByWorkoutKey(workoutKey)
        store(fetched)
    }
}
    .distinctUntilChanged()
    .flowOn(Dispatchers.IO)
