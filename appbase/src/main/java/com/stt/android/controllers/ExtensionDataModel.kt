package com.stt.android.controllers

import android.text.TextUtils
import androidx.annotation.WorkerThread
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.WorkoutExtension
import com.stt.android.exceptions.InternalDataException
import com.stt.android.remote.extensions.ExtensionsRemoteApi
import com.stt.android.remote.extensions.RemoteWorkoutExtension
import com.stt.android.utils.toV1
import io.reactivex.BackpressureStrategy
import kotlinx.coroutines.rx2.rxObservable
import rx.Observable
import rx.functions.Func1
import timber.log.Timber
import java.util.Collections
import java.util.concurrent.Callable
import java.util.function.Function
import java.util.stream.Collectors

abstract class ExtensionDataModel<T : WorkoutExtension>(
    private val extensionDataAccess: ExtensionDataAccess<T>,
    private val workoutHeaderController: WorkoutHeaderController,
    private val extensionsRemoteApi: ExtensionsRemoteApi,
    private val extensionDataAccessMap: Map<Class<out WorkoutExtension>, ExtensionDataAccess<out WorkoutExtension>>,
) {
    @WorkerThread
    @Throws(InternalDataException::class)
    private fun store(extension: T) {
        extensionDataAccess.store(extension)
    }

    /**
     * Find all extensions in table type of [T]
     *
     * @return all rows from extension table
     * @throws InternalDataException if we encounter error when querying database
     */
    @WorkerThread
    @Throws(InternalDataException::class)
    fun findAll(): List<T> = extensionDataAccess.findAll()

    @WorkerThread
    @Throws(InternalDataException::class)
    fun store(extensions: List<T>) {
        extensionDataAccess.store(extensions)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeByWorkoutId(workoutId: Int) {
        extensionDataAccess.removeByWorkoutId(workoutId)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun removeByWorkoutIds(workoutIds: Collection<Int>) {
        extensionDataAccess.removeByWorkoutIds(workoutIds)
    }

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findByWorkoutId(workoutId: Int): T? = extensionDataAccess.findByWorkoutId(workoutId)

    @WorkerThread
    @Throws(InternalDataException::class)
    fun findByWorkoutIds(workoutIds: Collection<Int>): Map<Int, T> {
        if (workoutIds.isEmpty()) {
            return emptyMap()
        }
        return extensionDataAccess.findByWorkoutIds(workoutIds)
            .stream()
            .collect(Collectors.toMap(WorkoutExtension::workoutId, Function.identity<T>()))
    }

    /**
     * Updates or inserts an extension to the data store
     * @param workoutId The workout ID to which this extension is associated to
     * @param extension The extension to store
     * @throws InternalDataException in case of DB error
     */
    @WorkerThread
    @Throws(InternalDataException::class)
    fun upsert(workoutId: Int, extension: WorkoutExtension) {
        if (findByWorkoutId(workoutId) != null) {
            removeByWorkoutId(workoutId)
        }
        store(extension as T)
    }

    /**
     * Loads extension observable from local and remote.
     *
     * @param workoutHeader the workout header
     * @return Extension observable. Note that This method may return null wrapper as Observable on
     * exception
     */
    fun load(workoutHeader: WorkoutHeader): Observable<T?> {
        val local = getLocal(workoutHeader)
        if (TextUtils.isEmpty(workoutHeader.key)) {
            return local
        }
        return local.flatMap(Func1 { extension: T? ->
            val extensionFetched = extensionsFetchedRuntimeCache[workoutHeader.key]
            if (extension != null) {
                Observable.just(extension)
            } else if (workoutHeader.extensionsFetched || extensionFetched != null && extensionFetched) {
                // extensions already fetched, but this extension doesn't exist
                // for the current workout so just emit null
                Observable.just(null)
            } else {
                getRemote(workoutHeader)
            }
        })
    }

    private fun getLocal(workoutHeader: WorkoutHeader): Observable<T?> {
        return Observable.fromCallable<T>(Callable { findByWorkoutId(workoutHeader.id) })
            .onErrorResumeNext(Func1 { t: Throwable? ->
                Timber.w(
                    t,
                    "Error fetching extension: %s",
                    getBackendExtensionType()
                )
                Observable.just(null)
            })
    }

    private fun getRemote(workoutHeader: WorkoutHeader): Observable<T?> {
        val workoutKey = workoutHeader.key
        if (workoutKey.isNullOrEmpty()) {
            return Observable.just(null)
        }
        return rxObservable {
            val fetchedExtensions = fetchExtensionsFromBackend(workoutHeader)
            try {
                for (extension in fetchedExtensions) {
                    (extensionDataAccessMap[extension.javaClass] as? ExtensionDataAccess<WorkoutExtension>)
                        ?.store(extension)
                }

                // We update both extensionsFetchedRuntimeCache and db because
                // updates to db are not visible to other extensions loading
                // logic until the workout header is reloaded from db
                extensionsFetchedRuntimeCache[workoutKey] = true
                val updatedWorkoutHeader = workoutHeader.toBuilder()
                    .extensionsFetched(true)
                    .build()
                workoutHeaderController.store(updatedWorkoutHeader)
            } catch (e: InternalDataException) {
                Timber.w(e, "Failed to store extensions")
            }
            send(fetchedExtensions)
        }.toV1(BackpressureStrategy.LATEST)
            .map(::findExtension)
            .onErrorResumeNext { e ->
                Timber.w(e, "Error during fetch of remote extensions")
                Observable.just(null)
            }
    }

    private suspend fun fetchExtensionsFromBackend(workoutHeader: WorkoutHeader): List<WorkoutExtension> {
        val types = RemoteWorkoutExtension.Type.textValues

        val key = workoutHeader.key
            ?: throw IllegalArgumentException("workout key cannot be null")

        val remoteExtensions = extensionsRemoteApi.fetchExtensions(
            workoutKey = key,
            extensionTypes = types,
        )
        return ExtensionRemoteMapper.convertRemoteExtensions(
            workoutId = workoutHeader.id,
            remoteWorkoutExtensions = remoteExtensions,
        )
    }

    /**
     * @return extension type used by backend, see [BackendWorkoutExtension]
     */
    abstract fun getBackendExtensionType(): String?

    /**
     * Gets extension from the list of provided extension.
     */
    abstract fun findExtension(extensions: List<WorkoutExtension>): T?

    companion object {
        private val extensionsFetchedRuntimeCache: MutableMap<String, Boolean> =
            Collections.synchronizedMap<String, Boolean>(HashMap<String, Boolean>())

        fun clearCache() {
            extensionsFetchedRuntimeCache.clear()
        }
    }
}
