package com.stt.android.controllers;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import com.j256.ormlite.dao.Dao;
import com.j256.ormlite.stmt.DeleteBuilder;
import com.j256.ormlite.stmt.QueryBuilder;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.user.follow.IsFolloweeUseCase;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.workoutdetail.comments.WorkoutComment;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class WorkoutCommentController {
    private final Dao<WorkoutComment, Integer> dao;
    final BackendController backendController;
    final CurrentUserController currentUserController;
    final WorkoutHeaderController workoutHeaderController;
    final IsFolloweeUseCase isFolloweeUseCase;

    @Inject
    public WorkoutCommentController(
        DatabaseHelper helper,
        BackendController backendController,
        CurrentUserController currentUserController,
        WorkoutHeaderController workoutHeaderController,
        IsFolloweeUseCase isFolloweeUseCase
    ) {
        try {
            this.dao = helper.getDao(WorkoutComment.class);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
        this.backendController = backendController;
        this.currentUserController = currentUserController;
        this.workoutHeaderController = workoutHeaderController;
        this.isFolloweeUseCase = isFolloweeUseCase;
    }

    @WorkerThread
    public void sendComment(final WorkoutComment workoutComment)
        throws InternalDataException, BackendException {
        UserSession userSession = currentUserController.getSession();
        if (userSession == null) {
            throw new IllegalStateException("No one's logged in");
        }
        backendController.pushNewWorkoutComment(userSession, workoutComment);
        store(Collections.singletonList(workoutComment));
    }

    @NonNull
    @WorkerThread
    public List<WorkoutComment> find(String workoutKey) throws InternalDataException {
        if (TextUtils.isEmpty(workoutKey)) {
            return Collections.emptyList();
        }

        try {
            QueryBuilder<WorkoutComment, Integer> qb = dao.queryBuilder();
            qb.orderBy(WorkoutComment.DbFields.TIMESTAMP, true)
                .where()
                .eq(WorkoutComment.DbFields.WORKOUT_KEY, workoutKey);
            return dao.query(qb.prepare());
        } catch (Throwable e) {
            throw new InternalDataException("Unable to find workout comments from local database", e);
        }
    }

    /**
     * Returns a map from workout keys to all comments of that workout.
     */
    @NonNull
    @WorkerThread
    public Map<String, List<WorkoutComment>> find(@NonNull List<String> workoutKeys) throws InternalDataException {
        if (workoutKeys.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            QueryBuilder<WorkoutComment, Integer> qb = dao.queryBuilder();
            qb.orderBy(WorkoutComment.DbFields.TIMESTAMP, true)
                .where()
                .in(WorkoutComment.DbFields.WORKOUT_KEY, workoutKeys);
            return dao.query(qb.prepare())
                .stream()
                .collect(Collectors.groupingBy(WorkoutComment::getWorkoutKey));
        } catch (Throwable e) {
            throw new InternalDataException("Unable to find workout comments from local database", e);
        }
    }

    @WorkerThread
    public void store(final List<WorkoutComment> workoutComments) throws InternalDataException {
        try {
            dao.callBatchTasks((Callable<Void>) () -> {
                for (WorkoutComment workoutComment : workoutComments) {
                    dao.createOrUpdate(workoutComment);
                }
                return null;
            });
        } catch (Exception e) {
            throw new InternalDataException("Unable to store workout comments to local database",
                e);
        }
    }

    @WorkerThread
    public int removeByWorkoutKey(String workoutKey) throws InternalDataException {
        try {
            DeleteBuilder<WorkoutComment, Integer> db = dao.deleteBuilder();
            db.where().eq(WorkoutComment.DbFields.WORKOUT_KEY, workoutKey);
            return dao.delete(db.prepare());
        } catch (SQLException e) {
            throw new InternalDataException("Unable to delete workout comment from local database",
                e);
        }
    }

    @WorkerThread
    public int removeByCommentKey(String commentKey) throws InternalDataException {
        if (commentKey == null) {
            return 0;
        }

        try {
            DeleteBuilder<WorkoutComment, Integer> db = dao.deleteBuilder();
            db.where().eq(WorkoutComment.DbFields.KEY, commentKey);
            return dao.delete(db.prepare());
        } catch (SQLException e) {
            throw new InternalDataException("Unable to delete workout comment from local database",
                e);
        }
    }

    @WorkerThread
    public void empty() throws InternalDataException {
        try {
            dao.deleteBuilder().delete();
        } catch (SQLException e) {
            throw new InternalDataException("Unable to empty workout comments from local database",
                e);
        }
    }
}
