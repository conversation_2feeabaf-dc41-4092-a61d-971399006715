package com.stt.android.controllers;

import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.WorkerThread;
import androidx.core.util.Pair;
import com.facebook.AccessToken;
import com.facebook.GraphRequest;
import com.facebook.GraphResponse;
import com.facebook.HttpMethod;
import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.google.gson.reflect.TypeToken;
import com.squareup.moshi.Moshi;
import com.stt.android.data.facebook.PermissionResponse;
import com.stt.android.data.facebook.PermissionStatus;
import com.stt.android.domain.ResponseWrapper;
import com.stt.android.domain.STTErrorCodes;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.user.BackendReaction;
import com.stt.android.domain.user.BackendUser;
import com.stt.android.domain.user.BackendUserWorkoutPair;
import com.stt.android.domain.user.BackendWorkout;
import com.stt.android.domain.user.BackendWorkoutComment;
import com.stt.android.domain.user.FacebookToken;
import com.stt.android.domain.user.FetchedResultList;
import com.stt.android.domain.user.ImageInformation;
import com.stt.android.domain.user.Reaction;
import com.stt.android.domain.user.User;
import com.stt.android.domain.user.UserSearchResult;
import com.stt.android.domain.user.VideoInformation;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.BackendException;
import com.stt.android.follow.BackendFollowLists;
import com.stt.android.follow.BackendFollowStatusChange;
import com.stt.android.network.HttpResponseException;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.remote.BaseRemoteApi;
import com.stt.android.remote.otp.GenerateOTPUseCase;
import com.stt.android.remote.otp.OTPGenerationException;
import com.stt.android.workoutdetail.comments.WorkoutComment;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import timber.log.Timber;

@SuppressWarnings("TypeParameterUnusedInFormals")
public abstract class BaseBackendController {
    private static final String OFFSET_PARAM_NAME = "offset";
    private static final String LIMIT_PARAM_NAME = "limit";

    final ANetworkProvider networkProvider;
    final Gson gson;
    private final GenerateOTPUseCase generateOTPUseCase;
    private final Moshi moshi;

    public BaseBackendController(
        ANetworkProvider networkProvider,
        Gson gson,
        GenerateOTPUseCase generateOTPUseCase,
        Moshi moshi
    ) {
        this.networkProvider = networkProvider;
        this.gson = gson;
        this.generateOTPUseCase = generateOTPUseCase;
        this.moshi = moshi;
    }

    /**
     * Convenient method that wraps the actual fetching of Json and takes care of the exceptions.
     */
    <E> E fetchJsonWithAuth(@NonNull UserSession session, @NonNull String url,
        @NonNull Type wrapperType) throws BackendException {
        return fetchJsonWithAuth(session, url, wrapperType, null);
    }

    private <E> E fetchJsonWithAuth(@NonNull UserSession session, @NonNull String url,
        @NonNull Type wrapperType, @Nullable STTErrorCodes errorCode) throws BackendException {
        return fetchJsonWithOptionalHeaders(url, wrapperType, session.getAuthorizationHeaders(),
            null, errorCode);
    }

    <E> E fetchJsonWithOptionalHeaders(@NonNull String url, @NonNull Type wrapperType,
        @Nullable Map<String, String> headers, @Nullable List<Pair<?, ?>> params)
        throws BackendException {
        return fetchJsonWithOptionalHeaders(url, wrapperType, headers, params, null);
    }

    <E> E fetchJsonWithOptionalHeaders(@NonNull String url, @NonNull Type wrapperType,
        @Nullable Map<String, String> headers, @Nullable List<Pair<?, ?>> params,
        @Nullable STTErrorCodes errorCode) throws BackendException {
        try {
            ResponseWrapper<E> response =
                networkProvider.getJson(url, headers, params, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (JsonParseException | IOException | HttpResponseException e) {
            String message = "Error communicating with backend. URL: " + url;
            Timber.e(e, message);
            if (errorCode != null) {
                throw new BackendException(errorCode, e);
            } else {
                throw new BackendException(message, e);
            }
        }
    }

    public BackendUser fetchSessionUser(UserSession session) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user");
        Type wrapperType = new TypeToken<ResponseWrapper<BackendUser.Builder>>() {
        }.getType();
        BackendUser.Builder builder = fetchJsonWithAuth(session, url, wrapperType);
        return builder.build(session);
    }

    public FetchedResultList<BackendWorkout> fetchWorkouts(UserSession session, String username)
        throws BackendException {
        return fetchAllWorkouts(session,
            ANetworkProvider.buildSecureBackendUrl("/workouts/" + username + "/public"), 50, 50);
    }

    private FetchedResultList<BackendWorkout> fetchAllWorkouts(UserSession session, String url,
        int partialFetchAmount, int totalAmount) throws BackendException {
        Pair<String, Integer> limitParameter = new Pair<>(LIMIT_PARAM_NAME, partialFetchAmount);
        List<BackendWorkout> result;
        List<Pair<?, ?>> params = new ArrayList<>(2);
        params.add(new Pair<>(OFFSET_PARAM_NAME, 0));
        params.add(limitParameter);
        FetchedResultList<BackendWorkout> partialResult =
            partialFetchWorkouts(session, url, params);
        result = partialResult.getResult();
        /*
         * Continue fetching while we get the maximum (limit) and we haven't
         * reach the totalLimit
         */
        while (partialResult.getResult().size() == partialFetchAmount
            && result.size() < totalAmount) {
            Timber.d("Fetching workouts from %d onwards", result.size());
            params.clear();
            params.add(new Pair<>(OFFSET_PARAM_NAME, result.size()));
            params.add(limitParameter);
            partialResult = partialFetchWorkouts(session, url, params);
            result.addAll(partialResult.getResult());
        }
        if (result.size() > totalAmount) {
            Timber.w("Too many items. Requested %i, but got %i in %s", totalAmount, result.size(),
                url);
            return new FetchedResultList<>(result.subList(0, totalAmount),
                partialResult.getLastModified());
        } else {
            return new FetchedResultList<>(result, partialResult.getLastModified());
        }
    }

    public List<BackendUserWorkoutPair> findWorkouts(@Nullable UserSession session,
        double bottomLeftLatitude, double bottomLeftLongitude, double topRightLatitude,
        double topRightLongitude, int limit) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/workouts/public/within");
        Type wrapperType = new TypeToken<ResponseWrapper<List<BackendUserWorkoutPair>>>() {
        }.getType();
        Map<String, String> headers = session != null ? session.getAuthorizationHeaders() : null;
        List<Pair<?, ?>> params =
            buildQueryParams(bottomLeftLatitude, bottomLeftLongitude, topRightLatitude,
                topRightLongitude, limit);
        return fetchJsonWithOptionalHeaders(url, wrapperType, headers, params);
    }

    private static List<Pair<?, ?>> buildQueryParams(double bottomLeftLatitude,
        double bottomLeftLongitude, double topRightLatitude, double topRightLongitude, int limit) {
        List<Pair<?, ?>> params = new ArrayList<>(5);
        params.add(new Pair<>("lowerlat", Double.toString(bottomLeftLatitude)));
        params.add(new Pair<>("lowerlng", Double.toString(bottomLeftLongitude)));
        params.add(new Pair<>("upperlat", Double.toString(topRightLatitude)));
        params.add(new Pair<>("upperlng", Double.toString(topRightLongitude)));
        params.add(new Pair<>("limit", Integer.toString(limit)));
        return params;
    }

    /**
     * @throws BackendException
     */
    private FetchedResultList<BackendWorkout> partialFetchWorkouts(UserSession session, String url,
        List<Pair<?, ?>> params) throws BackendException {
        try {
            Type responseType = new TypeToken<ResponseWrapper<List<BackendWorkout>>>() {
            }.getType();
            Map<String, String> headers =
                session != null ? session.getAuthorizationHeaders() : null;
            ResponseWrapper<List<BackendWorkout>> response =
                networkProvider.getJson(url, headers, params, responseType);
            List<BackendWorkout> result = ResponseWrapper.checkResponse(response, url);
            long lastModified = response.getLastModified();
            return new FetchedResultList<>(result, lastModified);
        } catch (IOException | JsonParseException | HttpResponseException e) {
            String message = "Unable to fetch user workouts";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    public BackendWorkout fetchWorkoutHeader(UserSession session, String workoutKey)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl(
            String.format(Locale.US, "/workouts/%s/noextensions", workoutKey));
        Type wrapperType = new TypeToken<ResponseWrapper<BackendWorkout>>() {
        }.getType();
        Map<String, String> headers = session != null ? session.getAuthorizationHeaders() : null;
        return fetchJsonWithOptionalHeaders(url, wrapperType, headers, null);
    }

    public List<UserSearchResult> findUsers(UserSession session, String searchValue)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/search/" + searchValue);
        Type wrapperType = new TypeToken<ResponseWrapper<List<UserSearchResult>>>() {
        }.getType();
        return fetchJsonWithAuth(session, url, wrapperType, STTErrorCodes.UNABLE_TO_SEARCH);
    }

    public List<UserSearchResult> findPossibleFriends(UserSession session) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/friends/possible");
        Type wrapperType = new TypeToken<ResponseWrapper<List<UserSearchResult>>>() {
        }.getType();
        return fetchJsonWithAuth(session, url, wrapperType);
    }

    /**
     * Tries to fetch the given workout from the backend if it fails it deletes
     * the file since it's not valid
     *
     * @param workoutKey the key for the workout to fetch
     * @param file a new file where to store the binary data
     * @throws BackendException if something goes wrong while fetching the data
     */
    public void fetchWorkoutDataAndSave(UserSession session, String workoutKey, File file)
        throws BackendException {
        if (workoutKey == null || workoutKey.trim().length() == 0) {
            throw new IllegalArgumentException("Workout key can't be null or empty");
        }
        if (file == null) {
            throw new IllegalArgumentException("File can't be null");
        }
        String url = ANetworkProvider.buildSecureBackendUrl("/workouts/" + workoutKey + "/bin");
        try {
            Map<String, String> headers =
                session != null ? session.getAuthorizationHeaders() : null;
            networkProvider.getAndSaveFile(url, headers, file);
        } catch (IOException | HttpResponseException e) {
            deleteFile(file);
            throw new BackendException("Error while fetching and saving data", e);
        }
    }

    private void deleteFile(File file) {
        if (file.exists()) {
            file.delete();
        }
    }

    public List<WorkoutComment> fetchWorkoutComments(UserSession session, String workoutKey)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/workouts/comments/" + workoutKey);
        Map<String, String> headers = session != null ? session.getAuthorizationHeaders() : null;
        Type wrapperType = new TypeToken<ResponseWrapper<List<BackendWorkoutComment>>>() {
        }.getType();
        List<BackendWorkoutComment> backendWorkoutComments =
            fetchJsonWithOptionalHeaders(url, wrapperType, headers, null);
        List<WorkoutComment> result = new ArrayList<>(backendWorkoutComments.size());
        for (BackendWorkoutComment backendWorkoutComment : backendWorkoutComments) {
            result.add(backendWorkoutComment.getWorkoutComment(workoutKey));
        }
        return result;
    }

    /**
     * Sends a comment to the backend as plain text.
     *
     * @throws BackendException
     */
    public void pushNewWorkoutComment(UserSession session, WorkoutComment workoutComment)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl(
            "/workouts/comment/" + workoutComment.getWorkoutKey());
        try {
            Map<String, String> headers = session.getAuthorizationHeaders();
            try {
                String totp = generateOTPUseCase.generateTOTP();
                headers.put(BaseRemoteApi.HEADER_TOTP_KEY, totp);
            } catch (OTPGenerationException e) {
                Timber.w(e, "Error generating OTP token");
            }
            String content = networkProvider.post(url, headers, null,
                workoutComment.getMessage(), ANetworkProvider.MEDIA_TYPE_TEXT_PLAIN);
            Type wrapperType = new TypeToken<ResponseWrapper<BackendWorkout>>() {
            }.getType();
            ResponseWrapper<BackendWorkout> response = gson.fromJson(content, wrapperType);
            ResponseWrapper.checkResponse(response, url);
        } catch (JsonParseException | IOException | HttpResponseException e) {
            String message = "Unable to push workout comment";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    /**
     * @param session can be null
     * @throws BackendException
     */
    public List<ImageInformation> fetchWorkoutPictures(UserSession session, String workoutKey)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/images/workout/" + workoutKey);
        Type wrapperType = new TypeToken<ResponseWrapper<List<ImageInformation>>>() {
        }.getType();
        Map<String, String> headers = session != null ? session.getAuthorizationHeaders() : null;
        return fetchJsonWithOptionalHeaders(url, wrapperType, headers, null);
    }

    public boolean pushDeletedVideo(UserSession session, VideoInformation video)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/video/" + video.getKey());
        Map<String, String> headers = session.getAuthorizationHeaders();
        try {
            String responseStr = networkProvider.delete(url, headers);
            Type wrapperType = new TypeToken<ResponseWrapper<Boolean>>() {
            }.getType();
            ResponseWrapper<Boolean> acceptResponse = gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(acceptResponse, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            String message = "Unable to push deleted video";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    @WorkerThread
    @NonNull
    public List<ImageInformation> fetchUserPictures(final UserSession session, final String userName, final long since)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/images/" + userName,
            "since=" + since);
        Map<String, String> headers =
            session != null ? session.getAuthorizationHeaders() : null;
        Type wrapperType = new TypeToken<ResponseWrapper<List<ImageInformation>>>() {
        }.getType();
        return fetchJsonWithOptionalHeaders(url, wrapperType, headers, null);
    }

    public boolean pushDeletedImage(UserSession session, ImageInformation imageInformation)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/image/" + imageInformation.getKey());
        Map<String, String> headers = session.getAuthorizationHeaders();
        try {
            String responseStr = networkProvider.delete(url, headers);
            Type wrapperType = new TypeToken<ResponseWrapper<Boolean>>() {
            }.getType();
            ResponseWrapper<Boolean> acceptResponse = gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(acceptResponse, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            String message = "Unable to push deleted image";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    /**
     * Links user in session with FB
     *
     * @throws BackendException
     */
    public boolean linkWithFB(UserSession session, String accessToken) throws BackendException {
        return linkWithNetwork("facebook", session, accessToken, null);
    }

    private boolean linkWithNetwork(String network, UserSession session, String token,
        String token2) throws BackendException {
        StringBuilder query = new StringBuilder();
        query.append("token=");
        query.append(token);
        if (token2 != null && token2.trim().length() > 0) {
            query.append("&token2=");
            query.append(token2);
        }
        String url =
            ANetworkProvider.buildSecureBackendUrl("/user/link/" + network, query.toString());
        Type wrapperType = new TypeToken<ResponseWrapper<Map<String, String>>>() {
        }.getType();
        Map<String, String> response = fetchJsonWithAuth(session, url, wrapperType);
        boolean success = Boolean.valueOf(response.get("success"));
        if (!success) {
            Integer errorCode = null;
            try {
                errorCode = Integer.valueOf(response.get("errorCode"));
            } catch (Exception ignored) {
            }
            if (errorCode != null) {
                throw new BackendException(STTErrorCodes.valueOf(errorCode));
            }
        }
        return success;
    }

    public boolean pushUserProfileImage(
        UserSession session,
        String profileImageFilePath
    ) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/profileimage");
        Map<String, String> headers = session.getAuthorizationHeaders();
        try {
            String totp = generateOTPUseCase.generateTOTP();
            headers.put(BaseRemoteApi.HEADER_TOTP_KEY, totp);
        } catch (OTPGenerationException e) {
            Timber.w(e, "Error generating OTP token");
        }
        try {
            File file = new File(profileImageFilePath);
            String responseStr = networkProvider.putBinaryFile(url, headers, file);
            Type wrapperType = new TypeToken<ResponseWrapper<String>>() {
            }.getType();
            ResponseWrapper<String> response = gson.fromJson(responseStr, wrapperType);
            if (response.getError() != null) {
                Timber.e("upload profile image failed: %s", response.getError().getDescription());
                throw new BackendException(response.getError());
            }
            return response.getError() == null;
        } catch (HttpResponseException | JsonParseException | IOException e) {
            String message = "Unable to push user profile image";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    public boolean pushUserCoverImage(
        UserSession session,
        String coverImageFilePath
    ) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/coverimage");
        Map<String, String> headers = session.getAuthorizationHeaders();
        try {
            String totp = generateOTPUseCase.generateTOTP();
            headers.put(BaseRemoteApi.HEADER_TOTP_KEY, totp);
        } catch (OTPGenerationException e) {
            Timber.w(e, "Error generating OTP token");
        }
        try {
            File file = new File(coverImageFilePath);
            String responseStr = networkProvider.putBinaryFile(url, headers, file);
            Type wrapperType = new TypeToken<ResponseWrapper<String>>() {
            }.getType();
            ResponseWrapper<String> response = gson.fromJson(responseStr, wrapperType);
            if (response.getError() != null) {
                Timber.e("upload cover image failed: %s", response.getError().getDescription());
                throw new BackendException(response.getError());
            }
            return response.getError() == null;
        } catch (HttpResponseException | JsonParseException | IOException e) {
            String message = "Unable to push user cover image";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    public boolean redeemVoucher(String userName, String code) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/voucher/redeem");
        try {
            List<Pair<?, ?>> params = new ArrayList<>();
            params.add(new Pair<>("username", userName));
            params.add(new Pair<>("code", code));
            String responseStr = networkProvider.put(url, null, params);
            Timber.d("BackendController.redeemVoucher Response: %s", responseStr);
            Type wrapperType = new TypeToken<ResponseWrapper<Boolean>>() {
            }.getType();
            ResponseWrapper<Boolean> response = gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (IOException | JsonParseException | HttpResponseException e) {
            String message = "Unable to redeem voucher";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }

    public FacebookToken fetchFacebookToken(UserSession session) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/token/facebook");
        Type wrapperType = new TypeToken<ResponseWrapper<FacebookToken>>() {
        }.getType();
        return fetchJsonWithAuth(session, url, wrapperType, STTErrorCodes.FB_TOKEN_ERROR);
    }

    @NonNull
    public List<User> fetchFacebookFriends(UserSession session) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/friends/facebook");
        Type wrapperType = new TypeToken<ResponseWrapper<List<BackendUser.Builder>>>() {
        }.getType();
        List<BackendUser.Builder> backendUsers =
            fetchJsonWithAuth(session, url, wrapperType, STTErrorCodes.FB_ERROR);
        List<User> facebookFriends = new ArrayList<>(backendUsers.size());
        for (BackendUser.Builder backendUser : backendUsers) {
            facebookFriends.add(backendUser.build(session).getUser());
        }
        return facebookFriends;
    }

    public boolean validateFacebookTokenForFindingFriends(FacebookToken facebookToken)
        throws BackendException {
        try {
            if (facebookToken != null && !TextUtils.isEmpty(facebookToken.getToken())) {
                GraphResponse graphResponse = new GraphRequest(
                    AccessToken.getCurrentAccessToken(),
                    "/me/permissions",
                    null,
                    HttpMethod.GET,
                    null
                ).executeAndWait();
                PermissionResponse response = moshi.adapter(PermissionResponse.class)
                    .fromJson(graphResponse.getJSONObject().toString());
                if (response == null) {
                    throw new BackendException(STTErrorCodes.FB_TOKEN_ERROR);
                }
                for (PermissionStatus permission : response.getPermissions()) {
                    if (PermissionResponse.PERMISSION_FRIENDS.equals(permission.getPermission()) &&
                        PermissionStatus.STATUS_GRANTED.equals(permission.getStatus())) {
                        return true;
                    }
                }
            }
            throw new BackendException(STTErrorCodes.FB_TOKEN_ERROR);
        } catch (Exception e) {
            Timber.e(e, "Unable to validate Facebook token");
            throw new BackendException(STTErrorCodes.FB_TOKEN_ERROR, e);
        }
    }

    @NonNull
    @WorkerThread
    public List<WorkoutHeader> fetchWorkoutsOnSimilarRoute(
        @NonNull UserSession session,
        @NonNull WorkoutHeader referenceWorkout,
        @NonNull WorkoutHeaderController workoutHeaderController
    ) throws IOException, HttpResponseException, BackendException {
        if (TextUtils.isEmpty(referenceWorkout.getKey())) {
            return Collections.emptyList();
        }
        return fetchWorkoutHeaders(
            ANetworkProvider.buildSecureBackendUrl("/workouts/similarRoutes/" + referenceWorkout.getKey()),
            session.getAuthorizationHeaders(),
            workoutHeaderController
        );
    }

    @NonNull
    @WorkerThread
    private List<WorkoutHeader> fetchWorkoutHeaders(
        String url,
        Map<String, String> headers,
        WorkoutHeaderController workoutHeaderController
    ) throws IOException, HttpResponseException, BackendException {
        Type wrapperType = new TypeToken<ResponseWrapper<List<BackendWorkout>>>() {}.getType();
        ResponseWrapper<List<BackendWorkout>> response =
            networkProvider.getJson(url, headers, wrapperType);
        List<BackendWorkout> backendWorkouts = ResponseWrapper.checkResponse(response, url);
        List<WorkoutHeader> workoutHeaders = new ArrayList<>(backendWorkouts.size());
        for (BackendWorkout backendWorkout : backendWorkouts) {
            // just in case, we had similar errors before...
            if (backendWorkout != null) {
                workoutHeaders.add(backendWorkout.getWorkoutHeader(workoutHeaderController));
            }
        }
        return workoutHeaders;
    }

    public List<Reaction> fetchWorkoutReactions(UserSession session, String workoutKey)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/workouts/reactions/" + workoutKey);
        Map<String, String> headers = session != null ? session.getAuthorizationHeaders() : null;
        Type wrapperType = new TypeToken<ResponseWrapper<List<BackendReaction>>>() {
        }.getType();
        List<BackendReaction> backendReactions =
            fetchJsonWithOptionalHeaders(url, wrapperType, headers, null);
        List<Reaction> result = new ArrayList<>(backendReactions.size());
        for (BackendReaction backendReaction : backendReactions) {
            result.add(backendReaction.toReaction(workoutKey));
        }
        return result;
    }

    // Follow model

    public BackendFollowStatusChange followUser(UserSession session, String targetUsername)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/follow/" + targetUsername);
        return followUser(url, session);
    }

    public BackendFollowStatusChange followUserV2(UserSession session, String targetUsername)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/follow/" + targetUsername, null, 2);
        return followUser(url, session);
    }

    private BackendFollowStatusChange followUser(String url, UserSession session)
        throws BackendException {
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendFollowStatusChange>>() {
            }.getType();
            Map<String, String> headers = session.getAuthorizationHeaders();
            try {
                String totp = generateOTPUseCase.generateTOTP();
                headers.put(BaseRemoteApi.HEADER_TOTP_KEY, totp);
            } catch (OTPGenerationException e) {
                Timber.w(e, "Error generating OTP token");
            }
            String responseStr =
                networkProvider.post(url, headers, null, null,
                    ANetworkProvider.MEDIA_TYPE_JSON);
            ResponseWrapper<BackendFollowStatusChange> response =
                gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            throw new BackendException("Unable to add user to follow", e);
        }
    }

    public BackendFollowStatusChange acceptFollower(UserSession session, String targetUsername)
        throws BackendException {
        String url =
            ANetworkProvider.buildSecureBackendUrl("/user/follow/accept/" + targetUsername);
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendFollowStatusChange>>() {
            }.getType();
            String responseStr =
                networkProvider.putJson(url, session.getAuthorizationHeaders(), null);
            ResponseWrapper<BackendFollowStatusChange> response =
                gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            throw new BackendException("Unable to accept follower", e);
        }
    }

    public BackendFollowStatusChange rejectFollower(UserSession session, String targetUsername)
        throws BackendException {
        String url =
            ANetworkProvider.buildSecureBackendUrl("/user/follow/reject/" + targetUsername);
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendFollowStatusChange>>() {
            }.getType();
            String responseStr =
                networkProvider.putJson(url, session.getAuthorizationHeaders(), null);
            ResponseWrapper<BackendFollowStatusChange> response =
                gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            throw new BackendException("Unable to reject follower", e);
        }
    }

    public BackendFollowStatusChange unFollow(UserSession session, String targetUsername)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/unfollow/" + targetUsername);
        return unfollow(url, session, targetUsername);
    }

    public BackendFollowStatusChange revokeFollowRequest(UserSession session, String targetUsername)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/unfollow/" + targetUsername, "followCancel=true");
        return unfollow(url, session, targetUsername);
    }

    private BackendFollowStatusChange unfollow(String url, UserSession session, String targetUsername)
        throws BackendException {
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendFollowStatusChange>>() {
            }.getType();
            Map<String, String> headers = session.getAuthorizationHeaders();
            try {
                String totp = generateOTPUseCase.generateTOTP();
                headers.put(BaseRemoteApi.HEADER_TOTP_KEY, totp);
            } catch (OTPGenerationException e) {
                Timber.w(e, "Error generating OTP token");
            }
            String responseStr =
                networkProvider.post(url, headers, null, null,
                    ANetworkProvider.MEDIA_TYPE_JSON);
            ResponseWrapper<BackendFollowStatusChange> response =
                gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            throw new BackendException("Unable to unfollow", e);
        }
    }

    public BackendFollowStatusChange revokeFollower(UserSession session, String targetUsername)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/revoke/" + targetUsername);
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendFollowStatusChange>>() {
            }.getType();
            String responseStr =
                networkProvider.post(url, session.getAuthorizationHeaders(), null, null,
                    ANetworkProvider.MEDIA_TYPE_JSON);
            ResponseWrapper<BackendFollowStatusChange> response =
                gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            throw new BackendException("Unable to revoke follower", e);
        }
    }

    @NonNull
    public BackendFollowLists getFollowersAndFollowings(UserSession session)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/follow", "hasFriends=true");
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendFollowLists>>() {
            }.getType();
            return fetchJsonWithAuth(session, url, wrapperType);
        } catch (Exception e) {
            throw new BackendException("Unable to fetch following statuses", e);
        }
    }

    public List<BackendFollowStatusChange> followListOfUsers(UserSession session,
        String[] usernames) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/followmultiple");
        return followListOfUsers(url, session, usernames);
    }

    public List<BackendFollowStatusChange> followListOfUsersV2(UserSession session,
        String[] usernames) throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/followmultiple?hasFriends=true");
        return followListOfUsers(url, session, usernames);
    }

    private List<BackendFollowStatusChange> followListOfUsers(String url, UserSession session,
        String[] usernames) throws BackendException {
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<List<BackendFollowStatusChange>>>() {
            }.getType();
            Map<String, String> headers = session.getAuthorizationHeaders();
            String responseStr = networkProvider.postJson(url, headers, usernames);
            ResponseWrapper<List<BackendFollowStatusChange>> response =
                gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            throw new BackendException("Unable to add list of users to follow", e);
        }
    }

    public BackendUser migrateUserToFollowModel(UserSession session, User user)
        throws BackendException {
        String url = ANetworkProvider.buildSecureBackendUrl("/user/migratefollowmodel");
        try {
            Type wrapperType = new TypeToken<ResponseWrapper<BackendUser>>() {
            }.getType();
            String responseStr =
                networkProvider.putJson(url, session.getAuthorizationHeaders(), user);
            ResponseWrapper<BackendUser> response = gson.fromJson(responseStr, wrapperType);
            return ResponseWrapper.checkResponse(response, url);
        } catch (HttpResponseException | JsonParseException | IOException e) {
            String message = "Unable to migrate user:" + user.getUsername() + " to follow model";
            Timber.e(e, message);
            throw new BackendException(message, e);
        }
    }
}
