package com.stt.android.controllers

import android.content.Context
import com.emarsys.Emarsys.messageInbox
import com.emarsys.core.api.result.CompletionListener
import com.emarsys.mobileengage.api.action.AppEventActionModel
import com.emarsys.mobileengage.api.action.CustomEventActionModel
import com.emarsys.mobileengage.api.action.DismissActionModel
import com.emarsys.mobileengage.api.action.OpenExternalUrlActionModel
import com.emarsys.mobileengage.api.inbox.Message
import com.stt.android.R
import com.stt.android.domain.NotificationState
import com.stt.android.domain.notifications.GetNotificationsCountUseCase
import com.stt.android.domain.notifications.GetUnreadNotificationsCountUseCase
import com.stt.android.social.notifications.list.EmarsysAction
import com.stt.android.social.notifications.list.EmarsysActionType
import com.stt.android.social.notifications.list.EmarsysInboxItem
import com.stt.android.social.notifications.list.EmarsysInboxSource
import com.stt.android.social.notifications.list.EmarsysInboxType
import com.stt.android.social.notifications.list.InboxMessageTag
import com.stt.android.social.notifications.list.clickDeleteMessage
import com.stt.android.social.notifications.list.getType
import com.stt.android.social.notifications.list.isPastCustomExpiryDate
import com.stt.android.social.notifications.list.testMessage
import com.stt.android.ui.utils.TextFormatter
import com.stt.android.utils.HuaweiUtils
import com.stt.android.utils.STTConstants
import com.stt.android.utils.toV1
import io.reactivex.BackpressureStrategy
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.timeout
import kotlinx.coroutines.launch
import kotlinx.coroutines.rx2.asObservable
import rx.Observable
import rx.subjects.BehaviorSubject
import rx.subjects.SerializedSubject
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.time.Duration.Companion.minutes

abstract class BaseFeedController protected constructor(
    private val context: Context,
    private val getNotificationsCountUseCase: GetNotificationsCountUseCase,
    private val getUnreadNotificationsCountUseCase: GetUnreadNotificationsCountUseCase,
    private val huaweiUtils: HuaweiUtils,
) {
    private val coroutineScope: CoroutineScope = CoroutineScope(SupervisorJob())

    /**
     * Used to notify about changes in the feed
     * EMARSYS_MESSAGE_UPDATE: update emarsys inbox message
     * OTHER_MESSAGE_UPDATE: update other message
     * use BehaviorSubject to ensure that the subscriber receives the last data before subscribing
     */
    val feedUpdatedSubject: SerializedSubject<Int?, Int?> =
        BehaviorSubject.create<Int?>().toSerialized()

    private val _feedUpdated: MutableSharedFlow<Int> = MutableSharedFlow()
    val feedUpdated: SharedFlow<Int> = _feedUpdated.asSharedFlow()

    @OptIn(FlowPreview::class)
    open val emarsysInboxItems: Flow<List<EmarsysInboxItem>>
        get() = flow {
            emit(fetchEmarsysMessages())
        }
            .timeout<List<EmarsysInboxItem>>(1.minutes)
            .catch { emit(emptyList()) }

    private suspend fun fetchEmarsysMessages(): List<EmarsysInboxItem> = suspendCoroutine { cont ->
        messageInbox.fetchMessages { inboxResult ->
            val emarsysInboxItems = inboxResult.result
                ?.messages
                ?.takeUnless(List<*>::isEmpty)
                ?.let(::handleEmarsysInboxItem)
                ?: emptyList<EmarsysInboxItem>()
            cont.resume(emarsysInboxItems)
        }
    }

    private fun handleEmarsysInboxItem(messages: List<Message>): List<EmarsysInboxItem> =
        messages.mapNotNull { message ->
            // delete test message after five minutes
            if ((message.testMessage() && exceedFiveMinutes(message.receivedAt)) || message.isPastCustomExpiryDate()) {
                markMessageDeleted(message.id)
                return@mapNotNull null
            }
            EmarsysInboxItem(
                id = message.id,
                title = message.title,
                body = message.body,
                imageUrl = message.imageUrl,
                actions = getEmarsysActions(message),
                tags = message.tags,
                messageType = message.getType(),
                receivedAtTime = message.receivedAt,
                clickDelete = message.clickDeleteMessage(),
                formattedDateTime = formatReceivedDateTime(TimeUnit.SECONDS.toMillis(message.receivedAt)),
                source = EmarsysInboxSource.EMARSYS,
            )
        }

    protected fun formatReceivedDateTime(millis: Long): String {
        val date = LocalDate.ofInstant(Instant.ofEpochMilli(millis), ZoneId.systemDefault())
        val today = LocalDate.now()
        return when (date) {
            today -> TextFormatter.formatRelativeDateSpan(context.resources, millis).toString()
            today.minusDays(1L) -> context.getString(R.string.yesterday)
            else -> DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT).format(date)
        }
    }

    private fun markMessageDeleted(messageId: String) {
        messageInbox.addTag(
            InboxMessageTag.DELETED.tag,
            messageId,
            CompletionListener { throwable: Throwable? ->
                if (throwable != null) {
                    Timber.w(throwable, "set tag deleted error:\$messageId")
                }
            }
        )
    }

    private fun exceedFiveMinutes(receiveTime: Long): Boolean {
        return OffsetDateTime.now().toEpochSecond() - receiveTime > 5 * 60
    }

    private fun getEmarsysActions(message: Message): List<EmarsysAction> =
        message.actions?.mapNotNull { actionModel ->
            when (actionModel) {
                is CustomEventActionModel,
                is DismissActionModel -> null

                is AppEventActionModel -> {
                    if (DEEP_LINK_EVENT_NAME == actionModel.name) {
                        getDeepLinkAction(actionModel)
                    } else {
                        // other application event
                        getOtherApplicationEventAction(actionModel)
                    }
                }

                is OpenExternalUrlActionModel -> {
                    val type = when (message.getType()) {
                        EmarsysInboxType.ANNUAL_REPORT -> {
                            EmarsysActionType.OPEN_ANNUAL_REPORT_URL
                        }

                        EmarsysInboxType.H5 -> EmarsysActionType.H5
                        else -> EmarsysActionType.OPEN_EXTERNAL_URL
                    }
                    EmarsysAction(
                        id = actionModel.id,
                        title = actionModel.title,
                        type = type,
                        url = actionModel.url.toString(),
                    )
                }
            }
        } ?: emptyList()

    private fun getDeepLinkAction(actionModel: AppEventActionModel): EmarsysAction? {
        val payload = actionModel.payload
            ?.takeUnless(Map<*, *>::isEmpty)
            ?: return null
        val url = payload[DEEP_LINK_PAYLOAD_URL] ?: return null
        return EmarsysAction(
            id = actionModel.id,
            title = actionModel.title,
            type = EmarsysActionType.DEEP_LINK,
            url = url.toString(),
        )
    }

    private fun getOtherApplicationEventAction(actionModel: AppEventActionModel): EmarsysAction? {
        val payload = actionModel.payload
            ?.takeUnless(Map<*, *>::isEmpty)
            ?: return null
        payload[STTConstants.EmarsysConstant.APPLICATION_DEEPLINK_URL_NAME]
            ?.let { deeplinkUrl ->
                return EmarsysAction(
                    id = actionModel.id,
                    title = actionModel.title,
                    type = EmarsysActionType.DEEP_LINK,
                    url = deeplinkUrl.toString(),
                    eventName = payload[STTConstants.EmarsysConstant.APPLICATION_CUSTOM_EVENT_NAME]?.toString(),
                )
            }
        payload[STTConstants.EmarsysConstant.APPLICATION_EXTERNAL_URL_NAME]
            ?.let { externalUrl ->
                return EmarsysAction(
                    id = actionModel.id,
                    title = actionModel.title,
                    type = EmarsysActionType.OPEN_EXTERNAL_URL,
                    url = externalUrl.toString(),
                    eventName = payload[STTConstants.EmarsysConstant.APPLICATION_CUSTOM_EVENT_NAME]?.toString(),
                )
            }
        return null
    }

    val notificationState: Flow<NotificationState>
        get() = combine(
            getNotificationsCountUseCase(),
            getUnreadNotificationsCountUseCase(),
            emarsysInboxItems,
        ) { notificationsCount, unreadNotificationsCount, emarsysInboxItems ->
            val readCount = emarsysInboxItems.count { item ->
                item.tags?.contains(InboxMessageTag.SEEN.tag) == true
            }
            val feedUnreadCount = emarsysInboxItems.size - readCount
            NotificationState(
                notificationsCount == 0,
                emarsysInboxItems.isEmpty(),
                unreadNotificationsCount + feedUnreadCount
            )
        }.onEach {
            Timber.d("BadgeCount: ${it.unreadItemCount}")
            huaweiUtils.setIconBadge(it.unreadItemCount)
        }

    // TODO Remove me after BaseDashboardToolbarPresenter is converted to Kotlin or deleted.
    val notificationStateRx: Observable<NotificationState>
        get() = notificationState.asObservable().toV1(BackpressureStrategy.LATEST)

    val feedUpdatedObservable: Observable<Int?>
        /**
         * Subscribe to this observable to receive notifications about feed changes
         */
        get() = feedUpdatedSubject.asObservable()

    fun feedEmarsysUpdate() {
        feedUpdatedSubject.onNext(EMARSYS_MESSAGE_UPDATE)
        coroutineScope.launch { _feedUpdated.emit(EMARSYS_MESSAGE_UPDATE) }
    }

    fun feedOtherUpdate() {
        feedUpdatedSubject.onNext(OTHER_MESSAGE_UPDATE)
        coroutineScope.launch { _feedUpdated.emit(OTHER_MESSAGE_UPDATE) }
    }

    companion object {
        const val EMARSYS_MESSAGE_UPDATE: Int = 1
        const val OTHER_MESSAGE_UPDATE: Int = 0

        private const val DEEP_LINK_EVENT_NAME = "DeepLink"
        private const val DEEP_LINK_PAYLOAD_URL = "url"
    }
}
