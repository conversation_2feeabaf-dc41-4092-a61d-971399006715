package com.stt.android.controllers

import com.stt.android.domain.user.BackendWorkout
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.VideoInformation
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.utils.toV1
import com.stt.android.utils.toV2
import io.reactivex.BackpressureStrategy
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.rx2.asFlow
import kotlinx.coroutines.rx2.asObservable
import kotlinx.coroutines.withContext
import rx.Observable
import timber.log.Timber
import kotlin.time.Duration.Companion.milliseconds

@OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
val WorkoutHeaderController.currentUserWorkoutUpdated: Flow<Unit> get() = currentUserWorkoutUpdatesAsObservable
    .toV2()
    .asFlow()
    .debounce(100.milliseconds)
    .mapLatest { }

@OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
val WorkoutHeaderController.workoutUpdated: Flow<Unit> get() = workoutUpdatesAsObservable
    .toV2()
    .asFlow()
    .debounce(100.milliseconds)
    .mapLatest { }

@OptIn(FlowPreview::class, ExperimentalCoroutinesApi::class)
val WorkoutHeaderController.othersWorkoutUpdated: Flow<WorkoutHeader> get() = othersWorkoutUpdatesAsObservable
    .toV2()
    .asFlow()
    .debounce(100.milliseconds)

fun WorkoutHeaderController.findOrFetchBlocking(workoutKey: String): WorkoutHeader? =
    runBlocking { findOrFetch(workoutKey) }

suspend fun WorkoutHeaderController.findOrFetch(workoutKey: String): WorkoutHeader? = withContext(Dispatchers.IO) {
    val local = find(workoutKey)
    if (local != null) {
        return@withContext local
    }

    val session = currentUserController.session ?: return@withContext null
    val fetched = backendController.fetchWorkoutHeader(session, workoutKey) ?: return@withContext null
    val fetchedWorkoutHeader = fetched.getWorkoutHeader(this@findOrFetch)

    val shouldStore = currentUserController.username == fetchedWorkoutHeader.username ||
        isFolloweeUseCase.isFollowee(fetchedWorkoutHeader.username)
    if (shouldStore) {
        store(fetchedWorkoutHeader)
        picturesController.store(fetched.images)
        videoModel.storeMetaData(fetched.videos)
    }

    fetchedWorkoutHeader
}

fun WorkoutHeaderController.loadWorkoutsRx(username: String): Observable<List<WorkoutHeader>> =
    loadWorkouts(username).asObservable().toV1(BackpressureStrategy.LATEST)

fun WorkoutHeaderController.loadWorkouts(username: String): Flow<List<WorkoutHeader>> = flow {
    val isCurrentUser = currentUserController.username == username
    val local = findAllWhereOwner(username, false)
    if (isCurrentUser || local.isNotEmpty()) {
        emit(local.sortedByDescending(WorkoutHeader::startTime))
    }
    if (isCurrentUser) {
        // For current user, all workouts are locally available, so no need to fetch from server.
        return@flow
    }

    val session = currentUserController.session ?: return@flow
    val fetchedWorkouts = backendController.fetchWorkouts(session, username)
        .getResult()
    val remoteToLocal = fetchedWorkouts
        .map { workout -> workout to workout.getWorkoutHeader(this@loadWorkouts) }
    val fetchedWorkoutHeaders = remoteToLocal.map { it.second }
    val workoutHeadersToEmit = merge(local, fetchedWorkoutHeaders)
        .sortedByDescending(WorkoutHeader::startTime)
    emit(workoutHeadersToEmit)

    val isFollowee = isFolloweeUseCase.isFollowee(username)
    if (!isFollowee) {
        return@flow
    }
    // Store workoutId for picture & video
    remoteToLocal.forEach { (remote, local) ->
        picturesController.store(remote.images.map {
            ImageInformation.fromPicture(it.toPicture().copy(workoutId = local.id))
        })
        videoModel.storeMetaData(remote.videos.map {
            VideoInformation.fromVideo(it.toVideo().copy(workoutId = local.id))
        })
    }
    if (workoutHeadersToEmit.none() && local.any()) {
        remove(local)
    }
    store(fetchedWorkoutHeaders)
}.catch { e ->
    Timber.w(e, "Error while loading workouts")
}.flowOn(Dispatchers.IO)

private fun merge(
    workoutHeaders1: List<WorkoutHeader>,
    workoutHeaders2: List<WorkoutHeader>,
): Collection<WorkoutHeader> {
    if (workoutHeaders1.isEmpty()) {
        return workoutHeaders2
    }
    if (workoutHeaders2.isEmpty()) {
        return emptyList()
    }

    val workoutHeadersByKey = workoutHeaders1.associateBy(WorkoutHeader::key)
        .toMutableMap()
    workoutHeaders2.associateByTo(workoutHeadersByKey, WorkoutHeader::key)
    return workoutHeadersByKey.values
}

fun WorkoutHeaderController.loadRemoteWorkouts(username: String): Flow<List<Pair<BackendWorkout, WorkoutHeader>>> = flow {
    val session = currentUserController.session ?: run {
        emit(emptyList())
        return@flow
    }
    val fetchedWorkouts = backendController.fetchWorkouts(session, username).getResult()
    val remoteToLocal = fetchedWorkouts
        .map { workout -> workout to workout.getWorkoutHeader(this@loadRemoteWorkouts) }
    emit(remoteToLocal.sortedByDescending { it.second.startTime })
}.catch { e ->
    Timber.w(e, "Error while loading workouts")
    emit(emptyList())
}.flowOn(Dispatchers.IO)
