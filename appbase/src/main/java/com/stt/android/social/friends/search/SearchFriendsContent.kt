package com.stt.android.social.friends.search

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.M3SearchBar
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.composables.FriendsListView

@Composable
fun SearchFriendsContent(
    state: SearchFriendsState,
    onBackClick: () -> Unit,
    onQueryChange: (String) -> Unit,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier) {
        M3SearchBar(
            query = state.keyword,
            onQueryChange = onQueryChange,
            placeholder = stringResource(R.string.search_friends_hint),
            onCancel = onBackClick,
            cancelText = stringResource(R.string.cancel),
            modifier = Modifier.background(MaterialTheme.colorScheme.surface),
        )

        if (state.keyword.trim().length in 1 until MIN_SEARCH_FRIENDS_KEYWORD_LENGTH) {
            Text(
                text = stringResource(R.string.minimum_2_characters),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.error,
                modifier = Modifier.padding(
                    horizontal = MaterialTheme.spacing.large,
                    vertical = MaterialTheme.spacing.xsmall,
                ),
            )
        }

        Box(
            modifier = Modifier.fillMaxSize().narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background,
            ),
        ) {
            if (state.searching) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = MaterialTheme.spacing.large),
                    contentAlignment = Alignment.TopCenter,
                ) {
                    CircularProgressIndicator()
                }
            } else {
                FriendsListView(
                    friends = state.searchedFriends,
                    onFriendClick = onFriendClick,
                    onStatusClick = onStatusClick,
                    emptyView = {
                        if (state.keyword.isNotBlank()) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 200.dp),
                                contentAlignment = Alignment.TopCenter,
                            ) {
                                Text(
                                    text = stringResource(R.string.search_phone_contacts_empty),
                                    style = MaterialTheme.typography.bodyLarge,
                                    color = MaterialTheme.colorScheme.secondary,
                                )
                            }
                        }
                    },
                )
            }
        }
    }
}
