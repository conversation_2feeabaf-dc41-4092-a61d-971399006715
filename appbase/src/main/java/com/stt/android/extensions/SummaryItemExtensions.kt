package com.stt.android.extensions

import android.content.Context
import androidx.annotation.DrawableRes
import com.stt.android.R
import com.stt.android.core.domain.LapsTableDataType
import com.stt.android.infomodel.SummaryItem
import com.stt.android.core.R as CR

@DrawableRes
fun LapsTableDataType.icon(isDownhill: Boolean): Int {
    // Downhill laps have some special cases for icons
    if (isDownhill) {
        when (this) {
            LapsTableDataType.Summary(SummaryItem.DURATION) -> return R.drawable.ic_summary_item_duration_downhill
            LapsTableDataType.Summary(SummaryItem.DISTANCE) -> return R.drawable.ic_summary_item_distance_downhill
            LapsTableDataType.Summary(SummaryItem.DESCENTALTITUDE) -> return R.drawable.ic_summary_item_descent_downhill
            LapsTableDataType.Summary(SummaryItem.AVGSPEED) -> return R.drawable.ic_summary_item_speed_downhill
            LapsTableDataType.Summary(SummaryItem.MAXSPEED) -> return R.drawable.ic_summary_item_speed_max_downhill
            else -> { // fall through
            }
        }
    }

    // Not downhill or not a special case
    return when (this) {
        is LapsTableDataType.Summary -> when (summaryItem) {
            SummaryItem.DURATION -> R.drawable.ic_summary_item_duration
            SummaryItem.TOTALTIME -> 0
            SummaryItem.PAUSETIME -> 0
            SummaryItem.MOVINGTIME -> 0
            SummaryItem.RESTTIME -> 0
            SummaryItem.DISTANCE -> R.drawable.ic_summary_item_distance
            SummaryItem.AVGPACE -> R.drawable.ic_summary_item_pace
            SummaryItem.MOVINGPACE -> 0
            SummaryItem.AVGHEARTRATE -> R.drawable.ic_summary_item_heart_rate
            SummaryItem.MAXHEARTRATE -> R.drawable.ic_summary_item_heart_rate_max
            SummaryItem.MINHEARTRATE -> R.drawable.ic_summary_item_heart_rate_min
            SummaryItem.ENERGY -> R.drawable.ic_summary_item_calories
            SummaryItem.RECOVERYTIME -> 0
            SummaryItem.PTE -> 0
            SummaryItem.PERFORMANCELEVEL -> 0
            SummaryItem.AVGSPEED -> R.drawable.ic_summary_item_pace
            SummaryItem.MOVINGSPEED -> 0
            SummaryItem.AVGVERTICALSPEED -> R.drawable.ic_summary_item_vertical_speed
            SummaryItem.AVGCADENCE -> R.drawable.ic_summary_item_cadence
            SummaryItem.STEPS -> 0
            SummaryItem.AVGSTEPCADENCE -> 0
            SummaryItem.MAXSTEPCADENCE -> 0
            SummaryItem.AVGSTRIDELENGTH -> 0
            SummaryItem.ASCENTALTITUDE -> R.drawable.ic_summary_item_ascent
            SummaryItem.DESCENTALTITUDE -> R.drawable.ic_summary_item_descent
            SummaryItem.HIGHALTITUDE -> R.drawable.ic_summary_item_altitude_max
            SummaryItem.LOWALTITUDE -> R.drawable.ic_summary_item_altitude_min
            SummaryItem.PEAKVERTICALSPEED30S -> 0
            SummaryItem.PEAKVERTICALSPEED1M -> 0
            SummaryItem.PEAKVERTICALSPEED3M -> 0
            SummaryItem.PEAKVERTICALSPEED5M -> 0
            SummaryItem.AVGTEMPERATURE -> R.drawable.ic_summary_item_temperature
            SummaryItem.MAXTEMPERATURE -> R.drawable.ic_summary_item_temperature_max
            SummaryItem.NAUTICALDISTANCE -> R.drawable.ic_summary_item_distance
            SummaryItem.PEAKEPOC -> 0
            SummaryItem.FEELING -> 0
            SummaryItem.MOVETYPE -> 0
            SummaryItem.CATCHFISH -> 0
            SummaryItem.CATCHBIGGAME -> 0
            SummaryItem.CATCHSMALLGAME -> 0
            SummaryItem.CATCHBIRD -> 0
            SummaryItem.CATCHSHOTCOUNT -> 0
            SummaryItem.AVGPOWER -> R.drawable.ic_summary_item_power
            SummaryItem.MAXPOWER -> R.drawable.ic_summary_item_power_max
            SummaryItem.AVGPOWERWITHZERO -> 0
            SummaryItem.PEAKPOWER30S -> 0
            SummaryItem.PEAKPOWER1M -> 0
            SummaryItem.PEAKPOWER3M -> 0
            SummaryItem.PEAKPOWER5M -> 0
            SummaryItem.AVGSWOLF -> R.drawable.ic_summary_item_swolf
            SummaryItem.AVGSWIMSTROKERATE -> R.drawable.ic_summary_item_stroke_rate
            SummaryItem.AVGNAUTICALSPEED -> R.drawable.ic_summary_item_pace
            SummaryItem.MAXNAUTICALSPEED -> R.drawable.ic_summary_item_pace_max
            SummaryItem.AVGSEALEVELPRESSURE -> R.drawable.ic_summary_item_sea_level_pressure
            SummaryItem.MAXPACE -> R.drawable.ic_summary_item_pace_max
            SummaryItem.PEAKPACE30S -> 0
            SummaryItem.PEAKPACE1M -> 0
            SummaryItem.PEAKPACE3M -> 0
            SummaryItem.PEAKPACE5M -> 0
            SummaryItem.PEAKSPEED30S -> 0
            SummaryItem.PEAKSPEED1M -> 0
            SummaryItem.PEAKSPEED3M -> 0
            SummaryItem.PEAKSPEED5M -> 0
            SummaryItem.MAXSPEED -> R.drawable.ic_summary_item_pace_max
            SummaryItem.MAXDEPTH -> R.drawable.ic_summary_item_depth_max
            SummaryItem.DIVETIME -> R.drawable.ic_summary_item_dive_time
            SummaryItem.DIVETIMEMAX -> 0
            SummaryItem.DIVEMODE -> 0
            SummaryItem.DIVENUMBERINSERIES -> 0
            SummaryItem.DIVESURFACETIME -> R.drawable.ic_summary_item_dive_recovery_time
            SummaryItem.DIVERECOVERYTIME -> R.drawable.ic_summary_item_dive_recovery_time
            SummaryItem.DIVEVISIBILITY -> 0
            SummaryItem.DIVEMAXDEPTHTEMPERATURE -> 0
            SummaryItem.SKIRUNCOUNT -> 0
            SummaryItem.SKIDISTANCE -> R.drawable.ic_summary_item_distance
            SummaryItem.SKITIME -> R.drawable.ic_summary_item_duration
            SummaryItem.AVGSKISPEED -> R.drawable.ic_summary_item_pace
            SummaryItem.MAXSKISPEED -> R.drawable.ic_summary_item_pace_max
            SummaryItem.ASCENTTIME -> R.drawable.ic_summary_item_duration
            SummaryItem.DESCENTTIME -> R.drawable.ic_summary_item_duration
            SummaryItem.ESTVO2PEAK -> 0
            SummaryItem.ALGORITHMLOCK -> 0
            SummaryItem.DIVECNS -> 0
            SummaryItem.DIVEOTU -> 0
            SummaryItem.AVGDEPTH -> 0
            SummaryItem.DIVEGASES -> 0
            SummaryItem.PERSONAL -> 0
            SummaryItem.GRADIENTFACTORS -> 0
            SummaryItem.ALTITUDESETTING -> 0
            SummaryItem.GASCONSUMPTION -> 0
            SummaryItem.ALGORITHM -> 0
            SummaryItem.AVGSWIMPACE -> R.drawable.ic_summary_item_pace
            SummaryItem.SWIMSTROKECOUNT -> 0
            SummaryItem.SWIMSTROKEDISTANCE -> 0
            SummaryItem.CUMULATEDDISTANCE -> R.drawable.ic_summary_item_distance
            SummaryItem.CUMULATEDDURATION -> R.drawable.ic_summary_item_duration
            SummaryItem.SWIMDISTANCE -> R.drawable.ic_summary_item_distance
            SummaryItem.CUMULATEDSWIMDISTANCE -> R.drawable.ic_summary_item_distance
            SummaryItem.SWIMSTYLE -> R.drawable.ic_summary_item_swim_style
            SummaryItem.TYPE -> R.drawable.ic_summary_item_interval_type
            SummaryItem.NONE -> 0
            SummaryItem.DIVEDISTANCE -> 0
            SummaryItem.DIVEGASPRESSURE -> 0
            SummaryItem.DIVEGASENDPRESSURE -> 0
            SummaryItem.DIVEGASUSEDPRESSURE -> 0
            SummaryItem.TRAININGSTRESSSCORE -> 0 // todo add proper resource
            SummaryItem.NORMALIZEDPOWER -> 0
            SummaryItem.NORMALIZEDGRADEDPACE -> 0
            SummaryItem.CO2EMISSIONSREDUCED -> 0
            SummaryItem.MINDEPTH -> 0
            SummaryItem.DIVEINWORKOUT -> 0
            SummaryItem.DOWNHILLDISTANCE -> 0
            SummaryItem.DOWNHILLDURATION -> 0
            SummaryItem.DOWNHILLCOUNT -> 0
            SummaryItem.DOWNHILLDESCENT -> 0
            SummaryItem.AVGDOWNHILLSPEED -> 0
            SummaryItem.MAXDOWNHILLSPEED -> 0
            SummaryItem.AVGDOWNHILLGRADE -> 0
            SummaryItem.MAXDOWNHILLGRADE -> 0
            SummaryItem.DOWNHILLMAXDESCENT -> 0
            SummaryItem.DOWNHILLMAXLENGTH -> 0
            SummaryItem.REVOLUTIONCOUNT,
            SummaryItem.ROWINGSTROKECOUNT,
            SummaryItem.SKIPCOUNT -> R.drawable.ic_summary_item_repetitions

            SummaryItem.MAXCADENCE -> 0
            SummaryItem.HRAEROBICTHRESHOLD -> 0
            SummaryItem.HRANAEROBICTHRESHOLD -> 0
            SummaryItem.AEROBICPACETHRESHOLD -> 0
            SummaryItem.ANAEROBICPACETHRESHOLD -> 0
            SummaryItem.ANAEROBICPOWERTHRESHOLD -> 0
            SummaryItem.AEROBICPOWERTHRESHOLD -> 0
            SummaryItem.AEROBICDURATION -> 0
            SummaryItem.ANAEROBICDURATION -> 0
            SummaryItem.VO2MAXDURATION -> 0
            SummaryItem.BREATHINGRATE -> R.drawable.icon_breathing_rate
            SummaryItem.BREASTSTROKEGLIDETIME -> R.drawable.icon_breaststorke_glide_time
            SummaryItem.BREASTSTROKEHEADANGLE,
            SummaryItem.FREESTYLEPITCHANGLE -> R.drawable.icon_swimming_head_angle

            SummaryItem.AVGBREASTSTROKEBREATHANGLE -> R.drawable.icon_avg_breaststroke_breath_angle
            SummaryItem.AVGFREESTYLEBREATHANGLE -> R.drawable.icon_avg_freestyle_breath_angle
            SummaryItem.FREESTYLEDURATION,
            SummaryItem.BREASTSTROKEDURATION,
            SummaryItem.FREESTYLEPERCENT,
            SummaryItem.BREASTSTROKEPERCENT,
            SummaryItem.MAXFREESTYLEBREATHANGLE,
            SummaryItem.MAXBREASTSTROKEBREATHANGLE,
            SummaryItem.FATCONSUMPTION,
            SummaryItem.CARBOHYDRATECONSUMPTION,
            SummaryItem.AVGGROUNDCONTACTTIME,
            SummaryItem.AVGVERTICALOSCILLATION,
            SummaryItem.AVGGROUNDCONTACTBALANCE,
            SummaryItem.CLIMBS,
            SummaryItem.CLIMBSCATEGORY1,
            SummaryItem.CLIMBSCATEGORY2,
            SummaryItem.CLIMBSCATEGORY3,
            SummaryItem.CLIMBSCATEGORY4,
            SummaryItem.CLIMBSCATEGORYHC,
            SummaryItem.CLIMBASCENTCATEGORY1,
            SummaryItem.CLIMBASCENTCATEGORY2,
            SummaryItem.CLIMBASCENTCATEGORY3,
            SummaryItem.CLIMBASCENTCATEGORY4,
            SummaryItem.CLIMBASCENTCATEGORYHC,
            SummaryItem.CLIMBDISTANCECATEGORY1,
            SummaryItem.CLIMBDISTANCECATEGORY2,
            SummaryItem.CLIMBDISTANCECATEGORY3,
            SummaryItem.CLIMBDISTANCECATEGORY4,
            SummaryItem.CLIMBDISTANCECATEGORYHC,
            SummaryItem.CLIMBDURATIONCATEGORY1,
            SummaryItem.CLIMBDURATIONCATEGORY2,
            SummaryItem.CLIMBDURATIONCATEGORY3,
            SummaryItem.CLIMBDURATIONCATEGORY4,
            SummaryItem.CLIMBDURATIONCATEGORYHC,
            SummaryItem.AVGASCENTSPEED,
            SummaryItem.AVGDESCENTSPEED,
            SummaryItem.MAXASCENTSPEED,
            SummaryItem.MAXDESCENTSPEED,
            SummaryItem.AVGDISTANCEPERSTROKE,
            SummaryItem.ZONESENSEBASELINE,
            SummaryItem.ZONESENSECUMULATIVEBASELINE,
            SummaryItem.AVGSKIPSRATE,
            SummaryItem.MAXAVGSKIPSRATE,
            SummaryItem.ROUNDS,
            SummaryItem.AVGSKIPSPERROUND,
            SummaryItem.MAXCONSECUTIVESKIPS,
            SummaryItem.AVGROWINGPACE,
            SummaryItem.AVGSTEPLENGTH,
            SummaryItem.ESTIMATEDFLOORSCLIMBED -> 0
        }

        is LapsTableDataType.SuuntoPlus -> R.drawable.ic_suuntoplus
    }
}

fun LapsTableDataType.label(context: Context, isDownhill: Boolean): String? {
    // Downhill laps have some special cases for strings
    if (isDownhill) {
        when (this) {
            LapsTableDataType.Summary(SummaryItem.DURATION) ->
                return context.getString(CR.string.summary_item_title_duration_downhill)

            LapsTableDataType.Summary(SummaryItem.DISTANCE) ->
                return context.getString(CR.string.summary_item_title_distance_downhill)

            LapsTableDataType.Summary(SummaryItem.DESCENTALTITUDE) ->
                return context.getString(CR.string.summary_item_title_descent_downhill)

            LapsTableDataType.Summary(SummaryItem.AVGSPEED) ->
                return context.getString(CR.string.summary_item_title_avg_speed_downhill)

            LapsTableDataType.Summary(SummaryItem.MAXSPEED) ->
                return context.getString(CR.string.summary_item_title_max_speed_downhill)

            else -> { // fall through
            }
        }
    }

    // Not downhill or not a special case
    return when (this) {
        is LapsTableDataType.Summary -> when (summaryItem) {
            SummaryItem.DURATION -> CR.string.summary_item_title_duration
            SummaryItem.TOTALTIME -> 0
            SummaryItem.PAUSETIME -> 0
            SummaryItem.MOVINGTIME -> 0
            SummaryItem.RESTTIME -> 0
            SummaryItem.DISTANCE -> CR.string.summary_item_title_distance
            SummaryItem.AVGPACE -> CR.string.summary_item_title_avg_pace
            SummaryItem.MOVINGPACE -> 0
            SummaryItem.AVGHEARTRATE -> CR.string.summary_item_title_avg_hr
            SummaryItem.MINHEARTRATE -> CR.string.summary_item_title_min_hr
            SummaryItem.MAXHEARTRATE -> CR.string.summary_item_title_max_hr
            SummaryItem.ENERGY -> CR.string.summary_item_title_energy
            SummaryItem.RECOVERYTIME -> 0
            SummaryItem.PTE -> 0
            SummaryItem.PERFORMANCELEVEL -> 0
            SummaryItem.AVGSPEED -> CR.string.summary_item_title_avg_speed
            SummaryItem.MOVINGSPEED -> 0
            SummaryItem.AVGVERTICALSPEED -> CR.string.summary_item_title_avg_vertical_speed
            SummaryItem.AVGCADENCE -> CR.string.summary_item_title_avg_cadence
            SummaryItem.STEPS -> 0
            SummaryItem.AVGSTEPCADENCE -> 0
            SummaryItem.MAXSTEPCADENCE -> 0
            SummaryItem.AVGSTRIDELENGTH -> 0
            SummaryItem.ASCENTALTITUDE -> CR.string.summary_item_title_ascent
            SummaryItem.DESCENTALTITUDE -> CR.string.summary_item_title_descent
            SummaryItem.HIGHALTITUDE -> CR.string.summary_item_title_max_altitude
            SummaryItem.LOWALTITUDE -> CR.string.summary_item_title_min_altitude
            SummaryItem.PEAKVERTICALSPEED30S -> 0
            SummaryItem.PEAKVERTICALSPEED1M -> 0
            SummaryItem.PEAKVERTICALSPEED3M -> 0
            SummaryItem.PEAKVERTICALSPEED5M -> 0
            SummaryItem.AVGTEMPERATURE -> CR.string.summary_item_title_avg_temperature
            SummaryItem.MAXTEMPERATURE -> CR.string.summary_item_title_max_temperature
            SummaryItem.PEAKEPOC -> 0
            SummaryItem.FEELING -> 0
            SummaryItem.MOVETYPE -> 0
            SummaryItem.CATCHFISH -> 0
            SummaryItem.CATCHBIGGAME -> 0
            SummaryItem.CATCHSMALLGAME -> 0
            SummaryItem.CATCHBIRD -> 0
            SummaryItem.CATCHSHOTCOUNT -> 0
            SummaryItem.AVGPOWER -> CR.string.summary_item_title_avg_power
            SummaryItem.MAXPOWER -> CR.string.summary_item_title_max_power
            SummaryItem.AVGPOWERWITHZERO -> 0
            SummaryItem.PEAKPOWER30S -> 0
            SummaryItem.PEAKPOWER1M -> 0
            SummaryItem.PEAKPOWER3M -> 0
            SummaryItem.PEAKPOWER5M -> 0
            SummaryItem.AVGSWOLF -> CR.string.summary_item_title_avg_swolf
            SummaryItem.AVGSWIMSTROKERATE -> CR.string.summary_item_title_avg_swim_stroke_rate
            SummaryItem.AVGNAUTICALSPEED -> CR.string.summary_item_title_nautical_avg_speed
            SummaryItem.MAXNAUTICALSPEED -> CR.string.summary_item_title_nautical_max_speed
            SummaryItem.NAUTICALDISTANCE -> CR.string.summary_item_title_nautical_distance
            SummaryItem.AVGSEALEVELPRESSURE -> CR.string.summary_item_title_avg_sea_level_pressure
            SummaryItem.MAXPACE -> CR.string.summary_item_title_max_pace
            SummaryItem.PEAKPACE30S -> 0
            SummaryItem.PEAKPACE1M -> 0
            SummaryItem.PEAKPACE3M -> 0
            SummaryItem.PEAKPACE5M -> 0
            SummaryItem.PEAKSPEED30S -> 0
            SummaryItem.PEAKSPEED1M -> 0
            SummaryItem.PEAKSPEED3M -> 0
            SummaryItem.PEAKSPEED5M -> 0
            SummaryItem.MAXSPEED -> CR.string.summary_item_title_max_speed
            SummaryItem.MAXDEPTH -> CR.string.summary_item_title_max_depth
            SummaryItem.DIVETIME -> CR.string.summary_item_title_dive_time
            SummaryItem.DIVETIMEMAX -> CR.string.summary_item_title_dive_time_max
            SummaryItem.DIVEMODE -> 0
            SummaryItem.DIVENUMBERINSERIES -> 0
            SummaryItem.DIVESURFACETIME -> CR.string.summary_item_title_dive_surface_time
            SummaryItem.DIVERECOVERYTIME -> CR.string.summary_item_title_dive_recovery_time
            SummaryItem.DIVEVISIBILITY -> 0
            SummaryItem.DIVEMAXDEPTHTEMPERATURE -> 0
            SummaryItem.SKIRUNCOUNT -> 0
            SummaryItem.SKIDISTANCE -> 0
            SummaryItem.SKITIME -> 0
            SummaryItem.AVGSKISPEED -> 0
            SummaryItem.MAXSKISPEED -> 0
            SummaryItem.ASCENTTIME -> 0
            SummaryItem.DESCENTTIME -> 0
            SummaryItem.ESTVO2PEAK -> 0
            SummaryItem.ALGORITHMLOCK -> 0
            SummaryItem.DIVECNS -> 0
            SummaryItem.DIVEOTU -> 0
            SummaryItem.AVGDEPTH -> 0
            SummaryItem.DIVEGASES -> 0
            SummaryItem.PERSONAL -> 0
            SummaryItem.GRADIENTFACTORS -> 0
            SummaryItem.ALTITUDESETTING -> 0
            SummaryItem.GASCONSUMPTION -> 0
            SummaryItem.ALGORITHM -> 0
            SummaryItem.AVGSWIMPACE -> R.string.workout_values_headline_swimming_avg_pace
            SummaryItem.SWIMSTROKECOUNT -> 0
            SummaryItem.SWIMSTROKEDISTANCE -> 0
            SummaryItem.CUMULATEDDISTANCE -> CR.string.summary_item_title_cumulated_distance
            SummaryItem.CUMULATEDDURATION -> CR.string.summary_item_title_cumulated_duration
            SummaryItem.SWIMDISTANCE -> CR.string.summary_item_title_distance
            SummaryItem.CUMULATEDSWIMDISTANCE -> CR.string.summary_item_title_cumulated_distance
            SummaryItem.SWIMSTYLE -> CR.string.summary_item_title_swim_style
            SummaryItem.TYPE -> CR.string.summary_item_title_interval_type
            SummaryItem.NONE -> 0
            SummaryItem.DIVEDISTANCE -> 0
            SummaryItem.DIVEGASPRESSURE -> 0
            SummaryItem.DIVEGASENDPRESSURE -> 0
            SummaryItem.DIVEGASUSEDPRESSURE -> 0
            SummaryItem.TRAININGSTRESSSCORE -> CR.string.summary_item_tss // todo
            SummaryItem.NORMALIZEDPOWER -> 0
            SummaryItem.NORMALIZEDGRADEDPACE -> 0
            SummaryItem.CO2EMISSIONSREDUCED -> 0
            SummaryItem.MINDEPTH -> CR.string.summary_item_title_min_depth
            SummaryItem.DIVEINWORKOUT -> 0
            SummaryItem.DOWNHILLDISTANCE -> 0
            SummaryItem.DOWNHILLDESCENT -> 0
            SummaryItem.DOWNHILLDURATION -> 0
            SummaryItem.DOWNHILLCOUNT -> 0
            SummaryItem.AVGDOWNHILLSPEED -> 0
            SummaryItem.MAXDOWNHILLSPEED -> 0
            SummaryItem.AVGDOWNHILLGRADE -> 0
            SummaryItem.MAXDOWNHILLGRADE -> 0
            SummaryItem.DOWNHILLMAXDESCENT -> 0
            SummaryItem.DOWNHILLMAXLENGTH -> 0
            SummaryItem.REVOLUTIONCOUNT -> CR.string.summary_item_title_revolution_count
            SummaryItem.ROWINGSTROKECOUNT -> CR.string.summary_item_title_rowing_stroke_count
            SummaryItem.SKIPCOUNT -> CR.string.summary_item_title_skip_count
            SummaryItem.MAXCADENCE -> 0
            SummaryItem.HRAEROBICTHRESHOLD -> 0
            SummaryItem.HRANAEROBICTHRESHOLD -> 0
            SummaryItem.AEROBICPACETHRESHOLD -> 0
            SummaryItem.ANAEROBICPACETHRESHOLD -> 0
            SummaryItem.ANAEROBICPOWERTHRESHOLD -> 0
            SummaryItem.AEROBICPOWERTHRESHOLD -> 0
            SummaryItem.AEROBICDURATION -> 0
            SummaryItem.ANAEROBICDURATION -> 0
            SummaryItem.VO2MAXDURATION -> 0
            SummaryItem.BREATHINGRATE -> CR.string.workout_values_headline_breathing_rate
            SummaryItem.BREASTSTROKEGLIDETIME -> CR.string.workout_values_headline_breaststroke_glide_time
            SummaryItem.AVGFREESTYLEBREATHANGLE -> CR.string.workout_values_headline_freestyle_avg_breath_angle
            SummaryItem.AVGBREASTSTROKEBREATHANGLE -> CR.string.workout_values_headline_breaststroke_avg_breath_angle
            // merge freestyle head angle and breaststroke head angle, all use SummaryItem.BREASTSTROKEHEADANGLE
            SummaryItem.FREESTYLEPITCHANGLE -> 0
            SummaryItem.BREASTSTROKEHEADANGLE -> CR.string.summary_item_category_head_angle
            SummaryItem.FREESTYLEDURATION,
            SummaryItem.BREASTSTROKEDURATION,
            SummaryItem.FREESTYLEPERCENT,
            SummaryItem.BREASTSTROKEPERCENT,
            SummaryItem.MAXFREESTYLEBREATHANGLE,
            SummaryItem.MAXBREASTSTROKEBREATHANGLE -> 0

            SummaryItem.FATCONSUMPTION -> CR.string.summary_item_title_fat_consumption
            SummaryItem.CARBOHYDRATECONSUMPTION -> CR.string.summary_item_title_carbohydrate_consumption
            SummaryItem.AVGGROUNDCONTACTTIME -> CR.string.summary_item_title_avg_ground_contact_time
            SummaryItem.AVGVERTICALOSCILLATION -> CR.string.summary_item_title_avg_vertical_oscillation
            SummaryItem.AVGGROUNDCONTACTBALANCE -> CR.string.summary_item_title_avg_ground_contact_balance
            SummaryItem.CLIMBS,
            SummaryItem.CLIMBSCATEGORY1,
            SummaryItem.CLIMBSCATEGORY2,
            SummaryItem.CLIMBSCATEGORY3,
            SummaryItem.CLIMBSCATEGORY4,
            SummaryItem.CLIMBSCATEGORYHC,
            SummaryItem.CLIMBASCENTCATEGORY1,
            SummaryItem.CLIMBASCENTCATEGORY2,
            SummaryItem.CLIMBASCENTCATEGORY3,
            SummaryItem.CLIMBASCENTCATEGORY4,
            SummaryItem.CLIMBASCENTCATEGORYHC,
            SummaryItem.CLIMBDISTANCECATEGORY1,
            SummaryItem.CLIMBDISTANCECATEGORY2,
            SummaryItem.CLIMBDISTANCECATEGORY3,
            SummaryItem.CLIMBDISTANCECATEGORY4,
            SummaryItem.CLIMBDISTANCECATEGORYHC,
            SummaryItem.CLIMBDURATIONCATEGORY1,
            SummaryItem.CLIMBDURATIONCATEGORY2,
            SummaryItem.CLIMBDURATIONCATEGORY3,
            SummaryItem.CLIMBDURATIONCATEGORY4,
            SummaryItem.CLIMBDURATIONCATEGORYHC,
            SummaryItem.AVGASCENTSPEED,
            SummaryItem.AVGDESCENTSPEED,
            SummaryItem.MAXASCENTSPEED,
            SummaryItem.MAXDESCENTSPEED,
            SummaryItem.AVGDISTANCEPERSTROKE,
            SummaryItem.ZONESENSEBASELINE,
            SummaryItem.ZONESENSECUMULATIVEBASELINE,
            SummaryItem.AVGSKIPSRATE,
            SummaryItem.MAXAVGSKIPSRATE,
            SummaryItem.ROUNDS,
            SummaryItem.AVGSKIPSPERROUND,
            SummaryItem.MAXCONSECUTIVESKIPS,
            SummaryItem.AVGROWINGPACE,
            SummaryItem.AVGSTEPLENGTH,
            SummaryItem.ESTIMATEDFLOORSCLIMBED -> 0
        }.takeIf { it != 0 }?.let { context.getString(it) }

        is LapsTableDataType.SuuntoPlus ->
            context.getString(CR.string.suuntoplus_item_title_avg, suuntoPlusChannel.name)
    }
}

fun LapsTableDataType.localize(context: Context, isDownhill: Boolean): String =
    label(context, isDownhill) ?: key
