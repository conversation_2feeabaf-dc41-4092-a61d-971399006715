package com.stt.android.extensions

import com.google.android.gms.maps.model.LatLng
import com.stt.android.domain.sml.MultisportPartActivity
import com.stt.android.domain.sml.RecordingStatusEvent
import com.stt.android.domain.sml.RecordingStatusEventType
import com.stt.android.domain.sml.Sml
import com.stt.android.domain.sml.TraverseEvent
import com.stt.android.domain.sml.filterSamplePoint

val Sml.multisportRoutes: List<List<LatLng>>
    get() = streamData.multisportPartActivities.map { activity ->
        streamData.samplePoint.filterSamplePoint(activity)
            .asSequence()
            .filter { it.latitude != null && it.longitude != null }
            .map { LatLng(it.latitude!!.toDouble(), it.longitude!!.toDouble()) }
            .toList()
    }

fun Sml.getIndexOfHighlightedRoute(
    multisportPartActivity: MultisportPartActivity?
): Int {
    return multisportPartActivity?.run { streamData.multisportPartActivities.indexOf(this) } ?: -1
}

val List<List<LatLng>>.activityTypeChangeCoordinates: List<LatLng>
    get() = dropLast(1).filter { it.isNotEmpty() }.map { it.last() }

val Sml.traverseEvents: List<TraverseEvent>
    get() = streamData.events.filterIsInstance<TraverseEvent>()

val Sml.recordingStatusEvents: List<RecordingStatusEvent>
    get() = streamData.events.filterIsInstance<RecordingStatusEvent>()

/**
 * The pause or stop event that marks the end of the workout.
 *
 * Note:
 * 1. On older devices, there is no pause event right before the stop event.
 * 2. On newer devices, user might keep "tracking" after pausing until stopping the workout, but we
 *    consider the last "pause" as end of the workout.
 */
val Sml.finalPauseOrStopEvent: RecordingStatusEvent?
    get() {
        var lastPauseEvent: RecordingStatusEvent? = null
        var lastStopEvent: RecordingStatusEvent? = null
        val iterator = streamData.events.reversed().iterator()
        while (iterator.hasNext()) {
            val event = (iterator.next() as? RecordingStatusEvent) ?: continue
            when (event.type) {
                RecordingStatusEventType.Start,
                RecordingStatusEventType.Resume -> break

                RecordingStatusEventType.Pause -> {
                    lastPauseEvent = event
                    break
                }

                RecordingStatusEventType.Stop -> if (lastStopEvent == null) {
                    lastStopEvent = event
                }
            }
        }
        return lastPauseEvent ?: lastStopEvent
    }

/**
 * On older devices, there is no resume event after start, and no pause event before stop event. Therefore,
 * the first pair uses the start or first resume event, and last pair uses the stop or last pause event.
 */
val Sml.findResumePauseEventPairs: List<Pair<RecordingStatusEvent, RecordingStatusEvent>>
    get() =
        buildList {
            var lastResumeEvent: RecordingStatusEvent? = null

            streamData.events.forEach { event ->
                val statusEvent = event as? RecordingStatusEvent ?: return@forEach
                when (statusEvent.type) {
                    RecordingStatusEventType.Start -> lastResumeEvent = statusEvent
                        .copy(type = RecordingStatusEventType.Resume)

                    RecordingStatusEventType.Resume -> lastResumeEvent = statusEvent
                    RecordingStatusEventType.Pause -> lastResumeEvent?.let {
                        add(it to statusEvent)
                        lastResumeEvent = null
                    }

                    RecordingStatusEventType.Stop -> lastResumeEvent?.let {
                        add(it to statusEvent.copy(type = RecordingStatusEventType.Pause))
                        lastResumeEvent = null
                    }
                }
            }
        }
