package com.stt.android.remoteconfig

import com.stt.android.remote.DefaultAskoRemoteConfig
import com.stt.android.remote.remoteconfig.AskoRemoteConfigResponse
import com.stt.android.remote.remoteconfig.AskoRemoteConfigValuesWithConditions
import com.stt.android.remote.remoteconfig.CompanionLinkingParameters
import com.stt.android.remote.remoteconfig.GraphhopperBaseUrl
import com.stt.android.remote.remoteconfig.STFusedLocationParameters
import com.stt.android.remote.remoteconfig.ValueConditions
import javax.inject.Inject
import kotlin.reflect.full.declaredMemberProperties

class AskoRemoteConfigDefaults
@Inject constructor(
    @DefaultAskoRemoteConfig val config: AskoRemoteConfigResponse
) {

    init {
        verify()
    }

    fun <T> defaultFor(
        getConditions: AskoRemoteConfigResponse.() -> AskoRemoteConfigValuesWithConditions<T>?
    ): T = config.getConditions()!!.values.first().value!!

    fun verify() {
        AskoRemoteConfigResponse::class.declaredMemberProperties.forEach {
            val property = it.get(config) as? AskoRemoteConfigValuesWithConditions<*>
            if (property != null && property.values.firstOrNull()?.value == null) {
                throw AskoRemoteConfigException("${this::class.java.name} defaults do not respect contract")
            }
        }
    }

    companion object {
        internal const val DEFAULT_PARTNER_CONNECTIONS_ENABLED: Boolean = true
        internal const val DEFAULT_SML_TO_BACKEND: Boolean = true
        internal const val DEFAULT_AI_PLANNER_ENABLED: Boolean =
            false // todo Can be updated to be true after the feature is launched

        internal val ST_FUSED_LOCATION_DEFAULTS: STFusedLocationParameters =
            STFusedLocationParameters(enabled = false)

        internal val COMPANION_LINKING_PARAMETERS_DEFAULTS = CompanionLinkingParameters(
            // Is Unlink option available?
            unlinkAvailable = false,
            // If true, Suunto watch service notification is show although
            // companion association exists.
            showForegroundNotificationAnyway = false
        )

        internal val DEFAULT_GRAPHHOPPER_BASE_URL: GraphhopperBaseUrl = GraphhopperBaseUrl(
            url = "https://graphhopper.com/api/1",
            alternatives = emptyList(),
        )

        @JvmStatic
        fun <T> singleCondition(value: T) =
            AskoRemoteConfigValuesWithConditions(listOf(ValueConditions(value = value)))
    }
}
