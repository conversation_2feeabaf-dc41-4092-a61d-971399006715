package com.stt.android.sharing

import android.app.Activity
import android.content.Intent
import android.net.Uri
import androidx.core.app.ShareCompat
import com.stt.android.analytics.ShareBroadcastReceiver
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.multimedia.MediaType
import timber.log.Timber
import java.io.FileOutputStream
import java.util.UUID

object SharingToSystemHelper {
    fun shareToSystem(
        activity: Activity,
        sharingInfo: SharingInfo,
        analysisData: AnalysisData,
    ) {
        when (sharingInfo.sharingType) {
            SharingType.MEDIA -> {
                if (sharingInfo.resourceUris.isEmpty()) {
                    Timber.w("share mediaUris is empty, can't share")
                    return
                }
                shareMediaToOS(activity, sharingInfo, analysisData)
            }

            SharingType.LINK -> {
                shareLinkToOS(activity, sharingInfo, analysisData)
            }
        }
    }

    private fun shareMediaToOS(
        activity: Activity,
        sharingInfo: SharingInfo,
        analysisData: AnalysisData,
    ) {
        // When using system sharing, need to save it to MediaStore
        val mediaImageUris = arrayListOf<Uri>()
        sharingInfo.resourceUris.forEach {
            val displayName = UUID.randomUUID().toString().take(8)
            mediaImageUris.add(
                MediaStoreUtils.saveMediaToMediaStore(
                    activity.contentResolver,
                    if (sharingInfo.mediaType == MediaType.VIDEO) displayName.plus(".mp4")
                    else displayName.plus(".jpg"),
                    sharingInfo.mediaType
                ) { fileDescriptor ->
                    FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                        activity.contentResolver.openInputStream(it).use {
                            outputStream.write(it?.readBytes())
                        }
                    }
                }
            )
        }
        val shareIntent = Intent()
        shareIntent.action = Intent.ACTION_SEND_MULTIPLE
        shareIntent.type =
            if (sharingInfo.mediaType == MediaType.IMAGE) "image/jpg" else "video/mp4"
        shareIntent.putParcelableArrayListExtra(Intent.EXTRA_STREAM, mediaImageUris)
        ShareBroadcastReceiver.shareToSystem(
            activity,
            shareIntent,
            analysisData.eventName,
            analysisData.analyticsProperties,
        )
    }

    private fun shareLinkToOS(
        activity: Activity,
        sharingInfo: SharingInfo,
        analysisData: AnalysisData,
    ) {
        val shareLinkInfo = sharingInfo.shareLinkInfo
        if (shareLinkInfo == null || shareLinkInfo.linkUrl.isEmpty()) {
            Timber.w("share linkUrl is empty, can't share")
            return
        }
        val shareIntent = ShareCompat.IntentBuilder(activity)
            .setType("text/plain")
            .setText(shareLinkInfo.linkUrl)
            .intent
        shareIntent.action = Intent.ACTION_SEND
        shareIntent.putExtra(Intent.EXTRA_TEXT, shareLinkInfo.linkUrl)
        ShareBroadcastReceiver.shareToSystem(
            activity,
            shareIntent,
            analysisData.eventName,
            analysisData.analyticsProperties
        )
    }
}
