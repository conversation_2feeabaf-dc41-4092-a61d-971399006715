package com.stt.android.sharing

import android.graphics.Bitmap
import android.net.Uri
import android.os.Parcelable
import com.stt.android.multimedia.MediaType
import kotlinx.parcelize.Parcelize

@Parcelize
data class SharingInfo(
    val sharingType: SharingType,
    val resourceUris: List<Uri> = emptyList(),
    // send indexes of resources to analysis platform.
    // e.g: have 9 resources, and want to send 1st, 3rd and 5th resources to analysis platform.
    val resourcesIndexes: List<Int> = emptyList(),
    val shareLinkInfo: ShareLinkInfo? = null,
    // add these tag for weibo, xiaohongshu, douyin to add topics
    val hashTags: List<String> = emptyList(),
    val mediaType: MediaType = MediaType.IMAGE,
    // indicate which h5 link this sharing is from, or other source of sharing
    val shareSourceName: String = "",
) : Parcelable

@Parcelize
data class ShareLinkInfo(
    val linkUrl: String,
    val title: String,
    val description: String,
    val iconBitmap: Bitmap? = null,
) : Parcelable

enum class SharingType {
    MEDIA,
    LINK,
}
