package com.stt.android.domain.user;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.google.gson.annotations.SerializedName;
import com.stt.android.domain.Point;

public class BackendVideoInformation {
    @SerializedName("key")
    private final String key;

    @SerializedName("username")
    private final String username;

    @Nullable
    @SerializedName("description")
    private final String description;

    @SerializedName("url")
    private final String url;

    @SerializedName("thumbnailUrl")
    private final String thumbnailUrl;

    @SerializedName("timestamp")
    private final long timestamp;

    @SerializedName("totalTime")
    private final long totalTime;

    @SerializedName("width")
    private final int width;

    @SerializedName("height")
    private final int height;

    @Nullable
    @SerializedName("location")
    private final Point location;

    @SerializedName("contentReviewStatus")
    private final int reviewState;

    public BackendVideoInformation(String key, String username, @Nullable String description,
        String url, String thumbnailUrl, long timestamp, long totalTime, int width, int height,
        @Nullable Point location, int reviewState) {
        this.key = key;
        this.username = username;
        this.description = description;
        this.url = url;
        this.thumbnailUrl = thumbnailUrl;
        this.timestamp = timestamp;
        this.totalTime = totalTime;
        this.width = width;
        this.height = height;
        this.location = location;
        this.reviewState = reviewState;
    }

    @NonNull
    public VideoInformation toVideoInformation(@NonNull String workoutKey) {
        return new VideoInformation(key.hashCode(), key, null, workoutKey, username, totalTime,
            timestamp, description, location, url, thumbnailUrl, width, height, null, null, false, ConvertToReviewState.INSTANCE.getReviewState(reviewState));
    }
}
