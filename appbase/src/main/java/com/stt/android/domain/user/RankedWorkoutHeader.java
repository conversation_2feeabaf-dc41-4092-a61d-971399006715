package com.stt.android.domain.user;

import android.os.Parcel;
import android.os.Parcelable;
import com.stt.android.domain.workouts.WorkoutHeader;
import java.util.Objects;

/**
 * Helper class used to hold extra information per each workout in a list. (See
 * {@link com.stt.android.controllers.SessionController#getWorkoutsWithSimilarRoute(WorkoutHeader)} and
 * {@link com.stt.android.controllers.SessionController#getWorkoutsWithSimilarDistance(WorkoutHeader)})
 */
public class RankedWorkoutHeader implements Parcelable {
    public static final Creator<RankedWorkoutHeader> CREATOR = new Creator<RankedWorkoutHeader>() {
        @Override
        public RankedWorkoutHeader createFromParcel(Parcel in) {
            int rank = in.readInt();
            WorkoutHeader header = in.readParcelable(WorkoutHeader.class.getClassLoader());
            double barWidth = in.readDouble();
            boolean selected = in.readInt() == 1;
            return new RankedWorkoutHeader(header, rank, barWidth, selected);
        }

        @Override
        public RankedWorkoutHeader[] newArray(int size) {
            return new RankedWorkoutHeader[size];

        }
    };
    /**
     * Position inside the list of workouts.
     */
    private final int rank;
    private final WorkoutHeader header;
    /**
     * Relation (in percent) between the total time of {@link #header} and the maximum in the list of workouts.
     */
    private final double percentageOfMaxDuration;
    private final boolean selected;

    public RankedWorkoutHeader(WorkoutHeader header, int rank, double percentageOfMaxDuration, boolean selected) {
        this.rank = rank;
        this.header = header;
        this.percentageOfMaxDuration = percentageOfMaxDuration;
        this.selected = selected;
    }

    public int getRank() {
        return rank;
    }

    public WorkoutHeader getHeader() {
        return header;
    }

    public boolean isSelected() {
        return selected;
    }

    public double getPercentageOfMaxDuration() {
        return percentageOfMaxDuration;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeInt(rank);
        parcel.writeParcelable(header, i);
        parcel.writeDouble(percentageOfMaxDuration);
        parcel.writeInt(selected ? 1 : 0);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof RankedWorkoutHeader)) return false;
        RankedWorkoutHeader that = (RankedWorkoutHeader) o;
        return rank == that.rank &&
            Double.compare(that.percentageOfMaxDuration, percentageOfMaxDuration) == 0 &&
            selected == that.selected &&
            Objects.equals(header, that.header);
    }

    @Override
    public int hashCode() {
        return Objects.hash(rank, header, percentageOfMaxDuration, selected);
    }
}
