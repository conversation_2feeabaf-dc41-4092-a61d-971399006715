package com.stt.android.domain.user;

import android.content.Context;
import android.net.Uri;
import android.os.Parcel;
import android.os.Parcelable;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.j256.ormlite.field.DataType;
import com.j256.ormlite.field.DatabaseField;
import com.j256.ormlite.table.DatabaseTable;
import com.stt.android.domain.Point;
import com.stt.android.domain.review.ReviewState;
import com.stt.android.domain.workouts.videos.Video;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import java.io.FileNotFoundException;
import java.util.Objects;
import java.util.Random;
import timber.log.Timber;

@DatabaseTable(tableName = VideoInformation.TABLE_NAME)
public class VideoInformation implements Parcelable {
    public static final String TABLE_NAME = "videoinformation";

    public abstract static class DbFields {

        public static final String ID = "id";
        public static final String KEY = "key";
        public static final String WORKOUT_ID = "workoutId";
        public static final String WORKOUT_KEY = "workoutKey";
        public static final String USERNAME = "username";
        public static final String TOTAL_TIME = "totalTime";
        public static final String TIMESTAMP = "timestamp";
        public static final String DESCRIPTION = "description";
        public static final String LOCATION = "location";
        public static final String URL = "url";
        public static final String THUMBNAIL_URL = "thumbnailUrl";
        public static final String WIDTH = "width";
        public static final String HEIGHT = "height";
        public static final String FILE_NAME = "fileName";
        public static final String THUMBNAIL_FILE_NAME = "thumbnailFileName";
        public static final String LOCALLY_CHANGED = "locallyChanged";
        public static final String REVIEW_STATE = "reviewState";
    }
    @DatabaseField(id = true, columnName = DbFields.ID)
    private final int id;

    @Nullable
    @DatabaseField(columnName = DbFields.KEY)
    private final String key;

    @Nullable
    @DatabaseField(columnName = DbFields.WORKOUT_ID)
    private final Integer workoutId;

    @Nullable
    @DatabaseField(columnName = DbFields.WORKOUT_KEY)
    private final String workoutKey;

    @NonNull
    @DatabaseField(canBeNull = false, columnName = DbFields.USERNAME)
    private final String username;

    @DatabaseField(columnName = DbFields.TOTAL_TIME)
    private final long totalTime;

    @DatabaseField(columnName = DbFields.TIMESTAMP)
    private final long timestamp;

    @Nullable
    @DatabaseField(columnName = DbFields.DESCRIPTION)
    private final String description;

    @Nullable
    @DatabaseField(dataType = DataType.SERIALIZABLE, columnName = DbFields.LOCATION)
    private final Point location;

    @Nullable
    @DatabaseField(columnName = DbFields.URL)
    private final String url;

    @Nullable
    @DatabaseField(columnName = DbFields.THUMBNAIL_URL)
    private final String thumbnailUrl;

    @DatabaseField(columnName = DbFields.WIDTH)
    private final int width;

    @DatabaseField(columnName = DbFields.HEIGHT)
    private final int height;

    @Nullable
    @DatabaseField(columnName = DbFields.FILE_NAME)
    private final String fileName;

    @Nullable
    @DatabaseField(columnName = DbFields.THUMBNAIL_FILE_NAME)
    private final String thumbnailFileName;

    @DatabaseField(columnName = DbFields.LOCALLY_CHANGED)
    private final boolean locallyChanged;

    @DatabaseField(columnName = DbFields.REVIEW_STATE, dataType = DataType.ENUM_STRING)
    private final ReviewState reviewState;

    // makes OrmLite happy

    private VideoInformation() {
        this(0, null, null, null, "", 0L, 0L, null, null, null, null, 0, 0, null, null, false, ReviewState.PASS);
    }
    /**
     * Creates a new video information that is to be saved.
     */
    public VideoInformation(@Nullable Integer workoutId, @Nullable String workoutKey,
        @NonNull String username, long totalTime, long timestamp, @Nullable Point location,
        int width, int height, @Nullable String fileName, @Nullable String thumbnailFileName) {
        this(generateId(), null, workoutId, workoutKey, username, totalTime,
            timestamp, null, location, null, null, width, height, fileName, thumbnailFileName,
            true, ReviewState.PASS);
    }

    // Generate a random ID if we don't have an ID already.
    // This may theoretically cause conflicts since the ID space is only 32 bits. At least it is
    // much more unique than getting hash of current timestamp like this used to work before.
    // TODO: use a proper ID (e.g. UUID) when migrating to Room
    private static int generateId() {
        return new Random().nextInt();
    }

    public VideoInformation(int id, @Nullable String key, @Nullable Integer workoutId,
        @Nullable String workoutKey, @NonNull String username, long totalTime, long timestamp,
        @Nullable String description, @Nullable Point location, @Nullable String url,
        @Nullable String thumbnailUrl, int width, int height, @Nullable String fileName,
        @Nullable String thumbnailFileName, boolean locallyChanged, ReviewState reviewState) {
        this.id = id;
        this.key = key;
        this.workoutId = workoutId;
        this.workoutKey = workoutKey;
        this.username = username;
        this.totalTime = totalTime;
        this.timestamp = timestamp;
        this.description = description;
        this.location = location;
        this.url = url;
        this.thumbnailUrl = thumbnailUrl;
        this.width = width;
        this.height = height;
        this.fileName = fileName;
        this.thumbnailFileName = thumbnailFileName;
        this.locallyChanged = locallyChanged;
        this.reviewState = reviewState;
    }

    @NonNull
    public Video toVideo() {
        return new Video(
            id,
            key,
            workoutId,
            workoutKey,
            username,
            totalTime,
            timestamp,
            description,
            location,
            url,
            thumbnailUrl,
            width,
            height,
            fileName,
            thumbnailFileName,
            locallyChanged,
            reviewState != null ? reviewState : ReviewState.PASS
        );
    }

    public static VideoInformation fromVideo(Video video) {
        return new VideoInformation(
            video.getId() != null ? video.getId() : generateId(),
            video.getKey(),
            video.getWorkoutId(),
            video.getWorkoutKey(),
            video.getUsername(),
            video.getTotalTime(),
            video.getTimestamp(),
            video.getDescription(),
            video.getLocation(),
            video.getUrl(),
            video.getThumbnailUrl(),
            video.getWidth(),
            video.getHeight(),
            video.getFilename(),
            video.getThumbnailFilename(),
            video.getLocallyChanged(),
            video.getReviewState()
        );
    }

    public int getId() {
        return id;
    }

    @NonNull
    public String getUsername() {
        return username;
    }

    @Nullable
    public String getDescription() {
        return description;
    }

    @Nullable
    public String getUrl() {
        return url;
    }

    @Nullable
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    @Nullable
    public String getKey() {
        return key;
    }

    @Nullable
    public Integer getWorkoutId() {
        return workoutId;
    }

    @Nullable
    public String getWorkoutKey() {
        return workoutKey;
    }

    public long getTotalTime() {
        return totalTime;
    }

    public long getTimestamp() {
        return timestamp;
    }

    @Nullable
    public Point getLocation() {
        return location;
    }

    @Nullable
    public String getFileName() {
        return fileName;
    }

    @Nullable
    public ReviewState getReviewState() { return reviewState; }

    @Nullable
    public String getThumbnailFileName() {
        return thumbnailFileName;
    }

    @Nullable
    public Uri getUri(Context context) {
        if (!TextUtils.isEmpty(url)) {
            return Uri.parse(url);
        }
        if (!TextUtils.isEmpty(fileName)) {
            try {
                return Uri.fromFile(
                    FileUtils.getInternalFilePath(context, STTConstants.DIRECTORY_VIDEOS,
                        fileName));
            } catch (FileNotFoundException e) {
                Timber.w(e);
            }
        }
        return null;
    }

    @Nullable
    public Uri getThumbnailUri(Context context) {
        if (!TextUtils.isEmpty(thumbnailUrl)) {
            return Uri.parse(thumbnailUrl);
        }
        if (!TextUtils.isEmpty(thumbnailFileName)) {
            try {
                return Uri.fromFile(
                    FileUtils.getInternalFilePath(context, STTConstants.DIRECTORY_VIDEOS,
                        thumbnailFileName));
            } catch (FileNotFoundException e) {
                Timber.w(e);
            }
        }
        return null;
    }

    public int getWidth() {
        return width;
    }

    public int getHeight() {
        return height;
    }

    public boolean isLocallyChanged() {
        return locallyChanged;
    }

    @NonNull
    public VideoInformation updateFileName(String fileName) {
        return new VideoInformation(id, key, workoutId, workoutKey, username, totalTime, timestamp,
            description, location, url, thumbnailUrl, width, height, fileName, thumbnailFileName,
            true, reviewState);
    }

    @NonNull
    public VideoInformation updateThumbnailFileName(String thumbnailFileName) {
        return new VideoInformation(id, key, workoutId, workoutKey, username, totalTime, timestamp,
            description, location, url, thumbnailUrl, width, height, fileName, thumbnailFileName,
            true, reviewState);
    }

    @NonNull
    public VideoInformation synced() {
        return new VideoInformation(id, key, workoutId, workoutKey, username, totalTime, timestamp,
            description, location, url, thumbnailUrl, width, height, fileName, thumbnailFileName,
            false, reviewState);
    }

    @Override
    public int hashCode() {
        int result;
        result = id;
        result = 31 * result + (key != null ? key.hashCode() : 0);
        result = 31 * result + (workoutId != null ? workoutId.hashCode() : 0);
        result = 31 * result + (workoutKey != null ? workoutKey.hashCode() : 0);
        result = 31 * result + username.hashCode();
        result = 31 * result + Long.hashCode(totalTime);
        result = 31 * result + Long.hashCode(timestamp);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (location != null ? location.hashCode() : 0);
        result = 31 * result + (url != null ? url.hashCode() : 0);
        result = 31 * result + (thumbnailUrl != null ? thumbnailUrl.hashCode() : 0);
        result = 31 * result + width;
        result = 31 * result + height;
        result = 31 * result + (fileName != null ? fileName.hashCode() : 0);
        result = 31 * result + (thumbnailFileName != null ? thumbnailFileName.hashCode() : 0);
        result = 31 * result + (locallyChanged ? 1 : 0);
        result = 31 * result + (reviewState != null ? reviewState.hashCode() : 0);
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VideoInformation that = (VideoInformation) o;

        if (id != that.id) return false;
        if (!Objects.equals(key, that.key)) return false;
        if (!Objects.equals(workoutId, that.workoutId)) return false;
        if (!Objects.equals(workoutKey, that.workoutKey)) return false;
        if (!username.equals(that.username)) return false;
        if (totalTime != that.totalTime) return false;
        if (timestamp != that.timestamp) return false;
        if (!Objects.equals(description, that.description)) return false;
        if (!Objects.equals(location, that.location)) return false;
        if (!Objects.equals(url, that.url)) return false;
        if (!Objects.equals(thumbnailUrl, that.thumbnailUrl)) return false;
        if (width != that.width) return false;
        if (height != that.height) return false;
        if (!Objects.equals(fileName, that.fileName)) return false;
        if (!Objects.equals(thumbnailFileName, that.thumbnailFileName)) return false;
        if (locallyChanged != that.locallyChanged) return false;
        if (reviewState != that.reviewState) return false;
        return true;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(key);
        dest.writeValue(workoutId);
        dest.writeString(workoutKey);
        dest.writeString(username);
        dest.writeLong(totalTime);
        dest.writeLong(timestamp);
        dest.writeString(description);
        dest.writeParcelable(location, flags);
        dest.writeString(url);
        dest.writeString(thumbnailUrl);
        dest.writeInt(width);
        dest.writeInt(height);
        dest.writeString(fileName);
        dest.writeString(thumbnailFileName);
        dest.writeByte((byte) (locallyChanged ? 1 : 0));
        dest.writeParcelable(reviewState, flags);
    }

    public static final Parcelable.Creator<VideoInformation> CREATOR =
        new Creator<VideoInformation>() {
            @Override
            public VideoInformation[] newArray(int size) {
                return new VideoInformation[size];
            }

            @Override
            public VideoInformation createFromParcel(Parcel source) {
                int id = source.readInt();
                String key = source.readString();
                Integer workoutId = (Integer) source.readValue(Integer.class.getClassLoader());
                String workoutKey = source.readString();
                String username = source.readString();
                long totalTime = source.readLong();
                long timestamp = source.readLong();
                String description = source.readString();
                Point location = source.readParcelable(Point.class.getClassLoader());
                String url = source.readString();
                String thumbnailUrl = source.readString();
                int width = source.readInt();
                int height = source.readInt();
                String fileName = source.readString();
                String thumbnailFileName = source.readString();
                boolean locallyChanged = source.readByte() == 1;
                ReviewState reviewState = source.readParcelable(ReviewState.class.getClassLoader());
                return new VideoInformation(id, key, workoutId, workoutKey, username, totalTime,
                    timestamp, description, location, url, thumbnailUrl, width, height, fileName,
                    thumbnailFileName, locallyChanged, reviewState);
            }
        };
}
