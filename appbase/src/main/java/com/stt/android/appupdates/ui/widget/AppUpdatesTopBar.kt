package com.stt.android.appupdates.ui.widget

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.stt.android.compose.component.SuuntoTopBar

@Composable
internal fun AppUpdatesTopBar(
    titleText: String,
    onBackPressed: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val elevation = with(LocalDensity.current) { 2.dp.toPx() }
    SuuntoTopBar(
        title = titleText,
        onNavigationClick = onBackPressed,
        modifier = modifier
            .fillMaxWidth()
            .graphicsLayer(shadowElevation = elevation),
    )
}
