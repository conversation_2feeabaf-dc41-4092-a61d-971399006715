package com.stt.android.premium

import android.view.ViewGroup
import androidx.activity.ComponentActivity
import androidx.annotation.MainThread
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.asFlow
import com.stt.android.domain.user.subscription.IsSubscribedToPremiumUseCase
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import javax.inject.Inject

/**
 * Helper to add [BuyPremiumToGetAccessView] to View hierarchy to block access to screens that
 * require premium subscription to access in Sports Tracker. In Suunto App the interface is by
 * default bound to a no-op implementation so it is ok to use without checking for app flavor first.
 *
 * In cases where you have multiple different reasons with different descriptions that need to be
 * checked at the same time, call [startCheckingForPremiumAccess] once for each of the reasons
 * with different descriptions and premiumRequired params.
 *
 * With Compose instead of using this Handler you should check for access rights
 * in the ViewModel (or other stateholder that lives through recompositions) with
 * [IsSubscribedToPremiumUseCase], observe that as state,
 * and then render [BuyPremiumToGetAccess] as needed.
 */
interface PremiumRequiredToAccessHandler {
    /**
     * Call sometime during onCreate, at least before calling [startCheckingForPremiumAccess]
     */
    fun onCreate(activity: ComponentActivity)

    /**
     * Call sometime during onCreate, at least before calling [startCheckingForPremiumAccess]
     */
    fun onCreate(fragment: Fragment)

    /**
     * @param containerView - If the screen isn't based on a ScrollView, the root of the screen.
     *   Screens using ScrollView need to be reorganized by giving a parent to the ScrollView and
     *   giving it a sibling that can be used as the [containerView]. This is needed to size &
     *   position the [BuyPremiumToGetAccessView] correctly and block scrolling by swiping.
     * @param premiumRequired - A secondary condition needed for the popup to be shown in addition
     *   to not having premium.
     * @param onCloseClickListener - Signals that whatever is causing [premiumRequired] to emit
     *   true should be cleared and make it emit false. Does not automatically close the popup,
     *   that'll happen as a reaction to the false from [premiumRequired].
     *   If user can't close the popup in ways other than buying premium, prefer the overloads
     *   with just three or four parameters. If you have a valid use case where you need to control
     *   [premiumRequired] but not allow user to close the popup, pass an empty lambda and
     *   keep both [closeFromButton] and [closeFromTouchOutside] false.
     */
    @MainThread
    fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        description: String,
        premiumRequired: Flow<Boolean>,
        onCloseClickListener: () -> Unit,
        analyticsSource: String,
        analyticsReason: String? = null,
        closeFromButton: Boolean = false,
        closeFromTouchOutside: Boolean = false,
        closeFromDeclineButton: Boolean = false,
        accessStateChangeListener: ((hasAccess: Boolean) -> Unit)? = null
    )

    /**
     * @param containerView - If the screen isn't based on a ScrollView, the root of the screen.
     *   Screens using ScrollView need to be reorganized by giving a parent to the ScrollView and
     *   giving it a sibling that can be used as the [containerView]. This is needed to size &
     *   position the [BuyPremiumToGetAccessView] correctly and block scrolling by swiping.
     * @param premiumRequired - A secondary condition needed for the popup to be shown in addition
     *   to not having premium.
     * @param onCloseClickListener - Signals that whatever is causing [premiumRequired] to emit
     *   true should be cleared and make it emit false. Does not automatically close the popup,
     *   that'll happen as a reaction to the false from [premiumRequired].
     *   If user can't close the popup in ways other than buying premium, prefer the overloads
     *   with just three or four parameters. If you have a valid use case where you need to control
     *   [premiumRequired] but not allow user to close the popup, pass an empty lambda and
     *   keep both [closeFromButton] and [closeFromTouchOutside] false.
     */
    @MainThread
    fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        description: String,
        premiumRequired: LiveData<Boolean>,
        onCloseClickListener: () -> Unit,
        analyticsSource: String,
        analyticsReason: String? = null,
        closeFromButton: Boolean = false,
        closeFromTouchOutside: Boolean = false,
        closeFromDeclineButton: Boolean = false,
        accessStateChangeListener: ((hasAccess: Boolean) -> Unit)? = null
    ) = startCheckingForPremiumAccess(
        viewLifecycleOwner = viewLifecycleOwner,
        containerView = containerView,
        description = description,
        premiumRequired = premiumRequired.asFlow(),
        analyticsSource = analyticsSource,
        analyticsReason = analyticsReason,
        closeFromButton = closeFromButton,
        closeFromTouchOutside = closeFromTouchOutside,
        onCloseClickListener = onCloseClickListener,
        closeFromDeclineButton = closeFromDeclineButton,
        accessStateChangeListener = accessStateChangeListener
    )

    /**
     * Explicitly defined overload for Java to use when user can't close the popup and only
     * condition to show it is not having premium.
     *
     * @param containerView - If the screen isn't based on a ScrollView, the root of the screen.
     *   Screens using ScrollView need to be reorganized by giving a parent to the ScrollView and
     *   giving it a sibling that can be used as the [containerView]. This is needed to size &
     *   position the [BuyPremiumToGetAccessView] correctly and block scrolling by swiping.
     */
    @MainThread
    fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        description: String,
        analyticsSource: String,
        analyticsReason: String?,
    ) = startCheckingForPremiumAccess(
        viewLifecycleOwner = viewLifecycleOwner,
        containerView = containerView,
        description = description,
        analyticsSource = analyticsSource,
        analyticsReason = analyticsReason,
        premiumRequired = flowOf(true),
        onCloseClickListener = { /* do nothing */ },
        closeFromButton = false,
        closeFromTouchOutside = false,
        accessStateChangeListener = null
    )

    /**
     * Explicitly defined overload that can be used add [accessStateChangeListener] without
     * dummy premiumRequired and onCloseClickListener
     *
     * @param containerView - If the screen isn't based on a ScrollView, the root of the screen.
     *   Screens using ScrollView need to be reorganized by giving a parent to the ScrollView and
     *   giving it a sibling that can be used as the [containerView]. This is needed to size &
     *   position the [BuyPremiumToGetAccessView] correctly and block scrolling by swiping.
     */
    @MainThread
    fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        description: String,
        analyticsSource: String,
        analyticsReason: String?,
        accessStateChangeListener: ((hasAccess: Boolean) -> Unit)
    ) = startCheckingForPremiumAccess(
        viewLifecycleOwner = viewLifecycleOwner,
        containerView = containerView,
        description = description,
        premiumRequired = flowOf(true),
        analyticsSource = analyticsSource,
        analyticsReason = analyticsReason,
        onCloseClickListener = { /* do nothing */ },
        closeFromButton = false,
        closeFromTouchOutside = false,
        accessStateChangeListener = accessStateChangeListener
    )
}

class PremiumRequiredToAccessHandlerNoOp @Inject constructor() : PremiumRequiredToAccessHandler {
    override fun onCreate(activity: ComponentActivity) {
        // Do nothing
    }

    override fun onCreate(fragment: Fragment) {
        // Do nothing
    }

    override fun startCheckingForPremiumAccess(
        viewLifecycleOwner: LifecycleOwner,
        containerView: ViewGroup,
        description: String,
        premiumRequired: Flow<Boolean>,
        onCloseClickListener: () -> Unit,
        analyticsSource: String,
        analyticsReason: String?,
        closeFromButton: Boolean,
        closeFromTouchOutside: Boolean,
        closeFromDeclineButton: Boolean,
        accessStateChangeListener: ((hasAccess: Boolean) -> Unit)?
    ) {
        accessStateChangeListener?.invoke(true)
    }
}
