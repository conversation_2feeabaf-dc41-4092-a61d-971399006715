package com.stt.android.menstrualcycle.settings

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ModalBottomSheetState
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.stt.android.R
import com.stt.android.compose.theme.body
import com.stt.android.compose.theme.bodyBold
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.bodyLargeBold
import com.stt.android.compose.theme.bodyXLarge
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.compose.widgets.WheelPickerWithoutLoop
import com.stt.android.data.usersettings.MenstrualCycleSettings
import com.stt.android.menstrualcycle.domain.LengthConstants.CYCLE_LENGTH_SELECTABLE
import com.stt.android.menstrualcycle.domain.LengthConstants.DEFAULT_CYCLE_LENGTH
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_SELECTABLE
import com.stt.android.menstrualcycle.settings.MenstrualCycleSettingsViewModel.Companion.getCycleLengthSelectedIndex
import com.stt.android.menstrualcycle.settings.MenstrualCycleSettingsViewModel.Companion.getPeriodDurationSelectedIndex
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.util.Locale
import kotlin.math.min
import com.stt.android.core.R as CR

enum class SettingsEditType {
    CYCLE_LENGTH,
    PERIOD_DURATION,
    STOP_TRACKING,
    UNKNOWN
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SettingsEditSheetContent(
    settingsEditSheetState: ModalBottomSheetState,
    coroutineScope: CoroutineScope,
    settingsEditType: SettingsEditType,
    menstrualCycleSettings: MenstrualCycleSettings,
    onCycleLengthChanged: (Int) -> Unit,
    onPeriodDurationChanged: (Int) -> Unit,
    onTrackingStopped: () -> Unit,
    averageCycleLength: Int?,
    averagePeriodLength: Int?,
    modifier: Modifier = Modifier
) {
    fun hideEditSheet() = coroutineScope.launch { settingsEditSheetState.hide() }

    when (settingsEditType) {
        SettingsEditType.CYCLE_LENGTH -> {
            SelectDays(
                title = stringResource(id = CR.string.cycle_length).uppercase(Locale.getDefault()),
                description = stringResource(id = R.string.settings_menstrual_cycle_cycle_length_edit_description),
                subDescription = averageCycleLength?.let {
                    stringResource(id = R.string.settings_menstrual_cycle_average_cycle_length, it)
                },
                value = menstrualCycleSettings.cycleLength ?: DEFAULT_CYCLE_LENGTH,
                selectableList = CYCLE_LENGTH_SELECTABLE,
                selectedIndex = getCycleLengthSelectedIndex(menstrualCycleSettings.cycleLength),
                onDone = {
                    hideEditSheet()
                    onCycleLengthChanged(it)
                },
                onCancel = { hideEditSheet() },
                modifier = modifier
            )
        }

        SettingsEditType.PERIOD_DURATION -> {
            SelectDays(
                title = stringResource(id = CR.string.period_length).uppercase(Locale.getDefault()),
                description = stringResource(id = R.string.settings_menstrual_cycle_period_length_edit_description),
                subDescription = averagePeriodLength?.let {
                    stringResource(id = R.string.settings_menstrual_cycle_average_period_length, it)
                },
                value = menstrualCycleSettings.periodDuration,
                selectableList = PERIOD_DURATION_SELECTABLE,
                selectedIndex = getPeriodDurationSelectedIndex(menstrualCycleSettings.periodDuration),
                onDone = {
                    hideEditSheet()
                    onPeriodDurationChanged(it)
                },
                onCancel = { hideEditSheet() },
                modifier = modifier
            )
        }

        SettingsEditType.STOP_TRACKING -> {
            StopTracking(
                onStopTracking = {
                    hideEditSheet()
                    onTrackingStopped()
                },
                onCancel = { hideEditSheet() },
                modifier = modifier
            )
        }

        else -> {
            // Will not come here
        }
    }
}

@Composable
private fun SelectDays(
    title: String,
    description: String,
    subDescription: String?,
    value: Int,
    selectableList: List<Int>,
    selectedIndex: Int,
    onDone: (Int) -> Unit,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedValue by remember {
        mutableIntStateOf(value)
    }
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.bodyLargeBold,
            modifier = Modifier.padding(MaterialTheme.spacing.small)
        )

        Text(
            text = description,
            style = MaterialTheme.typography.body,
            modifier = Modifier
                .padding(MaterialTheme.spacing.medium)
                .fillMaxWidth()
        )

        subDescription?.let {
            Text(
                text = it,
                style = MaterialTheme.typography.body,
                modifier = Modifier
                    .padding(
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.medium,
                    )
                    .fillMaxWidth()
            )
        }

        ConstraintLayout(
            modifier = Modifier
                .height(216.dp)
                .fillMaxWidth()
        ) {
            val (marker, wheel, unit) = createRefs()

            Spacer(
                Modifier
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .height(34.dp)
                    .fillMaxWidth()
                    .background(
                        colorResource(R.color.wheel_picker_marker),
                        RoundedCornerShape(6.dp)
                    )
                    .constrainAs(marker) {
                        top.linkTo(wheel.top)
                        bottom.linkTo(wheel.bottom)
                    }
            )

            WheelPickerWithoutLoop(
                data = selectableList,
                selectIndex = selectedIndex,
                visibleCount = min(selectableList.size, 7),
                modifier = Modifier
                    .constrainAs(wheel) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    },
                onSelect = { _, item ->
                    selectedValue = item
                }
            ) {
                Text(
                    text = it.toString(),
                    style = MaterialTheme.typography.bodyXLarge,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            Text(
                text = pluralStringResource(CR.plurals.unit_days, selectedValue),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier
                    .padding(start = 80.dp)
                    .constrainAs(unit) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        top.linkTo(parent.top)
                        bottom.linkTo(parent.bottom)
                    }
            )
        }

        PrimaryButton(
            onClick = { onDone(selectedValue) },
            text = stringResource(id = R.string.done).uppercase(Locale.getDefault()),
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium)
        )

        TextButton(
            onClick = onCancel,
            modifier = Modifier
                .padding(bottom = MaterialTheme.spacing.medium)
        ) {
            Text(
                text = stringResource(id = R.string.cancel).uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary
            )
        }
    }
}

@Composable
private fun StopTracking(
    onStopTracking: () -> Unit,
    onCancel: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.settings_menstrual_cycle_stop_tracking).uppercase(
                Locale.getDefault()
            ),
            style = MaterialTheme.typography.bodyLargeBold,
            modifier = Modifier.padding(MaterialTheme.spacing.small)
        )

        Text(
            text = stringResource(id = R.string.settings_menstrual_cycle_stop_tracking_edit_description),
            style = MaterialTheme.typography.body,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        )

        PrimaryButton(
            onClick = onStopTracking,
            text = stringResource(id = R.string.settings_menstrual_cycle_stop_tracking_title)
                .uppercase(Locale.getDefault()),
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium)
        )

        TextButton(
            onClick = onCancel,
            modifier = Modifier
                .padding(bottom = MaterialTheme.spacing.medium)
        ) {
            Text(
                text = stringResource(id = R.string.cancel).uppercase(Locale.getDefault()),
                style = MaterialTheme.typography.bodyBold,
                color = MaterialTheme.colors.primary
            )
        }
    }
}
