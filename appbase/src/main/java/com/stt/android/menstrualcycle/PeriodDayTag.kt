package com.stt.android.menstrualcycle

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.bodyLarge
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.Chip

@Composable
fun PeriodDayTag(
    onDeletePeriodDay: () -> Unit,
    modifier: Modifier = Modifier
) {
    Chip(
        isSelected = true,
        onChecked = {
            if (!it) onDeletePeriodDay()
        },
        selectedBorderColor = MaterialTheme.colors.lightGrey,
        selectedBackgroundColor = MaterialTheme.colors.lightGrey,
        modifier = modifier.heightIn(32.dp),
        rightIcon = {
            Box(
                modifier = Modifier.padding(end = MaterialTheme.spacing.xsmall),
            ) {
                Icon(
                    painter = SuuntoIcons.ActionCloseCircleFilled.asPainter(),
                    contentDescription = null,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                    tint = MaterialTheme.colors.nearBlack
                )
            }
        },
        content = {
            Text(
                text = stringResource(id = R.string.day_view_period_day),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.small)
            )
        }
    )
}

@Preview(showBackground = true)
@Composable
private fun PeriodDayTagPreview() {
    AppTheme {
        PeriodDayTag(
            onDeletePeriodDay = {}
        )
    }
}
