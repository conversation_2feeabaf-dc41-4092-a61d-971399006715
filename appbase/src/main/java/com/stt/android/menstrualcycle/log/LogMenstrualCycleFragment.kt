package com.stt.android.menstrualcycle.log

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.SelectableDates
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.menstrualcycle.domain.LengthConstants.DEFAULT_PERIOD_DURATION
import com.stt.android.menstrualcycle.domain.LengthConstants.PERIOD_DURATION_SELECTABLE
import com.stt.android.ui.utils.ExpandedBottomSheetDialogFragment
import com.stt.android.utils.atEndOfDay
import com.stt.android.utils.toEpochMilli
import java.time.Instant
import java.time.LocalDate
import java.time.Year
import java.time.ZoneOffset

interface OnLogMenstrualCycleDoneListener {

    fun onDone(startDate: LocalDate, endDate: LocalDate)
}

class LogMenstrualCycleFragment : ExpandedBottomSheetDialogFragment() {

    var onLogMenstrualCycleDoneListener: OnLogMenstrualCycleDoneListener? = null

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ) = ComposeView(requireContext()).apply {
        setViewCompositionStrategy(
            ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
        )

        val initDate = arguments?.getSerializable(DATE_PICKER_INIT_DATE) as? LocalDate
        var duration = requireNotNull(arguments?.getInt(PERIOD_DURATION))
        if (!PERIOD_DURATION_SELECTABLE.contains(duration)) {
            duration = DEFAULT_PERIOD_DURATION
        }

        setContentWithTheme {
            var periodDuration by rememberSaveable { mutableIntStateOf(duration) }
            val datePickerState = rememberDatePickerState(
                initialSelectedDateMillis = initDate?.atStartOfDay(ZoneOffset.UTC)?.toInstant()
                    ?.toEpochMilli(),
                yearRange = IntRange(2010, Year.now().value),
                selectableDates = object : SelectableDates {
                    val todayDateMilli = LocalDate.now().atEndOfDay().toEpochMilli()
                    val oldestDateMilli = LocalDate.of(2010, 1, 1).atStartOfDay().toEpochMilli()
                    override fun isSelectableDate(utcTimeMillis: Long): Boolean {
                        return utcTimeMillis in oldestDateMilli..<todayDateMilli
                    }

                    override fun isSelectableYear(year: Int): Boolean {
                        return year >= 2010
                    }
                }
            )

            LogMenstrualCycleContent(
                datePickerState = datePickerState,
                periodDuration = periodDuration,
                onPeriodDurationChange = { periodDuration = it },
                onDoneClick = {
                    val starDate = datePickerState.selectedDateMillis?.let {
                        LocalDate.ofInstant(Instant.ofEpochMilli(it), ZoneOffset.UTC)
                    } ?: LocalDate.now()
                    val endDate = starDate.plusDays(periodDuration - 1L)
                    onLogMenstrualCycleDoneListener?.onDone(starDate, endDate)
                    dismiss()
                },
                onCancelClick = { dismiss() },
                onWheelScrollInProgressChanged = { isScrollInProgress ->
                    setBottomSheetDraggable(!isScrollInProgress)
                }
            )
        }
    }

    companion object {

        private const val DATE_PICKER_INIT_DATE = "DATE_PICKER_INIT_DATE"
        private const val PERIOD_DURATION = "PERIOD_DURATION"

        fun create(
            initDate: LocalDate,
            periodDuration: Int,
            onLogMenstrualCycleDoneListener: OnLogMenstrualCycleDoneListener
        ) = LogMenstrualCycleFragment().apply {
            arguments = Bundle().apply {
                putSerializable(DATE_PICKER_INIT_DATE, initDate)
                putInt(PERIOD_DURATION, periodDuration)
            }
            this.onLogMenstrualCycleDoneListener = onLogMenstrualCycleDoneListener
        }
    }
}
