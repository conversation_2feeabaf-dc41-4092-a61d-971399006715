package com.stt.android.featuretoggle

import com.stt.android.common.viewstate.ViewState
import com.stt.android.common.viewstate.ViewStateEpoxyController
import com.stt.android.featureToggleItem
import javax.inject.Inject

/**
 * An Epoxy controller that takes care of laying out feature toggle items in a RecyclerView.
 */
class FeatureToggleController
@Inject constructor() : ViewStateEpoxyController<FeatureItemContainer?>() {
    override fun buildModels(viewState: ViewState<FeatureItemContainer?>) {
        featureToggleBaseUrl {
            id("featureToggleBaseUrl")
            selectedConfiguration(viewState.data?.selectedBaseUrlConfiguration)
            onSelectConfiguration(viewState.data?.onSelectBaseUrlConfiguration)
        }

        featureToggleGraphhopperBaseUrl {
            id("featureToggleGraphhopperBaseUrl")
            baseUrls(viewState.data?.graphhopperBaseUrls)
            selectedBaseUrl(viewState.data?.selectedGraphhopperBaseUrl)
            onBaseUrlSelected(viewState.data?.onGraphhopperBaseUrlSelected)
        }

        // Here we simply loop through the data that is passed by ViewStateEpoxyController.setData().
        // If you are extending ViewStateListFragment, this call is handled automatically for you
        // based on the ViewState. The data can be of any type we want. It's pure data and is not
        // associated to Epoxy in any way.
        // The order we "add" the Epoxy models here will dictate the order they will appear in the RecyclerView.
        // Read on for info about adding Epoxy models.
        viewState.data?.list?.forEach { item ->

            // `featureToggleItem` is an extension method generated by Epoxy.
            // It is generated for each layout xml file that is data binding enabled and starts with `viewholder_`.
            // By calling this generated extension method we tell Epoxy to "add" a model to the model list,
            // which it will then use in the RecyclerView.Adapter. This is where we bind the pure data passed
            // to this method to its Epoxy model. If we navigate to the source code of the generated code,
            // we will see that under the hood, Epoxy generated a model class called `FeatureToggleItemBindingModel_`
            // in this case. Note that we DO NOT interact with these generated models directly.
            // The generated extension methods will handle that for us.

            // Each variable we define in the data binding xml will be available here.
            // In this case, we have two variables:
            // * `item` - variable of type FeatureItem
            // * `onCheckChanged` - variable of type CompoundButton.OnCheckedChangeListener. Note that you *must*
            // always use Android's Framework own widget listeners as data binding variable in XML. The reason
            // for that is due to the way Epoxy generated code handles the diff. When it sees Android Framework
            // listeners it ignores them in the comparision.
            featureToggleItem {
                id(item.key) // ID must be unique for proper diffing when the models are updated
                item(item)

                // model - the epoxy model instance associated with this listener in runtime.
                // isChecked - boolean provided by CompoundButton.OnCheckedChangeListener indicating whether the
                // switch is toggled or not.
                onCheckChanged { model, _, _, isChecked, _ ->
                    // Our container exposes listeners that are used for all items. These listeners can be of
                    // any type we want. We take the associated model and get the key.
                    // *Important thing to notice is that we *must* use the passed in `model` instead of
                    // referencing `this@featureToggleItem` when we're inside a listener block. The reason
                    // for that is because we do not know exactly which model would be associated to which
                    // listener in runtime.
                    val data = viewState.data
                    when {
                        model.item().requireDeviceReboot -> {
                            data.saveAndRequestPhoneRebootListener(model.item().key, isChecked)
                        }

                        model.item().requireProcessKill -> {
                            data.saveAndKillProcessListener(model.item().key, isChecked)
                        }

                        else -> {
                            data.saveOnlyListener(model.item().key, isChecked)
                        }
                    }
                }
            }
        }

        // Calling the super method is optional, but recommended.
        // By calling it, Epoxy will automatically add a loading spinner when the viewState is loading.
        // The loading spinner will stretch to fit the RecyclerView if there's no data
        // so that it appears at the center. Otherwise, it will be added as an item in the RecyclerView.
        super.buildModels(viewState)
    }
}
