#Don't touch suunto stuff:
-keep class com.suunto.** {*;}
-keep interface com.suunto.** {*;}
-keep enum com.suunto.** {*;}
-keep interface com.stt.android.data.sportmodes.component.** { *; }
-keep class com.squareup.duktape.** { *; }

# Prevent weird missing class error
# Missing class com.sun.activation.registries.MailcapFile (referenced from: com.sun.activation.registries.MailcapFile[] javax.activation.MailcapCommandMap.DB and 5 other contexts)
# Missing class java.awt.datatransfer.DataFlavor (referenced from: java.awt.datatransfer.DataFlavor[] javax.activation.DataHandler.emptyFlavors and 4 other contexts)
# Missing class java.awt.datatransfer.Transferable (referenced from: javax.activation.DataHandler)
# Missing class com.sun.activation.registries.LogSupport (referenced from: void javax.activation.MailcapCommandMap.<init>() and 4 other contexts)
-dontwarn com.sun.activation.registries.MailcapFile
-dontwarn java.awt.datatransfer.DataFlavor
-dontwarn java.awt.datatransfer.Transferable
-dontwarn com.sun.activation.registries.LogSupport
-dontwarn com.polidea.rxandroidble2.** # used by default com.movesense.mds.internal.connectivity.BLEManager implementation in obi2, which is not used by our app

# todo these missing classes errors are from tencent SDK, should probably be investigated, they might break Huawei/Oppo integrations
-dontwarn com.huawei.hms.push.HmsMessageService
-dontwarn org.apache.commons.codec.binary.StringUtils
